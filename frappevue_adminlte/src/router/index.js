import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import About from '../views/About.vue'
import Login from '@/views/authentication/Login.vue'
import Profile from '@/views/Profile.vue'
import Invoice from '@/views/transactions/Invoice.vue'
import InvoiceDetail from '@/views/transactions/InvoiceDetail.vue'
// New modern theme imports
import Dashboard from '../views/Dashboard.vue'
import ModernLogin from '../views/Login.vue'
import DocList from '../views/DocList.vue'
import DocForm from '../views/DocForm.vue'

const routes = [
  { path: '/', name: 'Home', component: Home, meta: { requiresAuth: true }, },
  { path: '/dashboard', name: 'Dashboard', component: Dashboard, meta: { requiresAuth: true }, },
  { path: '/about', name: 'About', component: About },
  { path: '/login', name: '<PERSON><PERSON>', component: Login },
  { path: '/modern-login', name: '<PERSON><PERSON>ogin', component: ModernLogin },
  { path: '/profile', name: 'Profile', component: Profile },
  { path: '/my-invoices', name: 'Invoice', component: Invoice },
  { path: '/my-invoices/:name', name: 'InvoiceDetail', component: InvoiceDetail },
  { path: '/doctype/:doctype', name: 'DocList', component: DocList, props: true, meta: { requiresAuth: true }, },
  { path: '/doctype/:doctype/:name', name: 'DocForm', component: DocForm, props: true, meta: { requiresAuth: true }, },
  { path: '/:pathMatch(.*)*', redirect: '/' }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

let getToken = () => {
  try {
    return JSON.parse(localStorage.frappUser).token;
  } catch (e) {
    return null
  }
}

router.beforeEach((to, from, next) => {
  // alert(to.fullPath)
  if (!['/login', '/register'].includes(to.fullPath)) {
    if (!getToken()) {
      next('/login');
    }
  }
  //
  if (to.fullPath === '/login') {
    if (getToken()) {
      next('/');
    }
  }
  next();
});

export default router
