# -*- coding: utf-8 -*-
from setuptools import setup, find_packages

with open("requirements.txt") as f:
	install_requires = f.read().strip().split("\n")

setup(
	name="erpsmart_car",
	version="1.0.0",
	description="نظام إدارة معرض السيارات المتكامل مع ERPNext",
	author="NewsMART",
	author_email="<EMAIL>",
	packages=find_packages(),
	zip_safe=False,
	include_package_data=True,
	install_requires=install_requires
)