# -*- coding: utf-8 -*-
# Copyright (c) 2024, NewsMART and contributors
# Dashboard API for Car Showroom

from __future__ import unicode_literals
import frappe
from frappe.utils import flt

@frappe.whitelist()
def get_car_stats():
	"""احصائيات لوحة معلومات السيارات"""
	stats = {}
	
	# إجمالي السيارات
	stats['total_cars'] = frappe.db.count('Car Record')
	
	# السيارات المتاحة
	stats['available_cars'] = frappe.db.count('Car Record', {'status': 'متاحة'})
	
	# السيارات المباعة
	stats['sold_cars'] = frappe.db.count('Car Record', {'status': 'مباعة'})
	
	# السيارات المحجوزة
	stats['reserved_cars'] = frappe.db.count('Car Record', {'status': 'محجوزة'})
	
	# القيمة الإجمالية للمخزون
	total_value = frappe.db.sql("""
		SELECT SUM(selling_price) 
		FROM `tabCar Record` 
		WHERE status IN ('متاحة', 'محجوزة')
	""")[0][0] or 0
	
	stats['total_value'] = flt(total_value)
	
	# متوسط سعر السيارات
	avg_price = frappe.db.sql("""
		SELECT AVG(selling_price) 
		FROM `tabCar Record` 
		WHERE selling_price > 0
	""")[0][0] or 0
	
	stats['average_price'] = flt(avg_price)
	
	# أحدث السيارات المضافة
	latest_cars = frappe.db.sql("""
		SELECT name, car_name, brand, model, year, selling_price
		FROM `tabCar Record`
		ORDER BY creation DESC
		LIMIT 5
	""", as_dict=True)
	
	stats['latest_cars'] = latest_cars
	
	# أفضل الماركات مبيعاً
	top_brands = frappe.db.sql("""
		SELECT brand, COUNT(*) as sold_count
		FROM `tabCar Record`
		WHERE status = 'مباعة'
		GROUP BY brand
		ORDER BY sold_count DESC
		LIMIT 5
	""", as_dict=True)
	
	stats['top_brands'] = top_brands
	
	return stats

@frappe.whitelist()
def get_monthly_sales():
	"""احصائيات المبيعات الشهرية"""
	monthly_sales = frappe.db.sql("""
		SELECT 
			MONTH(sale_date) as month,
			YEAR(sale_date) as year,
			COUNT(*) as cars_sold,
			SUM(selling_price) as total_revenue
		FROM `tabCar Record`
		WHERE status = 'مباعة' AND sale_date IS NOT NULL
		GROUP BY YEAR(sale_date), MONTH(sale_date)
		ORDER BY year DESC, month DESC
		LIMIT 12
	""", as_dict=True)
	
	return monthly_sales

@frappe.whitelist()
def get_inventory_summary():
	"""ملخص المخزون"""
	inventory = {}
	
	# حسب الحالة
	status_summary = frappe.db.sql("""
		SELECT status, COUNT(*) as count, SUM(selling_price) as value
		FROM `tabCar Record`
		GROUP BY status
	""", as_dict=True)
	
	inventory['by_status'] = status_summary
	
	# حسب الماركة
	brand_summary = frappe.db.sql("""
		SELECT brand, COUNT(*) as count, AVG(selling_price) as avg_price
		FROM `tabCar Record`
		WHERE status IN ('متاحة', 'محجوزة')
		GROUP BY brand
		ORDER BY count DESC
	""", as_dict=True)
	
	inventory['by_brand'] = brand_summary
	
	# حسب سنة الصنع
	year_summary = frappe.db.sql("""
		SELECT year, COUNT(*) as count
		FROM `tabCar Record`
		WHERE status IN ('متاحة', 'محجوزة')
		GROUP BY year
		ORDER BY year DESC
	""", as_dict=True)
	
	inventory['by_year'] = year_summary
	
	return inventory

@frappe.whitelist()
def get_profit_analysis():
	"""تحليل الأرباح"""
	profit_data = frappe.db.sql("""
		SELECT 
			name,
			car_name,
			brand,
			purchase_price,
			selling_price,
			(selling_price - purchase_price) as profit,
			CASE 
				WHEN purchase_price > 0 THEN ((selling_price - purchase_price) / purchase_price) * 100
				ELSE 0
			END as profit_margin
		FROM `tabCar Record`
		WHERE status = 'مباعة' 
		AND purchase_price > 0 
		AND selling_price > 0
		ORDER BY profit DESC
	""", as_dict=True)
	
	# إحصائيات الربح
	total_profit = sum([car.profit for car in profit_data])
	avg_profit_margin = sum([car.profit_margin for car in profit_data]) / len(profit_data) if profit_data else 0
	
	return {
		'cars': profit_data,
		'total_profit': total_profit,
		'average_profit_margin': avg_profit_margin,
		'highest_profit_car': profit_data[0] if profit_data else None
	}

@frappe.whitelist()
def get_luxury_cars():
	"""قائمة السيارات الفاخرة"""
	luxury_brands = ['مرسيدس', 'BMW', 'أودي', 'لكزس', 'جاكوار', 'بورش', 'فيراري', 'لامبورغيني']
	
	luxury_cars = frappe.db.sql("""
		SELECT *
		FROM `tabCar Record`
		WHERE brand IN ({brands})
		AND status IN ('متاحة', 'محجوزة')
		ORDER BY selling_price DESC
	""".format(brands=', '.join(['%s'] * len(luxury_brands))), luxury_brands, as_dict=True)
	
	return luxury_cars