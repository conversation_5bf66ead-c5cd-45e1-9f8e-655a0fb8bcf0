# -*- coding: utf-8 -*-
# Copyright (c) 2024, NewsMART and contributors
# Installation script for ERP Smart Car

from __future__ import unicode_literals
import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields

def after_install():
	"""تنفيذ بعد تثبيت التطبيق"""
	setup_custom_fields()
	create_car_item_group()
	create_default_car_brands()
	setup_naming_series()
	frappe.db.commit()

def setup_custom_fields():
	"""إضافة حقول مخصصة لتوثيق الفواتير والعروض مع السيارات"""
	
	custom_fields = {
		"Sales Invoice": [
			{
				"fieldname": "car_record",
				"label": "سجل السيارة",
				"fieldtype": "Link",
				"options": "Car Record",
				"insert_after": "customer",
				"read_only": 1
			}
		],
		"Quotation": [
			{
				"fieldname": "car_record", 
				"label": "سجل السيارة",
				"fieldtype": "Link",
				"options": "Car Record",
				"insert_after": "party_name"
			}
		],
		"Customer": [
			{
				"fieldname": "preferred_car_brands",
				"label": "الماركات المفضلة",
				"fieldtype": "Table",
				"options": "Customer Car Preference",
				"insert_after": "customer_details"
			},
			{
				"fieldname": "car_budget_range",
				"label": "نطاق الميزانية",
				"fieldtype": "Data",
				"insert_after": "preferred_car_brands"
			}
		]
	}
	
	create_custom_fields(custom_fields)

def create_car_item_group():
	"""إنشاء مجموعة أصناف السيارات"""
	if not frappe.db.exists("Item Group", "سيارات"):
		item_group = frappe.new_doc("Item Group")
		item_group.item_group_name = "سيارات"
		item_group.parent_item_group = "جميع مجموعات الأصناف"
		item_group.is_group = 0
		item_group.insert(ignore_permissions=True)

def create_default_car_brands():
	"""إنشاء ماركات السيارات الافتراضية"""
	brands = [
		{"brand_name": "تويوتا", "country_of_origin": "اليابان", "brand_category": "متوسطة"},
		{"brand_name": "هوندا", "country_of_origin": "اليابان", "brand_category": "متوسطة"},
		{"brand_name": "نيسان", "country_of_origin": "اليابان", "brand_category": "متوسطة"},
		{"brand_name": "مرسيدس", "country_of_origin": "ألمانيا", "brand_category": "فاخرة", "is_luxury_brand": 1},
		{"brand_name": "BMW", "country_of_origin": "ألمانيا", "brand_category": "فاخرة", "is_luxury_brand": 1},
		{"brand_name": "أودي", "country_of_origin": "ألمانيا", "brand_category": "فاخرة", "is_luxury_brand": 1},
		{"brand_name": "لكزس", "country_of_origin": "اليابان", "brand_category": "فاخرة", "is_luxury_brand": 1},
		{"brand_name": "هيونداي", "country_of_origin": "كوريا الجنوبية", "brand_category": "اقتصادية"},
		{"brand_name": "كيا", "country_of_origin": "كوريا الجنوبية", "brand_category": "اقتصادية"},
		{"brand_name": "فورد", "country_of_origin": "الولايات المتحدة", "brand_category": "متوسطة"},
		{"brand_name": "شيفروليه", "country_of_origin": "الولايات المتحدة", "brand_category": "متوسطة"},
		{"brand_name": "جاكوار", "country_of_origin": "المملكة المتحدة", "brand_category": "فاخرة", "is_luxury_brand": 1},
		{"brand_name": "لاند روفر", "country_of_origin": "المملكة المتحدة", "brand_category": "فاخرة", "is_luxury_brand": 1},
		{"brand_name": "بورش", "country_of_origin": "ألمانيا", "brand_category": "رياضية", "is_luxury_brand": 1},
		{"brand_name": "فيراري", "country_of_origin": "إيطاليا", "brand_category": "رياضية", "is_luxury_brand": 1}
	]
	
	for brand_data in brands:
		if not frappe.db.exists("Car Brand", brand_data["brand_name"]):
			brand = frappe.new_doc("Car Brand")
			brand.update(brand_data)
			brand.insert(ignore_permissions=True)

def setup_naming_series():
	"""إعداد سلاسل الترقيم"""
	# إضافة سلاسل ترقيم للسيارات
	naming_series = [
		"CAR-.YYYY.-",  # للسيارات العادية
		"LUX-.YYYY.-",  # للسيارات الفاخرة  
		"PRM-.YYYY.-"   # للسيارات المميزة
	]
	
	for series in naming_series:
		if not frappe.db.exists("Naming Series", series):
			frappe.db.sql("INSERT INTO `tabNaming Series` (name) VALUES (%s)", series)

def before_uninstall():
	"""تنفيذ قبل إلغاء تثبيت التطبيق"""
	frappe.msgprint("جاري إزالة بيانات تطبيق ERP Smart Car...")