{"align_labels_right": 0, "creation": "2024-12-27 12:00:00.000000", "custom_format": 1, "default_print_language": "ar", "disabled": 0, "doc_type": "Car Record", "docstatus": 0, "doctype": "Print Format", "font_size": 12, "html": "<style>\n.luxury-card {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    padding: 30px;\n    border-radius: 20px;\n    color: white;\n    font-family: 'Arial', sans-serif;\n    margin: 20px;\n    box-shadow: 0 20px 40px rgba(0,0,0,0.3);\n}\n\n.car-header {\n    text-align: center;\n    border-bottom: 3px solid #FFD700;\n    padding-bottom: 20px;\n    margin-bottom: 30px;\n}\n\n.car-title {\n    font-size: 28px;\n    font-weight: bold;\n    margin-bottom: 10px;\n    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n}\n\n.car-subtitle {\n    font-size: 18px;\n    opacity: 0.9;\n}\n\n.car-details {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 30px;\n    margin: 30px 0;\n}\n\n.detail-item {\n    background: rgba(255,255,255,0.1);\n    padding: 15px;\n    border-radius: 10px;\n    border-left: 4px solid #FFD700;\n}\n\n.detail-label {\n    font-weight: bold;\n    color: #FFD700;\n    font-size: 14px;\n    margin-bottom: 5px;\n}\n\n.detail-value {\n    font-size: 16px;\n}\n\n.price-section {\n    text-align: center;\n    background: rgba(255,215,0,0.2);\n    padding: 20px;\n    border-radius: 15px;\n    margin: 20px 0;\n    border: 2px solid #FFD700;\n}\n\n.price-label {\n    font-size: 16px;\n    margin-bottom: 10px;\n}\n\n.price-value {\n    font-size: 32px;\n    font-weight: bold;\n    color: #FFD700;\n    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);\n}\n\n.features-section {\n    margin: 30px 0;\n}\n\n.features-title {\n    font-size: 20px;\n    font-weight: bold;\n    margin-bottom: 15px;\n    color: #FFD700;\n}\n\n.features-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 10px;\n}\n\n.feature-item {\n    background: rgba(255,255,255,0.1);\n    padding: 8px 12px;\n    border-radius: 20px;\n    text-align: center;\n    font-size: 12px;\n}\n\n.footer {\n    text-align: center;\n    margin-top: 30px;\n    padding-top: 20px;\n    border-top: 2px solid #FFD700;\n    opacity: 0.8;\n}\n\n.qr-code {\n    float: right;\n    margin: 10px;\n}\n\n.logo {\n    width: 80px;\n    height: 80px;\n    border-radius: 50%;\n    float: left;\n    margin: 10px;\n}\n\n@media print {\n    .luxury-card {\n        margin: 0;\n        box-shadow: none;\n    }\n}\n</style>\n\n<div class=\"luxury-card\">\n    <div class=\"car-header\">\n        <div class=\"car-title\">{{ doc.car_name }}</div>\n        <div class=\"car-subtitle\">{{ doc.brand }} {{ doc.model }} - {{ doc.year }}</div>\n    </div>\n    \n    <div class=\"car-details\">\n        <div class=\"detail-item\">\n            <div class=\"detail-label\">الماركة</div>\n            <div class=\"detail-value\">{{ doc.brand }}</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">الموديل</div>\n            <div class=\"detail-value\">{{ doc.model }}</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">سنة الصنع</div>\n            <div class=\"detail-value\">{{ doc.year }}</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">اللون</div>\n            <div class=\"detail-value\">{{ doc.color or \"غير محدد\" }}</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">نوع الوقود</div>\n            <div class=\"detail-value\">{{ doc.fuel_type or \"غير محدد\" }}</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">ناقل الحركة</div>\n            <div class=\"detail-value\">{{ doc.transmission or \"غير محدد\" }}</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">الكيلومترات</div>\n            <div class=\"detail-value\">{{ \"{:,}\".format(doc.mileage or 0) }} كم</div>\n        </div>\n        \n        <div class=\"detail-item\">\n            <div class=\"detail-label\">رقم الهيكل</div>\n            <div class=\"detail-value\">{{ doc.vin_number or \"غير متوفر\" }}</div>\n        </div>\n    </div>\n    \n    <div class=\"price-section\">\n        <div class=\"price-label\">السعر</div>\n        <div class=\"price-value\">{{ frappe.format(doc.selling_price, {\"fieldtype\": \"Currency\"}) }}</div>\n    </div>\n    \n    {% if doc.luxury_features %}\n    <div class=\"features-section\">\n        <div class=\"features-title\">الميزات الفاخرة</div>\n        <div class=\"features-grid\">\n            {% for feature in doc.luxury_features %}\n            <div class=\"feature-item\">\n                {{ feature.feature_name }}\n            </div>\n            {% endfor %}\n        </div>\n    </div>\n    {% endif %}\n    \n    <div class=\"footer\">\n        <p>معرض السيارات الفاخرة - ERP Smart Car</p>\n        <p>للاستفسار: {{ frappe.db.get_single_value(\"Company\", \"phone_no\") or \"\" }}</p>\n        <p>تاريخ الطباعة: {{ frappe.utils.formatdate(frappe.utils.nowdate(), \"dd/mm/yyyy\") }}</p>\n    </div>\n</div>", "idx": 0, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Showroom", "name": "Luxury Car Card", "owner": "Administrator", "page_break": 0, "page_number": "<PERSON>de", "print_format_builder": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "No"}