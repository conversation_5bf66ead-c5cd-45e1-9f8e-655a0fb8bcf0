# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from frappe import _

def get_data():
	return [
		{
			"label": _("إدارة السيارات"),
			"icon": "fa fa-car",
			"items": [
				{
					"type": "doctype",
					"name": "Car Record",
					"label": _("سجل السيارات"),
					"description": _("إدارة جميع السيارات في المعرض"),
					"icon": "fa fa-car"
				},
				{
					"type": "doctype", 
					"name": "Car Brand",
					"label": _("ماركات السيارات"),
					"description": _("إدارة ماركات السيارات"),
					"icon": "fa fa-star"
				}
			]
		},
		{
			"label": _("التقارير والتحليلات"),
			"icon": "fa fa-bar-chart",
			"items": [
				{
					"type": "report",
					"name": "Car Sales Analysis",
					"label": _("تحليل مبيعات السيارات"),
					"doctype": "Car Record",
					"is_query_report": True,
					"icon": "fa fa-line-chart"
				},
				{
					"type": "report", 
					"name": "Car Inventory Report",
					"label": _("تقرير مخزون السيارات"),
					"doctype": "Car Record",
					"is_query_report": True,
					"icon": "fa fa-warehouse"
				},
				{
					"type": "report",
					"name": "Luxury Cars Report", 
					"label": _("تقرير السيارات الفاخرة"),
					"doctype": "Car Record",
					"is_query_report": True,
					"icon": "fa fa-diamond"
				}
			]
		},
		{
			"label": _("إعدادات المعرض"),
			"icon": "fa fa-cog",
			"items": [
				{
					"type": "doctype",
					"name": "Car Brand",
					"label": _("إعداد الماركات"),
					"icon": "fa fa-tags"
				}
			]
		}
	]