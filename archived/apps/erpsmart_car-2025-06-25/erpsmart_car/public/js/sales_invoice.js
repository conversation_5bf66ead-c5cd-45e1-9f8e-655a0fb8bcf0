// Custom Sales Invoice enhancements for Car Showroom

frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // Add Car Selection button if not already filled
        if (!frm.doc.car_record && frm.doc.docstatus === 0) {
            frm.add_custom_button(__('اختيار سيارة'), function() {
                show_car_selection_dialog(frm);
            }, __('إضافة'));
        }
        
        // Show car details if car is selected
        if (frm.doc.car_record) {
            show_car_details_banner(frm);
        }
        
        // Add print car invoice button
        if (frm.doc.docstatus === 1 && frm.doc.car_record) {
            frm.add_custom_button(__('طباعة فاتورة السيارة'), function() {
                print_car_invoice(frm);
            }, __('طباعة'));
        }
    },
    
    car_record: function(frm) {
        if (frm.doc.car_record) {
            load_car_details(frm);
        }
    }
});

function show_car_selection_dialog(frm) {
    let d = new frappe.ui.Dialog({
        title: 'اختيار سيارة للبيع',
        fields: [
            {
                label: 'البحث عن السيارة',
                fieldname: 'car_search',
                fieldtype: 'Data',
                description: 'ابحث بالاسم أو الماركة أو الموديل'
            },
            {
                label: 'الماركة',
                fieldname: 'brand_filter',
                fieldtype: 'Link',
                options: 'Car Brand'
            },
            {
                label: 'السنة من',
                fieldname: 'year_from',
                fieldtype: 'Int'
            },
            {
                label: 'السنة إلى', 
                fieldname: 'year_to',
                fieldtype: 'Int'
            },
            {
                label: 'السعر الأقصى',
                fieldname: 'max_price',
                fieldtype: 'Currency'
            },
            {
                fieldtype: 'Section Break'
            },
            {
                fieldname: 'available_cars',
                fieldtype: 'HTML'
            }
        ],
        primary_action_label: 'بحث',
        primary_action: function(values) {
            search_available_cars(d, values);
        }
    });
    
    d.show();
    // Load available cars initially
    search_available_cars(d, {});
}

function search_available_cars(dialog, filters) {
    frappe.call({
        method: 'erpsmart_car.api.car_record.get_available_cars',
        args: {
            filters: filters
        },
        callback: function(r) {
            if (r.message) {
                display_car_grid(dialog, r.message);
            }
        }
    });
}

function display_car_grid(dialog, cars) {
    let html = '<div class="row">';
    
    cars.forEach(function(car) {
        html += `
            <div class="col-md-6 car-selection-card" style="margin-bottom: 15px;">
                <div class="card" style="cursor: pointer; border: 2px solid #ddd; border-radius: 8px;">
                    <div class="card-body" onclick="select_car('${car.name}', '${dialog.dialog_name}')">
                        <h5 class="card-title">${car.car_name}</h5>
                        <p class="card-text">
                            <strong>الماركة:</strong> ${car.brand}<br>
                            <strong>الموديل:</strong> ${car.model}<br>
                            <strong>السنة:</strong> ${car.year}<br>
                            <strong>السعر:</strong> ${format_currency(car.selling_price)}<br>
                            <strong>الكيلومترات:</strong> ${car.mileage || 0} كم
                        </p>
                        <span class="badge badge-success">متاحة</span>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    if (cars.length === 0) {
        html = '<div class="text-center text-muted">لا توجد سيارات متاحة تطابق معايير البحث</div>';
    }
    
    dialog.fields_dict.available_cars.$wrapper.html(html);
}

function select_car(car_name, dialog_name) {
    // Close dialog
    cur_dialog.hide();
    
    // Load car details into invoice
    frappe.call({
        method: 'frappe.client.get',
        args: {
            doctype: 'Car Record',
            name: car_name
        },
        callback: function(r) {
            if (r.message) {
                let car = r.message;
                
                // Set car record
                cur_frm.set_value('car_record', car_name);
                
                // Clear existing items
                cur_frm.clear_table('items');
                
                // Add car as item
                let item_row = cur_frm.add_child('items');
                item_row.item_code = car.item_code;
                item_row.item_name = car.car_name;
                item_row.description = `${car.brand} ${car.model} ${car.year} - ${car.color || ''}`;
                item_row.qty = 1;
                item_row.rate = car.selling_price;
                item_row.amount = car.selling_price;
                
                cur_frm.refresh_field('items');
                cur_frm.refresh();
                
                frappe.msgprint('تم إضافة السيارة إلى الفاتورة بنجاح');
            }
        }
    });
}

function load_car_details(frm) {
    if (frm.doc.car_record) {
        frappe.call({
            method: 'frappe.client.get',
            args: {
                doctype: 'Car Record',
                name: frm.doc.car_record
            },
            callback: function(r) {
                if (r.message) {
                    show_car_details_banner(frm, r.message);
                }
            }
        });
    }
}

function show_car_details_banner(frm, car_data) {
    if (!car_data && frm.doc.car_record) {
        load_car_details(frm);
        return;
    }
    
    if (car_data) {
        let banner_html = `
            <div class="alert alert-info car-details-banner" style="margin: 10px 0;">
                <h4><i class="fa fa-car"></i> تفاصيل السيارة</h4>
                <div class="row">
                    <div class="col-md-4">
                        <strong>الاسم:</strong> ${car_data.car_name}<br>
                        <strong>الماركة:</strong> ${car_data.brand}<br>
                        <strong>الموديل:</strong> ${car_data.model}
                    </div>
                    <div class="col-md-4">
                        <strong>السنة:</strong> ${car_data.year}<br>
                        <strong>اللون:</strong> ${car_data.color || 'غير محدد'}<br>
                        <strong>الكيلومترات:</strong> ${car_data.mileage || 0} كم
                    </div>
                    <div class="col-md-4">
                        <strong>السعر:</strong> ${format_currency(car_data.selling_price)}<br>
                        <strong>رقم الهيكل:</strong> ${car_data.vin_number || 'غير محدد'}<br>
                        <strong>الحالة:</strong> <span class="badge badge-success">${car_data.status}</span>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing banner
        $('.car-details-banner').remove();
        
        // Add new banner
        frm.layout.wrapper.find('.form-layout').prepend(banner_html);
    }
}

function print_car_invoice(frm) {
    frappe.utils.print(
        frm.doctype,
        frm.docname,
        'فاتورة بيع سيارة فاخرة',
        null,
        null,
        null
    );
}

// Utility function for currency formatting
function format_currency(amount) {
    return frappe.format(amount, {fieldtype: 'Currency'});
}