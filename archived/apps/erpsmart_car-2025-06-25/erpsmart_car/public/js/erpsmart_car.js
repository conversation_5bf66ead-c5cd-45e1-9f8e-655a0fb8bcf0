// ERP Smart Car - Luxury Car Showroom JavaScript

frappe.provide('erpsmart_car');

// Initialize when DOM is ready
$(document).ready(function() {
    erpsmart_car.init();
});

erpsmart_car = {
    init: function() {
        this.setup_car_dashboard();
        this.setup_luxury_features();
        this.setup_car_status_updates();
        this.setup_price_calculators();
    },

    // Setup Car Dashboard with Real-time Stats
    setup_car_dashboard: function() {
        if (frappe.get_route()[0] === 'List' && frappe.get_route()[1] === 'Car Record') {
            this.add_dashboard_stats();
        }
    },

    // Add Dashboard Statistics
    add_dashboard_stats: function() {
        frappe.call({
            method: 'erpsmart_car.api.dashboard.get_car_stats',
            callback: function(r) {
                if (r.message) {
                    const stats = r.message;
                    const dashboard_html = `
                        <div class="row" style="margin: 20px 0;">
                            <div class="col-md-3">
                                <div class="dashboard-card">
                                    <div class="stat-number">${stats.total_cars}</div>
                                    <div class="stat-label">إجمالي السيارات</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="dashboard-card">
                                    <div class="stat-number">${stats.available_cars}</div>
                                    <div class="stat-label">سيارات متاحة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="dashboard-card">
                                    <div class="stat-number">${stats.sold_cars}</div>
                                    <div class="stat-label">سيارات مباعة</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="dashboard-card">
                                    <div class="stat-number">${frappe.format(stats.total_value, {fieldtype: 'Currency'})}</div>
                                    <div class="stat-label">القيمة الإجمالية</div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('.layout-main .page-head').after(dashboard_html);
                }
            }
        });
    },

    // Setup Luxury Features Display
    setup_luxury_features: function() {
        $(document).on('luxury_features_add', '.grid-row', function() {
            erpsmart_car.enhance_luxury_features_display();
        });
    },

    // Enhance Luxury Features Display
    enhance_luxury_features_display: function() {
        setTimeout(function() {
            $('.grid-body .grid-row').each(function() {
                const $row = $(this);
                const feature_name = $row.find('[data-fieldname="feature_name"] .static-area').text();
                const is_premium = $row.find('[data-fieldname="is_premium"] .static-area').text();
                
                if (feature_name && is_premium === '1') {
                    $row.addClass('premium-feature');
                    $row.find('[data-fieldname="feature_name"]').prepend('<i class="fa fa-star text-warning"></i> ');
                }
            });
        }, 500);
    },

    // Setup Car Status Updates with Animations
    setup_car_status_updates: function() {
        frappe.ui.form.on('Car Record', {
            refresh: function(frm) {
                erpsmart_car.add_car_status_indicator(frm);
                erpsmart_car.add_quick_actions(frm);
                erpsmart_car.setup_price_validations(frm);
            },

            status: function(frm) {
                erpsmart_car.update_status_indicator(frm);
            },

            selling_price: function(frm) {
                erpsmart_car.calculate_profit_margin(frm);
            },

            purchase_price: function(frm) {
                erpsmart_car.calculate_profit_margin(frm);
            }
        });
    },

    // Add Car Status Indicator
    add_car_status_indicator: function(frm) {
        if (frm.doc.status) {
            const status_class = {
                'متاحة': 'available',
                'محجوزة': 'reserved', 
                'مباعة': 'sold',
                'قيد الصيانة': 'maintenance'
            };

            const indicator_html = `
                <div class="car-status ${status_class[frm.doc.status] || 'available'}">
                    ${frm.doc.status}
                </div>
            `;

            $('.page-title .title-text').after(indicator_html);
        }
    },

    // Update Status Indicator
    update_status_indicator: function(frm) {
        $('.car-status').remove();
        this.add_car_status_indicator(frm);
    },

    // Add Quick Actions
    add_quick_actions: function(frm) {
        if (frm.doc.status === 'متاحة') {
            frm.add_custom_button(__('إنشاء عرض سعر'), function() {
                erpsmart_car.create_quotation(frm);
            }, __('الإجراءات'));

            frm.add_custom_button(__('تحديد كمباعة'), function() {
                erpsmart_car.mark_as_sold(frm);
            }, __('الإجراءات'));
        }

        if (frm.doc.status === 'مباعة' && frm.doc.customer) {
            frm.add_custom_button(__('عرض فاتورة البيع'), function() {
                frappe.set_route('List', 'Sales Invoice', {customer: frm.doc.customer});
            }, __('الإجراءات'));
        }

        // Add Print Options
        frm.add_custom_button(__('طباعة بطاقة السيارة'), function() {
            erpsmart_car.print_car_card(frm);
        }, __('طباعة'));
    },

    // Create Quotation
    create_quotation: function(frm) {
        frappe.prompt([
            {
                label: 'العميل',
                fieldname: 'customer',
                fieldtype: 'Link',
                options: 'Customer',
                reqd: 1
            },
            {
                label: 'صالح حتى',
                fieldname: 'valid_till',
                fieldtype: 'Date',
                default: frappe.datetime.add_days(frappe.datetime.nowdate(), 30)
            }
        ], function(values) {
            frappe.call({
                method: 'erpsmart_car.api.car_record.create_quotation',
                args: {
                    car_record: frm.doc.name,
                    customer: values.customer,
                    valid_till: values.valid_till
                },
                callback: function(r) {
                    if (r.message) {
                        frappe.msgprint('تم إنشاء عرض السعر بنجاح');
                        frappe.set_route('Form', 'Quotation', r.message);
                    }
                }
            });
        }, 'إنشاء عرض سعر للعميل');
    },

    // Mark as Sold
    mark_as_sold: function(frm) {
        frappe.prompt([
            {
                label: 'العميل',
                fieldname: 'customer',
                fieldtype: 'Link',
                options: 'Customer',
                reqd: 1
            }
        ], function(values) {
            frappe.call({
                method: 'mark_as_sold',
                doc: frm.doc,
                args: {
                    customer: values.customer
                },
                callback: function(r) {
                    frm.refresh();
                    frappe.show_alert({
                        message: 'تم تحديد السيارة كمباعة بنجاح',
                        indicator: 'green'
                    });
                }
            });
        }, 'تحديد العميل المشتري');
    },

    // Setup Price Calculators
    setup_price_calculators: function() {
        // Real-time profit margin calculation
        this.setup_profit_calculator();
    },

    // Calculate Profit Margin
    calculate_profit_margin: function(frm) {
        if (frm.doc.purchase_price && frm.doc.selling_price) {
            const profit = frm.doc.selling_price - frm.doc.purchase_price;
            const margin = (profit / frm.doc.purchase_price) * 100;
            
            // Update profit margin field
            frm.set_value('profit_margin', margin);
            
            // Add visual indicator
            const profit_class = margin > 20 ? 'profit-high' : margin > 10 ? 'profit-medium' : 'profit-low';
            const profit_html = `
                <div class="profit-indicator ${profit_class}">
                    هامش الربح: ${margin.toFixed(1)}%
                </div>
            `;
            
            $('.form-layout [data-fieldname="profit_margin"]').after(profit_html);
        }
    },

    // Setup Price Validations
    setup_price_validations: function(frm) {
        // Add price validation warnings
        if (frm.doc.minimum_price && frm.doc.selling_price) {
            if (frm.doc.selling_price < frm.doc.minimum_price) {
                frappe.msgprint({
                    title: 'تحذير السعر',
                    message: 'سعر البيع أقل من أقل سعر مقبول',
                    indicator: 'orange'
                });
            }
        }
    },

    // Print Car Card
    print_car_card: function(frm) {
        const print_format = 'بطاقة السيارة الفاخرة';
        frappe.utils.print(
            frm.doctype,
            frm.docname,
            print_format,
            null,
            null,
            null
        );
    },

    // Setup Profit Calculator
    setup_profit_calculator: function() {
        // Add profit calculator modal
        $(document).on('click', '.profit-calculator-btn', function() {
            erpsmart_car.show_profit_calculator();
        });
    },

    // Show Profit Calculator Modal
    show_profit_calculator: function() {
        const dialog = new frappe.ui.Dialog({
            title: 'حاسبة الربح المتقدمة',
            fields: [
                {
                    fieldtype: 'Currency',
                    label: 'سعر الشراء',
                    fieldname: 'purchase_price',
                    reqd: 1
                },
                {
                    fieldtype: 'Currency',
                    label: 'سعر البيع المطلوب',
                    fieldname: 'selling_price',
                    reqd: 1
                },
                {
                    fieldtype: 'Percent',
                    label: 'هامش الربح',
                    fieldname: 'profit_margin',
                    read_only: 1
                },
                {
                    fieldtype: 'Currency',
                    label: 'صافي الربح',
                    fieldname: 'net_profit',
                    read_only: 1
                }
            ],
            primary_action_label: 'حساب',
            primary_action: function(values) {
                const profit = values.selling_price - values.purchase_price;
                const margin = (profit / values.purchase_price) * 100;
                
                dialog.set_value('profit_margin', margin);
                dialog.set_value('net_profit', profit);
            }
        });
        
        dialog.show();
    }
};

// Custom List View Enhancements
frappe.listview_settings['Car Record'] = {
    onload: function(listview) {
        // Add custom filters
        listview.page.add_menu_item(__('السيارات المتاحة'), function() {
            listview.filter_area.add([['Car Record', 'status', '=', 'متاحة']]);
        });
        
        listview.page.add_menu_item(__('السيارات الفاخرة'), function() {
            listview.filter_area.add([['Car Record', 'brand', 'in', ['مرسيدس', 'BMW', 'أودي', 'لكزس']]]);
        });
    },
    
    refresh: function(listview) {
        // Add custom styling to list items
        listview.$result.find('.list-item').each(function() {
            const $item = $(this);
            const status = $item.find('[data-field="status"]').text();
            
            if (status === 'مباعة') {
                $item.addClass('sold-car');
            } else if (status === 'متاحة') {
                $item.addClass('available-car');
            }
        });
    }
};