/* ERP Smart Car - Luxury Car Showroom Styles */

/* Car Record Form Styling */
.car-record-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Luxury Card Design */
.car-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    margin: 20px 0;
}

.car-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.2);
}

/* Car Status Indicators */
.car-status {
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: bold;
    text-align: center;
    display: inline-block;
    color: white;
    font-size: 12px;
    text-transform: uppercase;
}

.car-status.available {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
}

.car-status.reserved {
    background: linear-gradient(45deg, #FF9800, #FFC107);
}

.car-status.sold {
    background: linear-gradient(45deg, #F44336, #E91E63);
}

.car-status.maintenance {
    background: linear-gradient(45deg, #2196F3, #03A9F4);
}

/* Luxury Features List */
.luxury-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
}

.luxury-feature {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: bold;
    box-shadow: 0 3px 10px rgba(255,215,0,0.3);
}

/* Premium Price Display */
.price-display {
    font-size: 24px;
    font-weight: bold;
    color: #2E7D32;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    margin: 10px 0;
}

.price-currency {
    font-size: 18px;
    color: #666;
}

/* Car Image Gallery */
.car-image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.car-image-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.car-image-item:hover {
    transform: scale(1.05);
}

.car-image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

/* Profit Margin Indicator */
.profit-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    color: white;
}

.profit-high {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
}

.profit-medium {
    background: linear-gradient(45deg, #FF9800, #FFC107);
}

.profit-low {
    background: linear-gradient(45deg, #F44336, #E91E63);
}

/* Brand Logo Styling */
.brand-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: contain;
    background: white;
    padding: 5px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
    margin-right: 15px;
}

/* Dashboard Stats Cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    color: white;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin: 15px;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card .stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
}

.dashboard-card .stat-label {
    font-size: 14px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Action Buttons */
.car-action-btn {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    border: none;
    border-radius: 25px;
    color: white;
    padding: 10px 20px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
    box-shadow: 0 5px 15px rgba(33,150,243,0.3);
}

.car-action-btn:hover {
    background: linear-gradient(45deg, #1976D2, #0097A7);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(33,150,243,0.4);
}

.car-action-btn.danger {
    background: linear-gradient(45deg, #F44336, #E91E63);
}

.car-action-btn.success {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
}

/* RTL Support */
[dir="rtl"] .car-card {
    text-align: right;
}

[dir="rtl"] .brand-logo {
    margin-right: 0;
    margin-left: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .car-image-gallery {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .dashboard-card {
        margin: 10px 0;
    }
    
    .price-display {
        font-size: 20px;
    }
}

/* Loading Animation for Luxury Feel */
.luxury-loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #FFD700;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Premium Form Fields */
.premium-field {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #FFD700, #FFA500) border-box;
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
}

.premium-field:focus {
    box-shadow: 0 0 20px rgba(255,215,0,0.3);
    outline: none;
}