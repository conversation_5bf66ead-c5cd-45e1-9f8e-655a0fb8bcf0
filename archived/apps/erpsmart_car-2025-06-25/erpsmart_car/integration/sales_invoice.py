# -*- coding: utf-8 -*-
# Copyright (c) 2024, NewsMART and contributors
# Integration with Sales Invoice for Car Sales

from __future__ import unicode_literals
import frappe
from frappe.utils import nowdate

def validate_car_sale(doc, method):
	"""التحقق من بيع السيارة في الفاتورة"""
	for item in doc.items:
		# البحث عن سجل السيارة المرتبط بالصنف
		car_record = frappe.db.sql("""
			SELECT name, status, selling_price 
			FROM `tabCar Record` 
			WHERE item_code = %s
		""", item.item_code, as_dict=True)
		
		if car_record:
			car = car_record[0]
			
			# التحقق من حالة السيارة
			if car.status == "مباعة":
				frappe.throw(f"السيارة {item.item_name} مباعة بالفعل ولا يمكن بيعها مرة أخرى")
			
			# التحقق من السعر
			if item.rate < car.selling_price * 0.8:  # تحذير إذا كان السعر أقل من 80% من السعر المحدد
				frappe.msgprint(
					f"تنبيه: سعر البيع للسيارة {item.item_name} أقل من السعر المحدد بشكل كبير",
					alert=True
				)

def update_car_status(doc, method):
	"""تحديث حالة السيارة عند إتمام البيع"""
	for item in doc.items:
		# البحث عن سجل السيارة المرتبط بالصنف
		car_record = frappe.db.get_value(
			"Car Record", 
			{"item_code": item.item_code}, 
			"name"
		)
		
		if car_record:
			# تحديث حالة السيارة
			car_doc = frappe.get_doc("Car Record", car_record)
			car_doc.status = "مباعة"
			car_doc.customer = doc.customer
			car_doc.sale_date = doc.posting_date or nowdate()
			car_doc.sales_invoice = doc.name
			car_doc.save(ignore_permissions=True)
			
			frappe.msgprint(f"تم تحديث حالة السيارة {item.item_name} إلى مباعة", alert=True)