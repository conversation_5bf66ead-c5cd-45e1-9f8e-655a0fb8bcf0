/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#1D3557',
        accent: '#D4AF37'
      }
    }
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        smarttalk: {
          'primary': '#1D3557',
          'secondary': '#D4AF37',
          'accent': '#1D3557',
          'neutral': '#3D4451',
          'base-100': '#FFFFFF',
          'info': '#2094f3',
          'success': '#00c48c',
          'warning': '#ffb822',
          'error': '#ff5724',
        }
      }
    ]
  }
}
