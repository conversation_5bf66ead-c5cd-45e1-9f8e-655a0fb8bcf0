<script setup>
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { frappeGetList } from '../services/api'

const route = useRoute()
const router = useRouter()
const docs = ref([])
const doctype = route.params.doctype

onMounted(async () => {
  docs.value = await frappeGetList(doctype)
})

function openDoc(name) {
  router.push({ name: 'DocForm', params: { doctype, name } })
}
</script>

<template>
  <div>
    <h1 class="text-2xl font-bold mb-4">{{ doctype }}</h1>
    <table class="table w-full">
      <thead>
        <tr>
          <th>Name</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="d in docs" :key="d.name" @click="openDoc(d.name)" class="cursor-pointer hover:bg-gray-100">
          <td>{{ d.name }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
