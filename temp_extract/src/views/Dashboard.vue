<template>
  <div>
    <h1 class="text-2xl font-bold mb-4">Dashboard</h1>
    <div class="grid gap-4 grid-cols-1 md:grid-cols-3">
      <AppCard title="Sales" icon="💰" to="/doctype/Sales Invoice" />
      <AppCard title="Items" icon="📦" to="/doctype/Item" />
      <AppCard title="Employees" icon="👥" to="/doctype/Employee" />
    </div>
  </div>
</template>

<script setup>
import AppCard from '../components/AppCard.vue'
</script>
