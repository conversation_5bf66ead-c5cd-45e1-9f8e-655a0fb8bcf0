<script setup>
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { frappeGetDoc } from '../services/api'

const route = useRoute()
const doc = ref(null)
const { doctype, name } = route.params

onMounted(async () => {
  doc.value = await frappeGetDoc(doctype, name)
})
</script>

<template>
  <div>
    <h1 class="text-2xl font-bold mb-4">{{ doctype }} / {{ name }}</h1>
    <pre v-if="doc">{{ doc }}</pre>
    <p v-else>Loading...</p>
  </div>
</template>
