<template>
  <div class="flex items-center justify-center h-full">
    <div class="card w-96 bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Login to ERPNext</h2>
        <div class="form-control">
          <label class="label"><span class="label-text">Email</span></label>
          <input v-model="email" type="text" placeholder="<EMAIL>" class="input input-bordered" />
        </div>
        <div class="form-control">
          <label class="label"><span class="label-text">Password</span></label>
          <input v-model="password" type="password" placeholder="••••••" class="input input-bordered" />
        </div>
        <div class="card-actions justify-end">
          <button class="btn btn-primary" @click="loginUser">Login</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { frappeLogin } from '../services/api'

const router = useRouter()
const email = ref('')
const password = ref('')
async function loginUser() {
  const ok = await frappeLogin(email.value, password.value)
  if (ok) {
    router.push('/')
  } else {
    alert('Login failed')
  }
}
</script>
