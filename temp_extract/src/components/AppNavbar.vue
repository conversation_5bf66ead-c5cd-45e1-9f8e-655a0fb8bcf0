<template>
  <div class="navbar bg-primary text-base-100">
    <div class="flex-1">
      <router-link class="btn btn-ghost normal-case text-xl" to="/">SmartTalk</router-link>
    </div>
    <div class="flex-none gap-2">
      <div class="form-control">
        <input type="text" placeholder="Search..." class="input input-bordered" v-model="query" @keyup.enter="goSearch" />
      </div>
      <button class="btn btn-accent" @click="logout">Logout</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()
const query = ref('')
function goSearch() {
  if (query.value) {
    router.push({ name: 'DocList', params: { doctype: query.value } })
  }
}
function logout() {
  router.push('/login')
}
</script>
