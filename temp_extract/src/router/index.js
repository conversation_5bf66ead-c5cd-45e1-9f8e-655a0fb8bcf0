import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import Login from '../views/Login.vue'
import DocList from '../views/DocList.vue'
import DocForm from '../views/DocForm.vue'

const routes = [
  { path: '/', name: 'Dashboard', component: Dashboard },
  { path: '/login', name: '<PERSON><PERSON>', component: <PERSON>gin },
  { path: '/doctype/:doctype', name: 'DocList', component: DocList, props: true },
  { path: '/doctype/:doctype/:name', name: 'DocForm', component: DocForm, props: true },
  { path: '/:pathMatch(.*)*', redirect: '/' }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
