import axios from 'axios'

const domain = import.meta.env.VITE_FRAPPE_DOMAIN || window.APP_CONFIG?.domain || ''
const api = axios.create({
  baseURL: domain ? domain + '/api/method' : '/api/method',
  withCredentials: true
})

export async function frappeLogin(email, password) {
  try {
    await api.post('/login', { usr: email, pwd: password })
    return true
  } catch (e) {
    console.error(e)
    return false
  }
}

export async function frappeGetList(doctype) {
  const { data } = await api.get('/frappe.client.get_list', {
    params: { doctype, fields: '["name"]', limit_page_length: 20 }
  })
  return data.message
}

export async function frappeGetDoc(doctype, name) {
  const { data } = await api.get('/frappe.client.get', { params: { doctype, name } })
  return data.message
}
