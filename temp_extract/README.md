# SmartTalk ERPNext Vue Theme

This is a modern Vue 3 + Tailwind CSS theme for ERPNext v15, generated 2025-07-11.

## Quick Start

```bash
npm install
npm run dev
```

Configure the ERPNext domain via `appConfig.json` or `.env` / `VITE_FRAPPE_DOMAIN`.

Build for production:

```bash
npm run build
```

### Embed inside Frappe

Copy the `dist/` folder into a Frappe app's `public/` directory and map routes to `index.html`.
