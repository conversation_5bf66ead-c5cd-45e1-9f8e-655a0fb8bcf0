; Notes:
; priority=1 --> Lower priorities indicate programs that start first and shut down last
; killasgroup=true --> send kill signal to child processes too

; graceful timeout should always be lower than stopwaitsecs to avoid orphan gunicorn workers.
[program:frappe-bench-frappe-web]
command=/home/<USER>/frappe-bench/env/bin/gunicorn -b 127.0.0.1:8000 -w 5 --max-requests 5000 --max-requests-jitter 500 -t 120 --graceful-timeout 30 frappe.app:application --preload
priority=4
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/web.log
stderr_logfile=/home/<USER>/frappe-bench/logs/web.error.log
stopwaitsecs=40
killasgroup=true
user=root
directory=/home/<USER>/frappe-bench/sites
startretries=10

[program:frappe-bench-frappe-schedule]
command=/usr/local/bin/bench schedule
priority=3
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/schedule.log
stderr_logfile=/home/<USER>/frappe-bench/logs/schedule.error.log
user=root
directory=/home/<USER>/frappe-bench
startretries=10



[program:frappe-bench-frappe-short-worker]
command=/usr/local/bin/bench worker --queue short,default
priority=4
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/worker.log
stderr_logfile=/home/<USER>/frappe-bench/logs/worker.error.log
user=root
stopwaitsecs=360
directory=/home/<USER>/frappe-bench
killasgroup=true
numprocs=1
process_name=%(program_name)s-%(process_num)d
startretries=10

[program:frappe-bench-frappe-long-worker]
command=/usr/local/bin/bench worker --queue long,default,short
priority=4
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/worker.log
stderr_logfile=/home/<USER>/frappe-bench/logs/worker.error.log
user=root
stopwaitsecs=1560
directory=/home/<USER>/frappe-bench
killasgroup=true
numprocs=1
process_name=%(program_name)s-%(process_num)d
startretries=10





[program:frappe-bench-redis-cache]
command=/usr/bin/redis-server /home/<USER>/frappe-bench/config/redis_cache.conf
priority=1
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/redis-cache.log
stderr_logfile=/home/<USER>/frappe-bench/logs/redis-cache.error.log
user=root
directory=/home/<USER>/frappe-bench/sites
startretries=10

[program:frappe-bench-redis-queue]
command=/usr/bin/redis-server /home/<USER>/frappe-bench/config/redis_queue.conf
priority=1
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/redis-queue.log
stderr_logfile=/home/<USER>/frappe-bench/logs/redis-queue.error.log
user=root
directory=/home/<USER>/frappe-bench/sites
startretries=10



[program:frappe-bench-node-socketio]
command=/usr/bin/node /home/<USER>/frappe-bench/apps/frappe/socketio.js
priority=4
autostart=true
autorestart=true
stdout_logfile=/home/<USER>/frappe-bench/logs/node-socketio.log
stderr_logfile=/home/<USER>/frappe-bench/logs/node-socketio.error.log
user=root
directory=/home/<USER>/frappe-bench
startretries=10


[group:frappe-bench-web]
programs=frappe-bench-frappe-web,frappe-bench-node-socketio




[group:frappe-bench-workers]
programs=frappe-bench-frappe-schedule,frappe-bench-frappe-short-worker,frappe-bench-frappe-long-worker




[group:frappe-bench-redis]
programs=frappe-bench-redis-cache,frappe-bench-redis-queue
