app_name = "erpsmart_car"
app_title = "ERP Smart Car"
app_publisher = "NewsMART"
app_description = "نظام إدارة معرض السيارات المتكامل مع ERPNext - نظام فخم ومتطور لإدارة معارض السيارات"
app_email = "<EMAIL>"
app_license = "MIT"
app_version = "1.0.0"

# Dependencies
required_apps = ["frappe", "erpnext"]

# Each item in the list will be shown as an app in the apps page
add_to_apps_screen = [
	{
		"name": "erpsmart_car",
		"logo": "/assets/erpsmart_car/images/car_logo.png",
		"title": "ERP Smart Car",
		"route": "/app/car-management",
		"has_permission": "erpsmart_car.api.permission.has_app_permission"
	}
]

# Custom App Include CSS/JS
app_include_css = "/assets/erpsmart_car/css/erpsmart_car.css"
app_include_js = "/assets/erpsmart_car/js/erpsmart_car.js"

# Integration with ERPNext
doc_events = {
	"Sales Invoice": {
		"validate": "erpsmart_car.integration.sales_invoice.validate_car_sale",
		"on_submit": "erpsmart_car.integration.sales_invoice.update_car_status"
	},
	"Quotation": {
		"validate": "erpsmart_car.integration.quotation.validate_car_quotation"
	},
	"Item": {
		"validate": "erpsmart_car.integration.item.validate_car_item"
	}
}

# Custom DocType Scripts
doctype_js = {
	"Sales Invoice": "public/js/sales_invoice.js",
	"Quotation": "public/js/quotation.js",
	"Customer": "public/js/customer.js"
}

# Installation
after_install = "erpsmart_car.install.after_install"

# Uninstallation
before_uninstall = "erpsmart_car.install.before_uninstall"