# -*- coding: utf-8 -*-
# Integration with Sales Invoice for Car Sales
from __future__ import unicode_literals
import frappe
from frappe import _
from frappe.utils import nowdate

def validate_car_sale(doc, method):
	"""التحقق من صحة بيع السيارة عند إنشاء فاتورة المبيعات"""
	
	# البحث عن السيارة في الفاتورة
	car_items = []
	for item in doc.items:
		if frappe.db.exists("Car Record", {"item_code": item.item_code}):
			car_items.append(item)
	
	# إذا كانت الفاتورة تحتوي على سيارات
	if car_items:
		for car_item in car_items:
			car_record = frappe.db.get_value("Car Record", 
				{"item_code": car_item.item_code}, 
				["name", "status", "selling_price", "minimum_price"], 
				as_dict=True)
			
			if car_record:
				# التحقق من توفر السيارة
				if car_record.status not in ["Available", "Reserved"]:
					frappe.throw(_("Car {0} is not available for sale. Current status: {1}")
						.format(car_item.item_name, car_record.status))
				
				# التحقق من السعر الأدنى
				if car_record.minimum_price and car_item.rate < car_record.minimum_price:
					frappe.throw(_("Sale price {0} is below minimum acceptable price {1} for car {2}")
						.format(car_item.rate, car_record.minimum_price, car_item.item_name))
				
				# ربط الفاتورة بسجل السيارة
				if not doc.get("car_record"):
					doc.car_record = car_record.name

def on_submit_car_sale(doc, method):
	"""عند تأكيد فاتورة بيع السيارة"""
	
	# البحث عن السيارات في الفاتورة وتحديث حالتها
	for item in doc.items:
		if frappe.db.exists("Car Record", {"item_code": item.item_code}):
			car_record = frappe.get_doc("Car Record", {"item_code": item.item_code})
			
			# تحديث حالة السيارة إلى مباعة
			car_record.status = "Sold"
			car_record.customer = doc.customer
			car_record.sale_date = doc.posting_date or nowdate()
			car_record.final_selling_price = item.rate
			car_record.sales_invoice = doc.name
			car_record.save(ignore_permissions=True)
			
			# إضافة تعليق
			car_record.add_comment("Info", 
				_("Car sold to {0} via Sales Invoice {1}").format(doc.customer, doc.name))
			
			frappe.msgprint(_("Car {0} status updated to Sold").format(car_record.car_name), 
				alert=True)

# إضافة حقل مخصص لفاتورة المبيعات
def add_custom_fields():
	"""إضافة حقول مخصصة لفاتورة المبيعات"""
	
	custom_fields = {
		"Sales Invoice": [
			{
				"fieldname": "car_sale_details",
				"label": "Car Sale Details",
				"fieldtype": "Section Break",
				"insert_after": "customer_address",
				"collapsible": 1
			},
			{
				"fieldname": "car_record",
				"label": "Car Record",
				"fieldtype": "Link",
				"options": "Car Record",
				"insert_after": "car_sale_details"
			},
			{
				"fieldname": "trade_in_vehicle",
				"label": "Trade-in Vehicle",
				"fieldtype": "Link",
				"options": "Car Record",
				"insert_after": "car_record"
			},
			{
				"fieldname": "column_break_car",
				"fieldtype": "Column Break",
				"insert_after": "trade_in_vehicle"
			},
			{
				"fieldname": "financing_type",
				"label": "Financing Type",
				"fieldtype": "Select",
				"options": "Cash\nBank Loan\nCompany Financing\nLeasing",
				"insert_after": "column_break_car"
			},
			{
				"fieldname": "down_payment",
				"label": "Down Payment",
				"fieldtype": "Currency",
				"precision": "2",
				"insert_after": "financing_type"
			}
		],
		"Quotation": [
			{
				"fieldname": "car_quotation_details",
				"label": "Car Quotation Details",
				"fieldtype": "Section Break",
				"insert_after": "customer_address",
				"collapsible": 1
			},
			{
				"fieldname": "car_record",
				"label": "Car Record",
				"fieldtype": "Link",
				"options": "Car Record",
				"insert_after": "car_quotation_details"
			},
			{
				"fieldname": "test_drive_scheduled",
				"label": "Test Drive Scheduled",
				"fieldtype": "Check",
				"insert_after": "car_record"
			},
			{
				"fieldname": "column_break_car_quot",
				"fieldtype": "Column Break",
				"insert_after": "test_drive_scheduled"
			},
			{
				"fieldname": "test_drive_date",
				"label": "Test Drive Date",
				"fieldtype": "Datetime",
				"depends_on": "test_drive_scheduled",
				"insert_after": "column_break_car_quot"
			}
		]
	}
	
	for doctype, fields in custom_fields.items():
		for field in fields:
			if not frappe.db.exists("Custom Field", {"dt": doctype, "fieldname": field["fieldname"]}):
				custom_field = frappe.new_doc("Custom Field")
				custom_field.dt = doctype
				custom_field.update(field)
				custom_field.insert(ignore_permissions=True)