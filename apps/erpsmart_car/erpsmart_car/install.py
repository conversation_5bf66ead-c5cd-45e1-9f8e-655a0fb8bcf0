# -*- coding: utf-8 -*-
# Installation and setup functions
from __future__ import unicode_literals
import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields

def after_install():
	"""إعدادات ما بعد التثبيت"""
	
	# إنشاء Item Group للسيارات
	create_car_item_group()
	
	# إضافة حقول مخصصة
	add_custom_fields()
	
	# إنشاء ماركات السيارات الأساسية
	create_default_car_brands()
	
	# إنشاء الخصائص الأساسية
	create_default_features()
	
	# إنشاء إعدادات النظام
	setup_car_showroom_settings()
	
	frappe.msgprint(_("ERP Smart Car app installed successfully!"), alert=True)

def create_car_item_group():
	"""إنشاء مجموعة أصناف خاصة بالسيارات"""
	
	if not frappe.db.exists("Item Group", "Cars"):
		car_group = frappe.new_doc("Item Group")
		car_group.item_group_name = "Cars"
		car_group.parent_item_group = "All Item Groups"
		car_group.is_group = 0
		car_group.insert(ignore_permissions=True)
		
	# إنشاء مجموعات فرعية
	sub_groups = [
		"Luxury Cars",
		"Sports Cars", 
		"SUV Cars",
		"Electric Cars",
		"Classic Cars"
	]
	
	for group_name in sub_groups:
		if not frappe.db.exists("Item Group", group_name):
			sub_group = frappe.new_doc("Item Group")
			sub_group.item_group_name = group_name
			sub_group.parent_item_group = "Cars"
			sub_group.is_group = 0
			sub_group.insert(ignore_permissions=True)

def add_custom_fields():
	"""إضافة حقول مخصصة للـ DocTypes المختلفة"""
	
	# حقول مخصصة لفاتورة المبيعات
	sales_invoice_fields = [
		{
			"fieldname": "car_sale_section",
			"label": "Car Sale Details",
			"fieldtype": "Section Break",
			"insert_after": "customer_address",
			"collapsible": 1
		},
		{
			"fieldname": "car_record",
			"label": "Car Record",
			"fieldtype": "Link",
			"options": "Car Record",
			"insert_after": "car_sale_section"
		},
		{
			"fieldname": "trade_in_vehicle",
			"label": "Trade-in Vehicle",
			"fieldtype": "Link",
			"options": "Car Record",
			"insert_after": "car_record"
		},
		{
			"fieldname": "financing_type",
			"label": "Financing Type",
			"fieldtype": "Select",
			"options": "Cash\nBank Loan\nCompany Financing\nLeasing",
			"insert_after": "trade_in_vehicle"
		}
	]
	
	# حقول مخصصة لعروض الأسعار
	quotation_fields = [
		{
			"fieldname": "car_quotation_section",
			"label": "Car Details",
			"fieldtype": "Section Break",
			"insert_after": "customer_address",
			"collapsible": 1
		},
		{
			"fieldname": "car_record",
			"label": "Car Record",
			"fieldtype": "Link",
			"options": "Car Record",
			"insert_after": "car_quotation_section"
		},
		{
			"fieldname": "test_drive_scheduled",
			"label": "Test Drive Scheduled",
			"fieldtype": "Check",
			"insert_after": "car_record"
		},
		{
			"fieldname": "test_drive_date",
			"label": "Test Drive Date",
			"fieldtype": "Datetime",
			"depends_on": "test_drive_scheduled",
			"insert_after": "test_drive_scheduled"
		}
	]
	
	# حقول مخصصة للعملاء
	customer_fields = [
		{
			"fieldname": "car_customer_section",
			"label": "Car Customer Details",
			"fieldtype": "Section Break",
			"insert_after": "customer_details",
			"collapsible": 1
		},
		{
			"fieldname": "preferred_car_brand",
			"label": "Preferred Car Brand",
			"fieldtype": "Link",
			"options": "Car Brand",
			"insert_after": "car_customer_section"
		},
		{
			"fieldname": "budget_range",
			"label": "Budget Range",
			"fieldtype": "Select",
			"options": "Under 50,000\n50,000 - 100,000\n100,000 - 200,000\n200,000 - 500,000\nAbove 500,000",
			"insert_after": "preferred_car_brand"
		},
		{
			"fieldname": "driving_license_number",
			"label": "Driving License Number",
			"fieldtype": "Data",
			"insert_after": "budget_range"
		}
	]
	
	# إنشاء الحقول المخصصة
	custom_fields = {
		"Sales Invoice": sales_invoice_fields,
		"Quotation": quotation_fields,
		"Customer": customer_fields
	}
	
	create_custom_fields(custom_fields)

def create_default_car_brands():
	"""إنشاء ماركات السيارات الأساسية"""
	
	default_brands = [
		{
			"brand_name": "BMW",
			"country_of_origin": "Germany",
			"brand_category": "Luxury",
			"is_luxury_brand": 1,
			"established_year": 1916
		},
		{
			"brand_name": "Mercedes-Benz",
			"country_of_origin": "Germany", 
			"brand_category": "Luxury",
			"is_luxury_brand": 1,
			"established_year": 1926
		},
		{
			"brand_name": "Audi",
			"country_of_origin": "Germany",
			"brand_category": "Luxury",
			"is_luxury_brand": 1,
			"established_year": 1909
		},
		{
			"brand_name": "Lexus",
			"country_of_origin": "Japan",
			"brand_category": "Luxury",
			"is_luxury_brand": 1,
			"established_year": 1989
		},
		{
			"brand_name": "Toyota",
			"country_of_origin": "Japan",
			"brand_category": "Mid-Range",
			"is_luxury_brand": 0,
			"established_year": 1937
		},
		{
			"brand_name": "Honda",
			"country_of_origin": "Japan",
			"brand_category": "Mid-Range",
			"is_luxury_brand": 0,
			"established_year": 1948
		},
		{
			"brand_name": "Ford",
			"country_of_origin": "United States",
			"brand_category": "Mid-Range",
			"is_luxury_brand": 0,
			"established_year": 1903
		},
		{
			"brand_name": "Tesla",
			"country_of_origin": "United States",
			"brand_category": "Luxury",
			"is_luxury_brand": 1,
			"established_year": 2003
		}
	]
	
	for brand_data in default_brands:
		if not frappe.db.exists("Car Brand", brand_data["brand_name"]):
			brand = frappe.new_doc("Car Brand")
			brand.update(brand_data)
			brand.insert(ignore_permissions=True)

def create_default_features():
	"""إنشاء قوالب الميزات الأساسية"""
	
	# يمكن إضافة منطق إنشاء ميزات افتراضية هنا
	pass

def setup_car_showroom_settings():
	"""إعداد إعدادات معرض السيارات"""
	
	# إنشاء إعدادات مخصصة للتطبيق
	if not frappe.db.exists("Car Showroom Settings", "Car Showroom Settings"):
		settings = frappe.new_doc("Car Showroom Settings")
		settings.name = "Car Showroom Settings"
		settings.insert(ignore_permissions=True)

def before_uninstall():
	"""إعدادات قبل إلغاء التثبيت"""
	
	# حذف الحقول المخصصة
	custom_fields_to_remove = [
		{"dt": "Sales Invoice", "fieldname": "car_sale_section"},
		{"dt": "Sales Invoice", "fieldname": "car_record"},
		{"dt": "Sales Invoice", "fieldname": "trade_in_vehicle"},
		{"dt": "Sales Invoice", "fieldname": "financing_type"},
		{"dt": "Quotation", "fieldname": "car_quotation_section"},
		{"dt": "Quotation", "fieldname": "car_record"},
		{"dt": "Quotation", "fieldname": "test_drive_scheduled"},
		{"dt": "Quotation", "fieldname": "test_drive_date"},
		{"dt": "Customer", "fieldname": "car_customer_section"},
		{"dt": "Customer", "fieldname": "preferred_car_brand"},
		{"dt": "Customer", "fieldname": "budget_range"},
		{"dt": "Customer", "fieldname": "driving_license_number"}
	]
	
	for field in custom_fields_to_remove:
		if frappe.db.exists("Custom Field", field):
			frappe.delete_doc("Custom Field", field, ignore_permissions=True)
	
	frappe.msgprint(_("ERP Smart Car app uninstalled successfully!"), alert=True)