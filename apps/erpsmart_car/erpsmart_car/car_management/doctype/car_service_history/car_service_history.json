{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["service_date", "service_type", "column_break_3", "service_cost", "service_description"], "fields": [{"fieldname": "service_date", "fieldtype": "Date", "in_list_view": 1, "label": "Service Date", "reqd": 1}, {"fieldname": "service_type", "fieldtype": "Select", "in_list_view": 1, "label": "Service Type", "options": "Regular Maintenance\nOil Change\nBrake Service\nEngine Repair\nTransmission Service\nElectrical Repair\nBody Work\nTire Change\nOther"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "service_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Service Cost", "precision": "2"}, {"fieldname": "service_description", "fieldtype": "Text", "label": "Service Description"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Management", "name": "Car Service History", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}