{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["image_title", "image_file", "column_break_3", "image_type", "is_primary"], "fields": [{"fieldname": "image_title", "fieldtype": "Data", "in_list_view": 1, "label": "Image Title"}, {"fieldname": "image_file", "fieldtype": "Attach Image", "in_list_view": 1, "label": "Image", "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "image_type", "fieldtype": "Select", "in_list_view": 1, "label": "Image Type", "options": "Exterior\nInterior\nEngine\nWheels\nDetails\nPanorama"}, {"fieldname": "is_primary", "fieldtype": "Check", "label": "Primary Image"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Management", "name": "Car Image", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}