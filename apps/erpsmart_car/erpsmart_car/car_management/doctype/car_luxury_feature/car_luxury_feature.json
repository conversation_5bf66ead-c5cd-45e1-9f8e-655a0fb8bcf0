{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["feature_name", "feature_category", "column_break_3", "is_premium", "additional_value"], "fields": [{"fieldname": "feature_name", "fieldtype": "Data", "in_list_view": 1, "label": "Feature Name", "reqd": 1}, {"fieldname": "feature_category", "fieldtype": "Select", "in_list_view": 1, "label": "Feature Category", "options": "Interior Comfort\nAdvanced Technology\nAudio System\nLuxury Seats\nClimate Control\nLighting\nWindows & Sunroof\nDriving Technology"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "is_premium", "fieldtype": "Check", "label": "Premium Feature"}, {"fieldname": "additional_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Value", "precision": "2"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Management", "name": "Car Luxury Feature", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}