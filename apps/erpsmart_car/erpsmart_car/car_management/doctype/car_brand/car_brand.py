# -*- coding: utf-8 -*-
# Copyright (c) 2024, NewsMART and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document

class CarBrand(Document):
	def validate(self):
		# التحقق من صحة اسم الماركة
		self.validate_brand_name()
		
	def validate_brand_name(self):
		"""التحقق من صحة اسم الماركة"""
		if self.brand_name:
			self.brand_name = self.brand_name.strip().title()
			
	def autoname(self):
		"""تسمية تلقائية للماركة"""
		if self.brand_name:
			self.name = self.brand_name
			
	def get_dashboard_data(self):
		"""بيانات لوحة المعلومات"""
		return {
			"fieldname": "brand",
			"transactions": [
				{
					"label": "Cars",
					"items": ["Car Record"]
				}
			]
		}