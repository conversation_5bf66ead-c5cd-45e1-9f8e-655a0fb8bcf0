{"actions": [], "allow_rename": 1, "autoname": "field:brand_name", "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["brand_name", "brand_name_english", "column_break_3", "country_of_origin", "brand_category", "section_break_6", "brand_logo", "brand_description", "column_break_9", "website", "established_year", "is_luxury_brand"], "fields": [{"fieldname": "brand_name", "fieldtype": "Data", "in_list_view": 1, "label": "Brand Name", "reqd": 1, "unique": 1}, {"fieldname": "brand_name_english", "fieldtype": "Data", "label": "English Name"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "country_of_origin", "fieldtype": "Link", "label": "Country of Origin", "options": "Country"}, {"fieldname": "brand_category", "fieldtype": "Select", "in_list_view": 1, "label": "Brand Category", "options": "Luxury\nMid-Range\nEconomic\nSports\nClassic"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"fieldname": "brand_logo", "fieldtype": "Attach Image", "label": "Brand Logo"}, {"fieldname": "brand_description", "fieldtype": "Text Editor", "label": "Brand Description"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "website", "fieldtype": "Data", "label": "Official Website"}, {"fieldname": "established_year", "fieldtype": "Int", "label": "Established Year"}, {"fieldname": "is_luxury_brand", "fieldtype": "Check", "label": "Is Luxury Brand"}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Car Record", "link_fieldname": "brand"}], "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Management", "name": "Car Brand", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"read": 1, "role": "Sales User"}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "brand_name", "track_changes": 1}