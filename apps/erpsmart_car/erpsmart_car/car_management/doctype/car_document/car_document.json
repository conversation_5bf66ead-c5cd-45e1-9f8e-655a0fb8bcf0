{"actions": [], "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["document_name", "document_type", "column_break_3", "document_file", "expiry_date"], "fields": [{"fieldname": "document_name", "fieldtype": "Data", "in_list_view": 1, "label": "Document Name", "reqd": 1}, {"fieldname": "document_type", "fieldtype": "Select", "in_list_view": 1, "label": "Document Type", "options": "Registration\nInsurance\nOwnership Certificate\nService Manual\nWarranty\nInspection Certificate\nOther"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "document_file", "fieldtype": "Attach", "label": "Document File"}, {"fieldname": "expiry_date", "fieldtype": "Date", "label": "Expiry Date"}], "index_web_pages_for_search": 1, "istable": 1, "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Management", "name": "Car Document", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}