# -*- coding: utf-8 -*-
# Copyright (c) 2024, NewsMART and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe.utils import nowdate, flt
from erpnext.accounts.utils import get_fiscal_year

class CarRecord(Document):
	def validate(self):
		# التحقق من صحة البيانات وحساب هامش الربح
		self.validate_pricing()
		self.calculate_profit_margin()
		self.create_item_if_not_exists()
		
	def validate_pricing(self):
		"""التحقق من صحة الأسعار"""
		if self.minimum_price and self.selling_price:
			if flt(self.minimum_price) > flt(self.selling_price):
				frappe.throw("Minimum acceptable price cannot be greater than selling price")
				
		if self.purchase_price and self.selling_price:
			if flt(self.purchase_price) > flt(self.selling_price):
				frappe.msgprint("Warning: Selling price is less than purchase price", alert=True)
	
	def calculate_profit_margin(self):
		"""حساب هامش الربح تلقائياً"""
		if self.purchase_price and self.selling_price:
			profit = flt(self.selling_price) - flt(self.purchase_price)
			if flt(self.purchase_price) > 0:
				self.profit_margin = (profit / flt(self.purchase_price)) * 100
	
	def create_item_if_not_exists(self):
		"""إنشاء صنف في ERPNext تلقائياً للسيارة"""
		item_code = f"{self.brand}-{self.model}-{self.year}-{self.name}"
		
		# التحقق من وجود الصنف
		if not frappe.db.exists("Item", item_code):
			item_doc = frappe.new_doc("Item")
			item_doc.item_code = item_code
			item_doc.item_name = f"{self.car_name} - {self.brand} {self.model} {self.year}"
			item_doc.item_group = "Cars"
			item_doc.stock_uom = "Nos"
			item_doc.is_stock_item = 1
			item_doc.is_sales_item = 1
			item_doc.has_serial_no = 1
			item_doc.serial_no_series = f"{self.brand}-.#####"
			item_doc.valuation_rate = self.purchase_price or 0
			item_doc.standard_rate = self.selling_price or 0
			item_doc.description = f"""
			<div style="direction: rtl; text-align: right;">
				<h3>{self.car_name}</h3>
				<p><strong>Brand:</strong> {self.brand}</p>
				<p><strong>Model:</strong> {self.model}</p>
				<p><strong>Year:</strong> {self.year}</p>
				<p><strong>Color:</strong> {self.color or 'Not Specified'}</p>
				<p><strong>Mileage:</strong> {self.mileage or 0} KM</p>
				<p><strong>Fuel Type:</strong> {self.fuel_type or 'Not Specified'}</p>
				<p><strong>Transmission:</strong> {self.transmission or 'Not Specified'}</p>
			</div>
			"""
			
			try:
				item_doc.insert(ignore_permissions=True)
				frappe.msgprint(f"New item created: {item_code}", alert=True)
				
				# ربط السيارة بالصنف
				self.db_set("item_code", item_code)
				
			except Exception as e:
				frappe.log_error(f"Error creating item for car {self.name}: {str(e)}")
	
	def on_update(self):
		"""عند تحديث السيارة"""
		if self.item_code:
			self.update_item_details()
	
	def update_item_details(self):
		"""تحديث تفاصيل الصنف عند تحديث السيارة"""
		if frappe.db.exists("Item", self.item_code):
			item_doc = frappe.get_doc("Item", self.item_code)
			item_doc.standard_rate = self.selling_price or 0
			item_doc.item_name = f"{self.car_name} - {self.brand} {self.model} {self.year}"
			item_doc.save(ignore_permissions=True)
	
	def create_quotation(self, customer, valid_till=None):
		"""إنشاء عرض سعر للعميل"""
		if not valid_till:
			valid_till = frappe.utils.add_days(nowdate(), 30)
			
		quotation = frappe.new_doc("Quotation")
		quotation.party_name = customer
		quotation.valid_till = valid_till
		quotation.car_record = self.name
		
		# إضافة السيارة كصنف في العرض
		quotation.append("items", {
			"item_code": self.item_code,
			"item_name": self.car_name,
			"description": f"{self.brand} {self.model} {self.year}",
			"qty": 1,
			"rate": self.selling_price,
			"amount": self.selling_price
		})
		
		quotation.insert()
		return quotation
	
	@frappe.whitelist()
	def mark_as_sold(self, customer, sales_invoice=None):
		"""تحديد السيارة كمباعة"""
		self.status = "Sold"
		self.customer = customer
		self.sale_date = nowdate()
		
		if sales_invoice:
			self.sales_invoice = sales_invoice
			
		self.save()
		
		# تحديث حالة المخزون
		if self.item_code:
			self.update_stock_status()
		
		frappe.msgprint(f"Car {self.car_name} marked as sold", alert=True)
	
	def update_stock_status(self):
		"""تحديث حالة المخزون"""
		# يمكن إضافة منطق تحديث المخزون هنا
		pass
		
	def get_dashboard_data(self):
		"""بيانات لوحة المعلومات"""
		return {
			"fieldname": "car_record",
			"transactions": [
				{
					"label": "Sales",
					"items": ["Quotation", "Sales Order", "Sales Invoice"]
				},
				{
					"label": "Stock",
					"items": ["Stock Entry", "Serial No"]
				}
			]
		}