{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-12-27 12:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["basic_info_section", "naming_series", "car_name", "vin_number", "column_break_5", "brand", "model", "year", "section_break_9", "body_type", "color", "engine_type", "column_break_13", "transmission", "fuel_type", "mileage", "pricing_section", "purchase_price", "selling_price", "column_break_20", "minimum_price", "profit_margin", "status_section", "status", "location", "column_break_26", "acquisition_date", "sale_date", "features_section", "luxury_features", "safety_features", "section_break_32", "interior_color", "exterior_condition", "column_break_35", "interior_condition", "service_history", "images_section", "car_images", "documents_section", "ownership_documents", "insurance_info", "section_break_42", "registration_number", "registration_expiry", "column_break_45", "insurance_expiry", "last_service_date", "customer_info_section", "customer", "sales_person", "column_break_51", "deposit_amount", "payment_terms", "notes_section", "notes", "internal_notes"], "fields": [{"fieldname": "basic_info_section", "fieldtype": "Section Break", "label": "Basic Information"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Naming Series", "options": "CAR-.YYYY.-\nLUX-.YYYY.-\nPRM-.YYYY.-", "reqd": 1}, {"fieldname": "car_name", "fieldtype": "Data", "in_list_view": 1, "label": "Car Name", "reqd": 1}, {"fieldname": "vin_number", "fieldtype": "Data", "label": "VIN Number", "unique": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "brand", "fieldtype": "Link", "in_list_view": 1, "label": "Brand", "options": "Car Brand", "reqd": 1}, {"fieldname": "model", "fieldtype": "Data", "in_list_view": 1, "label": "Model", "reqd": 1}, {"fieldname": "year", "fieldtype": "Int", "in_list_view": 1, "label": "Year", "reqd": 1}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Technical Specifications"}, {"fieldname": "body_type", "fieldtype": "Select", "label": "Body Type", "options": "Sedan\nSUV\nHatchback\nCoupe\nConvertible\nPickup\nVan\nSports"}, {"fieldname": "color", "fieldtype": "Data", "label": "Exterior Color"}, {"fieldname": "engine_type", "fieldtype": "Data", "label": "Engine Type"}, {"fieldname": "column_break_13", "fieldtype": "Column Break"}, {"fieldname": "transmission", "fieldtype": "Select", "label": "Transmission", "options": "Automatic\nManual\nCVT\nHybrid"}, {"fieldname": "fuel_type", "fieldtype": "Select", "label": "Fuel Type", "options": "Petrol\nDiesel\nHybrid\nElectric\nGas"}, {"fieldname": "mileage", "fieldtype": "Float", "label": "Mileage (KM)"}, {"fieldname": "pricing_section", "fieldtype": "Section Break", "label": "Pricing Information"}, {"fieldname": "purchase_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Purchase Price", "precision": "2"}, {"fieldname": "selling_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Selling <PERSON>", "precision": "2", "reqd": 1}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"fieldname": "minimum_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Minimum Acceptable Price", "precision": "2"}, {"fieldname": "profit_margin", "fieldtype": "Percent", "label": "<PERSON><PERSON>"}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status & Location"}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Available\nReserved\nSold\nMaintenance\nAppraisal", "reqd": 1}, {"fieldname": "location", "fieldtype": "Data", "label": "Showroom Location"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"fieldname": "acquisition_date", "fieldtype": "Date", "label": "Acquisition Date"}, {"fieldname": "sale_date", "fieldtype": "Date", "label": "Sale Date"}, {"fieldname": "features_section", "fieldtype": "Section Break", "label": "Features & Accessories"}, {"fieldname": "luxury_features", "fieldtype": "Table", "label": "Luxury Features", "options": "Car Luxury Feature"}, {"fieldname": "safety_features", "fieldtype": "Table", "label": "Safety Features", "options": "Car Safety Feature"}, {"fieldname": "section_break_32", "fieldtype": "Section Break"}, {"fieldname": "interior_color", "fieldtype": "Data", "label": "Interior Color"}, {"fieldname": "exterior_condition", "fieldtype": "Select", "label": "Exterior Condition", "options": "Excellent\nVery Good\nGood\nFair\nNeeds Repair"}, {"fieldname": "column_break_35", "fieldtype": "Column Break"}, {"fieldname": "interior_condition", "fieldtype": "Select", "label": "Interior Condition", "options": "Excellent\nVery Good\nGood\nFair\nNeeds Repair"}, {"fieldname": "service_history", "fieldtype": "Table", "label": "Service History", "options": "Car Service History"}, {"fieldname": "images_section", "fieldtype": "Section Break", "label": "Car Images"}, {"fieldname": "car_images", "fieldtype": "Table", "label": "Images", "options": "Car Image"}, {"fieldname": "documents_section", "fieldtype": "Section Break", "label": "Documents & Insurance"}, {"fieldname": "ownership_documents", "fieldtype": "Table", "label": "Ownership Documents", "options": "Car Document"}, {"fieldname": "insurance_info", "fieldtype": "Link", "label": "Insurance Company", "options": "Supplier"}, {"fieldname": "section_break_42", "fieldtype": "Section Break"}, {"fieldname": "registration_number", "fieldtype": "Data", "label": "Registration Number"}, {"fieldname": "registration_expiry", "fieldtype": "Date", "label": "Registration Expiry"}, {"fieldname": "column_break_45", "fieldtype": "Column Break"}, {"fieldname": "insurance_expiry", "fieldtype": "Date", "label": "Insurance Expiry"}, {"fieldname": "last_service_date", "fieldtype": "Date", "label": "Last Service Date"}, {"fieldname": "customer_info_section", "fieldtype": "Section Break", "label": "Customer & Sales Information"}, {"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Customer"}, {"fieldname": "sales_person", "fieldtype": "Link", "label": "Sales Person", "options": "Sales Person"}, {"fieldname": "column_break_51", "fieldtype": "Column Break"}, {"fieldname": "deposit_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "precision": "2"}, {"fieldname": "payment_terms", "fieldtype": "Link", "label": "Payment Terms", "options": "Payment Terms Template"}, {"fieldname": "notes_section", "fieldtype": "Section Break", "label": "Notes"}, {"fieldname": "notes", "fieldtype": "Text Editor", "label": "General Notes"}, {"fieldname": "internal_notes", "fieldtype": "Text Editor", "label": "Internal Notes"}], "index_web_pages_for_search": 1, "links": [{"link_doctype": "Sales Invoice", "link_fieldname": "car_record"}, {"link_doctype": "Quotation", "link_fieldname": "car_record"}], "modified": "2024-12-27 12:00:00.000000", "modified_by": "Administrator", "module": "Car Management", "name": "Car Record", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "car_name", "track_changes": 1, "track_seen": 1, "track_views": 1}