// Main ERP Smart Car JavaScript File
// وظائف JavaScript الرئيسية لتطبيق إدارة معرض السيارات

$(document).ready(function() {
    // تحسين واجهة المستخدم لتطبيق السيارات
    setupCarShowroomInterface();
});

function setupCarShowroomInterface() {
    // إضافة أيقونات مخصصة للسيارات
    if (window.location.pathname.includes('/car-record')) {
        addCarRecordEnhancements();
    }
    
    // تحسين قوائم السيارات
    if (window.location.pathname.includes('/List/Car Record')) {
        enhanceCarListView();
    }
}

function addCarRecordEnhancements() {
    // إضافة أزرار مخصصة لسجل السيارة
    setTimeout(function() {
        if (cur_frm && cur_frm.doctype === 'Car Record') {
            // زر لإنشاء عرض سعر سريع
            if (cur_frm.doc.status === 'Available' && !cur_frm.is_new()) {
                cur_frm.add_custom_button(__('Create Quotation'), function() {
                    createQuickQuotation(cur_frm.doc.name);
                }, __('Actions'));
            }
            
            // زر لحجز السيارة
            if (cur_frm.doc.status === 'Available' && !cur_frm.is_new()) {
                cur_frm.add_custom_button(__('Reserve Car'), function() {
                    reserveCarDialog(cur_frm.doc.name);
                }, __('Actions'));
            }
            
            // عرض معلومات إضافية
            if (cur_frm.doc.name) {
                displayCarInsights(cur_frm);
            }
        }
    }, 1000);
}

function enhanceCarListView() {
    // تحسين عرض قائمة السيارات
    setTimeout(function() {
        // إضافة مرشحات سريعة
        addQuickFilters();
        
        // إضافة أزرار عمل سريعة
        addBulkActions();
    }, 1500);
}

function createQuickQuotation(car_name) {
    // حوار إنشاء عرض سعر سريع
    let dialog = new frappe.ui.Dialog({
        title: __('Create Quotation'),
        fields: [
            {
                fieldtype: 'Link',
                fieldname: 'customer',
                label: __('Customer'),
                options: 'Customer',
                reqd: 1
            },
            {
                fieldtype: 'Date',
                fieldname: 'valid_till',
                label: __('Valid Till'),
                default: frappe.datetime.add_days(frappe.datetime.nowdate(), 30)
            },
            {
                fieldtype: 'Section Break'
            },
            {
                fieldtype: 'Check',
                fieldname: 'include_accessories',
                label: __('Include Accessories')
            },
            {
                fieldtype: 'Table',
                fieldname: 'accessories',
                label: __('Additional Items'),
                cannot_add_rows: false,
                depends_on: 'include_accessories',
                fields: [
                    {
                        fieldtype: 'Link',
                        fieldname: 'item_code',
                        label: __('Item'),
                        options: 'Item',
                        in_list_view: 1
                    },
                    {
                        fieldtype: 'Float',
                        fieldname: 'qty',
                        label: __('Qty'),
                        default: 1,
                        in_list_view: 1
                    },
                    {
                        fieldtype: 'Currency',
                        fieldname: 'rate',
                        label: __('Rate'),
                        in_list_view: 1
                    }
                ]
            }
        ],
        primary_action_label: __('Create'),
        primary_action: function(values) {
            frappe.call({
                method: 'erpsmart_car.api.car_record.create_car_quotation',
                args: {
                    car_name: car_name,
                    customer: values.customer,
                    additional_items: values.accessories || []
                },
                callback: function(r) {
                    if (r.message) {
                        frappe.msgprint(__('Quotation {0} created successfully', [r.message]));
                        dialog.hide();
                        // فتح عرض السعر الجديد
                        frappe.set_route('Form', 'Quotation', r.message);
                    }
                }
            });
        }
    });
    
    dialog.show();
}

function reserveCarDialog(car_name) {
    // حوار حجز السيارة
    let dialog = new frappe.ui.Dialog({
        title: __('Reserve Car'),
        fields: [
            {
                fieldtype: 'Link',
                fieldname: 'customer',
                label: __('Customer'),
                options: 'Customer',
                reqd: 1
            },
            {
                fieldtype: 'Currency',
                fieldname: 'deposit_amount',
                label: __('Deposit Amount'),
                precision: 2
            },
            {
                fieldtype: 'Small Text',
                fieldname: 'notes',
                label: __('Reservation Notes')
            }
        ],
        primary_action_label: __('Reserve'),
        primary_action: function(values) {
            frappe.call({
                method: 'erpsmart_car.api.car_record.reserve_car',
                args: {
                    car_name: car_name,
                    customer: values.customer,
                    deposit_amount: values.deposit_amount || 0
                },
                callback: function(r) {
                    if (r.message) {
                        dialog.hide();
                        cur_frm.reload_doc();
                        frappe.msgprint(__('Car reserved successfully'));
                    }
                }
            });
        }
    });
    
    dialog.show();
}

function displayCarInsights(frm) {
    // عرض معلومات إضافية عن السيارة
    if (frm.doc.purchase_price && frm.doc.selling_price) {
        let profit = frm.doc.selling_price - frm.doc.purchase_price;
        let profit_percentage = ((profit / frm.doc.purchase_price) * 100).toFixed(2);
        
        let insight_html = `
            <div class="car-insights alert alert-info">
                <h4><i class="fa fa-info-circle"></i> Car Insights</h4>
                <div class="row">
                    <div class="col-md-4">
                        <strong>Expected Profit:</strong><br>
                        <span class="text-success">${format_currency(profit)}</span>
                    </div>
                    <div class="col-md-4">
                        <strong>Profit Margin:</strong><br>
                        <span class="text-info">${profit_percentage}%</span>
                    </div>
                    <div class="col-md-4">
                        <strong>Days in Inventory:</strong><br>
                        <span class="text-warning">${getDaysInInventory(frm.doc.acquisition_date)}</span>
                    </div>
                </div>
            </div>
        `;
        
        // إزالة المعلومات السابقة وإضافة الجديدة
        $('.car-insights').remove();
        frm.layout.wrapper.find('.form-layout').prepend(insight_html);
    }
}

function getDaysInInventory(acquisition_date) {
    if (!acquisition_date) return 'N/A';
    
    let today = new Date();
    let acquired = new Date(acquisition_date);
    let diffTime = Math.abs(today - acquired);
    let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays + ' days';
}

function addQuickFilters() {
    // إضافة مرشحات سريعة لقائمة السيارات
    if (cur_list && cur_list.doctype === 'Car Record') {
        // مرشح السيارات المتاحة
        cur_list.page.add_button(__('Available Cars'), function() {
            cur_list.filter_area.add([
                [cur_list.doctype, 'status', '=', 'Available']
            ]);
        });
        
        // مرشح السيارات المباعة
        cur_list.page.add_button(__('Sold Cars'), function() {
            cur_list.filter_area.add([
                [cur_list.doctype, 'status', '=', 'Sold']
            ]);
        });
        
        // مرشح السيارات الفاخرة
        cur_list.page.add_button(__('Luxury Cars'), function() {
            cur_list.filter_area.add([
                [cur_list.doctype, 'selling_price', '>', 100000]
            ]);
        });
    }
}

function addBulkActions() {
    // إضافة إجراءات جماعية
    if (cur_list && cur_list.doctype === 'Car Record') {
        cur_list.page.add_actions_menu_item(__('Bulk Price Update'), function() {
            let selected = cur_list.get_checked_items();
            if (selected.length === 0) {
                frappe.msgprint(__('Please select cars to update'));
                return;
            }
            
            // حوار تحديث الأسعار بالجملة
            bulkPriceUpdateDialog(selected);
        });
    }
}

function bulkPriceUpdateDialog(selected_cars) {
    // حوار تحديث الأسعار بالجملة
    let dialog = new frappe.ui.Dialog({
        title: __('Bulk Price Update'),
        fields: [
            {
                fieldtype: 'Select',
                fieldname: 'update_type',
                label: __('Update Type'),
                options: 'Percentage Increase\nPercentage Decrease\nFixed Amount Increase\nFixed Amount Decrease',
                reqd: 1
            },
            {
                fieldtype: 'Float',
                fieldname: 'update_value',
                label: __('Value'),
                reqd: 1
            }
        ],
        primary_action_label: __('Update Prices'),
        primary_action: function(values) {
            frappe.call({
                method: 'erpsmart_car.api.car_record.bulk_price_update',
                args: {
                    cars: selected_cars.map(car => car.name),
                    update_type: values.update_type,
                    update_value: values.update_value
                },
                callback: function(r) {
                    if (r.message) {
                        dialog.hide();
                        cur_list.refresh();
                        frappe.msgprint(__('Prices updated successfully'));
                    }
                }
            });
        }
    });
    
    dialog.show();
}

// وظائف مساعدة
function format_currency(amount) {
    return frappe.format(amount, {fieldtype: 'Currency'});
}