/* ERP Smart Car - Custom CSS Styles */
/* ملف التصميم المخصص لتطبيق إدارة معرض السيارات */

/* تصميم عام للتطبيق */
.car-showroom-app {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* تصميم بطاقات السيارات */
.car-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 15px 0;
    color: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.car-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.car-card .car-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.car-card .car-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 15px 0;
}

.car-card .car-price {
    font-size: 20px;
    font-weight: bold;
    color: #FFD700;
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 10px;
    border-radius: 10px;
    margin: 10px 0;
}

/* شارات الحالة */
.status-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    display: inline-block;
    margin: 5px;
}

.status-available {
    background-color: #28a745;
    color: white;
}

.status-sold {
    background-color: #dc3545;
    color: white;
}

.status-reserved {
    background-color: #ffc107;
    color: #212529;
}

.status-maintenance {
    background-color: #17a2b8;
    color: white;
}

/* تصميم النماذج المخصصة */
.luxury-form {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 20px;
    margin: 10px 0;
    border: 1px solid #dee2e6;
}

.luxury-form .form-section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تصميم الجداول */
.car-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.car-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.car-table th, .car-table td {
    padding: 12px 15px;
    text-align: center;
}

.car-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* أزرار مخصصة */
.btn-car-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-car-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-car-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-car-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    color: white;
}

/* لوحة المعلومات */
.car-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.dashboard-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.dashboard-card .card-icon {
    font-size: 40px;
    color: #667eea;
    margin-bottom: 10px;
}

.dashboard-card .card-title {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.dashboard-card .card-value {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
}

/* تصميم المعارض */
.car-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.car-gallery img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 10px;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.car-gallery img:hover {
    transform: scale(1.05);
}

/* تصميم الميزات */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.feature-item {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 20px;
    text-align: center;
    font-size: 14px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.feature-item.premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #212529;
    font-weight: bold;
}

/* تصميم الإشعارات */
.car-alert {
    border-left: 4px solid #667eea;
    background: #f8f9fa;
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
}

.car-alert.success {
    border-left-color: #28a745;
    background: #d4edda;
    color: #155724;
}

.car-alert.warning {
    border-left-color: #ffc107;
    background: #fff3cd;
    color: #856404;
}

.car-alert.danger {
    border-left-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

/* تصميم النوافذ المنبثقة */
.car-modal .modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.car-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.car-modal .modal-body {
    padding: 30px;
}

/* تصميم النماذج المتقدمة */
.advanced-form {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.advanced-form .form-group {
    margin-bottom: 20px;
}

.advanced-form label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

.advanced-form .form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.3s ease;
}

.advanced-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* تصميم الطباعة */
@media print {
    .car-card {
        background: white !important;
        color: #333 !important;
        border: 2px solid #667eea;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .car-dashboard {
        grid-template-columns: 1fr;
    }
    
    .car-card .car-details {
        grid-template-columns: 1fr;
    }
    
    .car-gallery {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* تحسينات إضافية */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تصميم الأيقونات */
.car-icon {
    color: #667eea;
    margin-right: 8px;
}

.luxury-icon {
    color: #FFD700;
    margin-right: 8px;
}

.success-icon {
    color: #28a745;
    margin-right: 8px;
}

/* تخصيص frappe */
.frappe-card {
    border-radius: 10px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

.form-section .section-head {
    background: #f8f9fa !important;
    border-radius: 8px 8px 0 0 !important;
    color: #495057 !important;
    font-weight: bold !important;
}