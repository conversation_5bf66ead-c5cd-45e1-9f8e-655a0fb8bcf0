# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from frappe import _

def get_data():
	# إعداد التطبيق وقوائم الموديول
	return [
		{
			"label": _("Car Management"),
			"icon": "fa fa-car",
			"items": [
				{
					"type": "doctype",
					"name": "Car Record",
					"label": _("Car Record"),
					"description": _("إدارة سجلات السيارات")
				},
				{
					"type": "doctype",
					"name": "Car Brand",
					"label": _("Car Brand"),
					"description": _("إدارة ماركات السيارات")
				}
			]
		},
		{
			"label": _("Sales & Marketing"),
			"icon": "fa fa-handshake-o",
			"items": [
				{
					"type": "doctype",
					"name": "Car Promotion",
					"label": _("Car Promotion"),
					"description": _("إدارة العروض والخصومات")
				},
				{
					"type": "report",
					"name": "Car Sales Analysis",
					"label": _("Car Sales Analysis"),
					"description": _("تحليل مبيعات السيارات")
				},
				{
					"type": "report",
					"name": "Car Inventory Report",
					"label": _("Car Inventory Report"),
					"description": _("تقرير مخزون السيارات")
				}
			]
		},
		{
			"label": _("Settings"),
			"icon": "fa fa-cog",
			"items": [
				{
					"type": "doctype",
					"name": "Car Showroom Settings",
					"label": _("Car Showroom Settings"),
					"description": _("إعدادات معرض السيارات")
				}
			]
		},
		{
			"label": _("Reports"),
			"icon": "fa fa-chart-line",
			"items": [
				{
					"type": "report",
					"name": "Monthly Car Sales",
					"label": _("Monthly Car Sales"),
					"description": _("مبيعات السيارات الشهرية")
				},
				{
					"type": "report",
					"name": "Car Profit Analysis",
					"label": _("Car Profit Analysis"),
					"description": _("تحليل أرباح السيارات")
				}
			]
		}
	]