# 📄 نظام الطباعة المخصص - printe_custom

## ✅ تم إنشاء النظام بنجاح!

### 🎯 الوصف
نظام طباعة مخصص لفواتير المبيعات مع QR Code متوافق مع ZATCA، مخصص لمؤسسة السرعة الفاخرة للسيارات.

## 🏗️ مكونات النظام

### 1. Print Format الجديد
**الاسم:** فاتورة ضريبية - السرعة الفاخرة
- ✅ تصميم احترافي باللغة العربية
- ✅ معلومات شركة مؤسسة السرعة الفاخرة للسيارات
- ✅ QR Code تلقائي متوافق مع ZATCA
- ✅ جدول مفصل للأصناف مع بيانات السيارات
- ✅ حساب دقيق للضرائب والإجماليات
- ✅ منطقة التوقيعات والشروط القانونية

### 2. حقول QR Code المخصصة
- **qr_zatca_data**: بيانات QR متوافقة مع ZATCA
- **qr_detailed_data**: بيانات QR تفصيلية شاملة
- **custom_plate_fees**: رسوم إصدار لوحات

### 3. تحسينات JavaScript
- أزرار طباعة مخصصة
- عرض QR Code في نافذة منبثقة
- تحديث QR Code يدوياً
- طباعة مجمعة للفواتير
- تحديث مجمع لـ QR Codes

## 📋 كيفية الاستخدام

### 1. إنشاء فاتورة مبيعات
1. اذهب إلى **Sales Invoice** → **New**
2. أدخل بيانات العميل
3. أضف الأصناف (بيانات السيارات)
4. أضف رسوم إصدار اللوحات (اختياري)
5. احفظ وأكد الفاتورة (**Submit**)

### 2. طباعة الفاتورة الضريبية
**الطريقة الأولى (الموصى بها):**
- انقر على **"فاتورة ضريبية - السرعة الفاخرة"** في قسم Print

**الطريقة الثانية:**
1. انقر على **Print** → **Print Format**
2. اختر **"فاتورة ضريبية - السرعة الفاخرة"**
3. انقر على **Print**

### 3. عرض وإدارة QR Code
- **عرض QR**: انقر على **Actions** → **"عرض QR Code"**
- **تحديث QR**: انقر على **Actions** → **"تحديث QR Code"**

### 4. العمليات المجمعة
من قائمة الفواتير:
- **طباعة مجمعة**: اختر فواتير متعددة → **"طباعة فواتير ضريبية مجمعة"**
- **تحديث QR مجمع**: اختر فواتير → **"تحديث QR Codes مجمع"**

## 🏢 معلومات الشركة المدمجة

### بيانات مؤسسة السرعة الفاخرة للسيارات
- **الاسم العربي**: مؤسسة السرعة الفاخرة للسيارات
- **الاسم الإنجليزي**: Luxury Speed Establishment Cars
- **السجل التجاري**: 7041878401
- **الرقم الضريبي**: 301377763200003
- **الهاتف**: 0555522138
- **الموقع**: المملكة العربية السعودية - جدة

## 📊 هيكل الفاتورة

```
┌─────────────────────────────────────────────┐
│     رأس الفاتورة (شعار + معلومات الشركة)      │
├─────────────────────────────────────────────┤
│              فاتورة ضريبية مبسطة            │
├─────────────────────────────────────────────┤
│  بيانات العميل     │    رقم الفاتورة والتاريخ │
├─────────────────────────────────────────────┤
│                QR Code                      │
├─────────────────────────────────────────────┤
│              جدول الأصناف                  │
│  (م، رقم الهيكل، الصنف، اللون، الموديل،    │
│   الكمية، السعر، الإجماليات، الضريبة)       │
├─────────────────────────────────────────────┤
│    الإجماليات    │    رسوم ومدفوعات       │
├─────────────────────────────────────────────┤
│       ذيل الفاتورة (التوقيعات والشروط)       │
└─────────────────────────────────────────────┘
```

## 🔢 بيانات QR Code

### تنسيق ZATCA
```
مؤسسة السرعة الفاخرة للسيارات|301377763200003|2024-01-01T12:00:00|1000.00|150.00
```

### المكونات
1. اسم الشركة
2. الرقم الضريبي
3. تاريخ ووقت الفاتورة
4. إجمالي المبلغ
5. قيمة الضريبة

## 🎨 الميزات التقنية

### التصميم
- تخطيط من اليمين إلى اليسار (RTL)
- خطوط عربية واضحة
- ألوان احترافية
- تنسيق مُحسن للطباعة

### QR Code
- متوافق مع ZATCA
- يدعم جميع المتصفحات
- تحديث تلقائي عند تأكيد الفاتورة
- إمكانية التحديث اليدوي

### JavaScript
- تحسين تجربة المستخدم
- أزرار مخصصة
- رسائل تأكيد وإرشاد
- معالجة أخطاء شاملة

## 📈 إحصائيات النظام

- **Print Formats**: 11 تنسيق متوفر
- **حقول QR**: 2 حقل مخصص
- **الفواتير مع QR**: يتم التحديث تلقائياً

## 🔧 استكشاف الأخطاء

### مشكلة: QR Code لا يظهر
**الأسباب المحتملة:**
1. الفاتورة غير مؤكدة
2. مشكلة في الاتصال بالإنترنت
3. خطأ في تحميل مكتبة QR

**الحلول:**
1. تأكد من تأكيد الفاتورة (Submit)
2. استخدم "تحديث QR Code" من الأزرار
3. تحديث الصفحة

### مشكلة: Print Format غير موجود
**الحل:**
```bash
bench --site site1.local execute printe_custom.tax_invoice.install_print_custom_system
```

### مشكلة: JavaScript لا يعمل
**الحل:**
```bash
bench build --app printe_custom
bench --site site1.local clear-cache
```

## 🚀 تحديثات مستقبلية

### المخطط إضافته
- دعم عدة شركات
- تخصيص ألوان الفاتورة
- تصدير QR كصورة منفصلة
- تقارير إحصائية للفواتير

## 📞 الدعم الفني

### فحص حالة النظام
```bash
bench --site site1.local execute printe_custom.tax_invoice.check_print_custom_status
```

### تحديث مجمع للفواتير القديمة
```bash
bench --site site1.local execute printe_custom.tax_invoice.bulk_update_qr_codes
```

## 📁 هيكل الملفات

```
printe_custom/
├── hooks.py                    # إعدادات التطبيق
├── tax_invoice.py             # نظام الفاتورة الضريبية
├── public/js/
│   └── sales_invoice_print.js # تحسينات JavaScript
└── PRINT_SYSTEM_GUIDE.md     # هذا الدليل
```

## 📄 الترخيص
MIT License - يمكن التعديل والتوزيع بحرية

---

**🎉 تهانينا! نظام الطباعة المخصص جاهز للاستخدام مع فاتورة ضريبية احترافية!**

التاريخ: $(date)  
الحالة: جاهز للإنتاج ✅  
التطبيق: printe_custom  
Print Format: فاتورة ضريبية - السرعة الفاخرة