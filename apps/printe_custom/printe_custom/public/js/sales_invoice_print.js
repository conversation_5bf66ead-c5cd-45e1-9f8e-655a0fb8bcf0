// تحسينات الطباعة لفواتير المبيعات

frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // إضافة أزرار الطباعة المخصصة
        if (frm.doc.docstatus === 1) {
            
            // زر الفاتورة الضريبية
            frm.add_custom_button(__('فاتورة ضريبية - السرعة الفاخرة'), function() {
                print_tax_invoice(frm);
            }, __('Print'));
        }
        
        // عرض معلومات رسوم اللوحات
        if (frm.doc.custom_plate_fees) {
            frm.dashboard.add_comment(
                __('رسوم إصدار لوحات: {0}', [format_currency(frm.doc.custom_plate_fees)]),
                'blue'
            );
        }
    }
});

function print_tax_invoice(frm) {
    // طباعة الفاتورة الضريبية العادية
    frappe.ui.get_print_settings(false, function(print_settings) {
        var w = window.open(
            frappe.urllib.get_full_url("/printview")
            + "?doctype=" + encodeURIComponent(frm.doc.doctype)
            + "&name=" + encodeURIComponent(frm.doc.name)
            + "&print_format=" + encodeURIComponent("فاتورة ضريبية - السرعة الفاخرة")
            + "&no_letterhead=0"
            + "&letterhead=" + encodeURIComponent(print_settings.letterhead || "")
            + "&settings=" + encodeURIComponent(JSON.stringify(print_settings))
        );
        
        if (!w) {
            frappe.msgprint(__("يرجى السماح بالنوافذ المنبثقة للطباعة"));
        }
    });
}