{"charts": [], "content": "[{\"id\":\"aCk49ShVRs\",\"type\":\"onboarding\",\"data\":{\"onboarding_name\":\"Home\",\"col\":12}},{\"id\":\"kb3XPLg8lb\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>اختصاراتك</b></span>\",\"col\":12}},{\"id\":\"nWd2KJPW8l\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Item\",\"col\":3}},{\"id\":\"snrzfbFr5Y\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Customer\",\"col\":3}},{\"id\":\"SHJKakmLLf\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Supplier\",\"col\":3}},{\"id\":\"CPxEyhaf3G\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Sales Invoice\",\"col\":3}},{\"id\":\"WU4F-HUcIQ\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Leaderboard\",\"col\":3}},{\"id\":\"NZV6m9njgs\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"تنزيل نسخ من قاعدة البيانات\",\"col\":3}},{\"id\":\"d_KVM1gsf9\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"JVu8-FJZCu\",\"type\":\"header\",\"data\":{\"text\":\"<span class=\\\"h4\\\"><b>Reports &amp; Masters</b></span>\",\"col\":12}},{\"id\":\"JiuSi0ubOg\",\"type\":\"card\",\"data\":{\"card_name\":\"Accounting\",\"col\":4}},{\"id\":\"ji2Jlm3Q8i\",\"type\":\"card\",\"data\":{\"card_name\":\"Stock\",\"col\":4}},{\"id\":\"N61oiXpuwK\",\"type\":\"card\",\"data\":{\"card_name\":\"CRM\",\"col\":4}},{\"id\":\"6J0CVl1mPo\",\"type\":\"card\",\"data\":{\"card_name\":\"Data Import and Settings\",\"col\":4}}]", "creation": "2020-01-23 13:46:38.833076", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "getting-started", "idx": 0, "is_hidden": 0, "label": "Home", "links": [{"hidden": 0, "is_query_report": 0, "label": "Accounting", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Chart of Accounts", "link_count": 0, "link_to": "Account", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Company", "link_count": 0, "link_to": "Company", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Customer", "link_count": 0, "link_to": "Customer", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Supplier", "link_count": 0, "link_to": "Supplier", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Stock", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON>", "link_count": 0, "link_to": "<PERSON><PERSON>", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Warehouse", "link_count": 0, "link_to": "Warehouse", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Brand", "link_count": 0, "link_to": "Brand", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Unit of Measure (UOM)", "link_count": 0, "link_to": "UOM", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Stock Reconciliation", "link_count": 0, "link_to": "Stock Reconciliation", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "CRM", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Lead", "link_count": 0, "link_to": "Lead", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Customer Group", "link_count": 0, "link_to": "Customer Group", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Territory", "link_count": 0, "link_to": "Territory", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Data Import and Settings", "link_count": 0, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Import Data", "link_count": 0, "link_to": "Data Import", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Opening Invoice Creation Tool", "link_count": 0, "link_to": "Opening Invoice Creation Tool", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Chart of Accounts Importer", "link_count": 0, "link_to": "Chart of Accounts Importer", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Letter Head", "link_count": 0, "link_to": "Letter Head", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON> Account", "link_count": 0, "link_to": "<PERSON><PERSON> Account", "link_type": "DocType", "onboard": 1, "type": "Link"}], "modified": "2025-05-31 17:13:08.718319", "modified_by": "Administrator", "module": "Setup", "name": "Home", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 1.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "تنزيل نسخ من قاعدة البيانات", "link_to": "Download Backup", "type": "DocType"}, {"label": "<PERSON><PERSON>", "link_to": "<PERSON><PERSON>", "type": "DocType"}, {"label": "Customer", "link_to": "Customer", "type": "DocType"}, {"label": "Supplier", "link_to": "Supplier", "type": "DocType"}, {"label": "Sales Invoice", "link_to": "Sales Invoice", "type": "DocType"}, {"label": "Leaderboard", "link_to": "leaderboard", "type": "Page"}], "title": "Home"}