name: Server

on:
  pull_request:
  workflow_dispatch:
  schedule:
    # Run everday at midnight UTC / 5:30 IST
    - cron: "0 0 * * *"

concurrency:
  group: server-develop-${{ github.event_name }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  # Do not change this as GITHUB_TOKEN is being used by roulette
  contents: read

jobs:
  checkrun:
    name: Build Check
    runs-on: ubuntu-latest

    outputs:
      build: ${{ steps.check-build.outputs.build }}

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Check if build should be run
        id: check-build
        run: |
          python "${GITHUB_WORKSPACE}/.github/helper/roulette.py"
        env:
          TYPE: "server"
          PR_NUMBER: ${{ github.event.number }}
          REPO_NAME: ${{ github.repository }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: checkrun
    if: ${{ needs.checkrun.outputs.build == 'strawberry' }}
    timeout-minutes: 30
    env:
      NODE_ENV: "production"

    strategy:
      fail-fast: false

    services:
      mariadb:
        image: mariadb:10.6
        env:
          MARIADB_ROOT_PASSWORD: travis
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3

      postgres:
        image: postgres:12.4
        env:
          POSTGRES_PASSWORD: travis
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      smtp_server:
        image: rnwood/smtp4dev:3.7.1
        ports:
          - 2525:25
          - 3000:80

    steps:
      - name: Clone
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Check for valid Python & Merge Conflicts
        run: |
          python -m compileall -q -f "${GITHUB_WORKSPACE}"
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          check-latest: true

      - name: Add to Hosts
        run: |
          echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml', '**/setup.py') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dependencies
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install_dependencies.sh
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          BEFORE: ${{ env.GITHUB_EVENT_PATH.before }}
          AFTER: ${{ env.GITHUB_EVENT_PATH.after }}
          TYPE: server
          DB: 'mariadb'

      - name: Run Tests
        run: bench --site test_site run-parallel-tests
        working-directory: /home/<USER>/frappe-bench

      - name: Show bench output
        if: ${{ always() }}
        run: |
          cd ~/frappe-bench
          cat bench_start.log || true
          cd logs
          for f in ./*.log*; do
            echo "Printing log: $f";
            cat $f
          done

      - name: Setup tmate session
        uses: mxschmitt/action-tmate@v3
        if: ${{ contains( github.event.pull_request.labels.*.name, 'debug-gha') }}

  # This is required because github still doesn't understand knowingly skipped tests
  faux-test:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: checkrun
    if: ${{ needs.checkrun.outputs.build != 'strawberry' }}

    steps:
      - name: Pass skipped tests unconditionally
        run: "echo Skipped"
