{"actions": [], "autoname": "DL.####", "creation": "2013-01-29 17:55:08", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["details_tab", "doc_type", "properties", "label", "search_fields", "link_filters", "column_break_5", "istable", "is_calendar_and_gantt", "editable_grid", "quick_entry", "track_changes", "track_views", "allow_auto_repeat", "allow_import", "queue_in_background", "naming_section", "naming_rule", "autoname", "form_settings_section", "image_field", "max_attachments", "column_break_21", "allow_copy", "make_attachments_public", "protect_attached_files", "view_settings_section", "title_field", "show_title_field_in_link", "translated_doctype", "default_print_format", "default_view", "force_re_route_to_default_view", "column_break_29", "show_preview_popup", "email_settings_section", "default_email_template", "column_break_26", "email_append_to", "sender_field", "sender_name_field", "subject_field", "section_break_8", "sort_field", "column_break_10", "sort_order", "document_actions_section", "actions", "document_links_section", "links", "document_states_section", "states", "fields_section_break", "fields", "form_tab", "form_builder"], "fields": [{"fieldname": "naming_rule", "fieldtype": "Select", "label": "Naming Rule", "length": 40, "options": "\nSet by user\nBy fieldname\nBy \"Naming Series\" field\nExpression\nExpression (old style)\nRandom\nBy script"}, {"fieldname": "doc_type", "fieldtype": "Link", "in_list_view": 1, "label": "Enter Form Type", "options": "DocType"}, {"depends_on": "doc_type", "fieldname": "properties", "fieldtype": "Section Break"}, {"fieldname": "label", "fieldtype": "Data", "label": "Change Label (via Custom Translation)"}, {"fieldname": "default_print_format", "fieldtype": "Link", "in_list_view": 1, "label": "Default Print Format", "options": "Print Format"}, {"fieldname": "max_attachments", "fieldtype": "Int", "label": "<PERSON> Attachments"}, {"default": "0", "fieldname": "allow_copy", "fieldtype": "Check", "label": "<PERSON><PERSON>py"}, {"default": "0", "fieldname": "istable", "fieldtype": "Check", "label": "Is Table", "read_only": 1}, {"default": "0", "depends_on": "istable", "fieldname": "editable_grid", "fieldtype": "Check", "label": "Editable <PERSON><PERSON>"}, {"default": "1", "fieldname": "quick_entry", "fieldtype": "Check", "label": "Quick Entry"}, {"default": "0", "fieldname": "track_changes", "fieldtype": "Check", "label": "Track Changes"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"description": "Use this fieldname to generate title", "fieldname": "title_field", "fieldtype": "Data", "label": "Title Field"}, {"description": "Must be of type \"Attach Image\"", "fieldname": "image_field", "fieldtype": "Data", "label": "Image Field"}, {"description": "Fields separated by comma (,) will be included in the \"Search By\" list of Search dialog box", "fieldname": "search_fields", "fieldtype": "Data", "in_list_view": 1, "label": "Search Fields"}, {"collapsible": 1, "depends_on": "doc_type", "fieldname": "section_break_8", "fieldtype": "Section Break", "label": "List Settings"}, {"fieldname": "sort_field", "fieldtype": "Select", "label": "Sort Field"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "sort_order", "fieldtype": "Select", "label": "Sort Order", "options": "ASC\nDESC"}, {"collapsible": 1, "depends_on": "doc_type", "fieldname": "fields_section_break", "fieldtype": "Section Break", "label": "Fields"}, {"allow_bulk_edit": 1, "fieldname": "fields", "fieldtype": "Table", "label": "Fields", "options": "Customize Form Field", "reqd": 1}, {"default": "0", "fieldname": "track_views", "fieldtype": "Check", "label": "Track Views"}, {"default": "0", "fieldname": "allow_auto_repeat", "fieldtype": "Check", "label": "Allow Auto Repeat"}, {"default": "0", "fieldname": "allow_import", "fieldtype": "Check", "label": "Allow Import (via Data Import Tool)"}, {"depends_on": "email_append_to", "fieldname": "subject_field", "fieldtype": "Data", "label": "Subject Field"}, {"depends_on": "email_append_to", "fieldname": "sender_field", "fieldtype": "Data", "label": "Sender Email <PERSON>", "mandatory_depends_on": "email_append_to"}, {"default": "0", "fieldname": "email_append_to", "fieldtype": "Check", "label": "Allow document creation via Email"}, {"default": "0", "fieldname": "show_preview_popup", "fieldtype": "Check", "label": "Show Preview Popup"}, {"collapsible": 1, "depends_on": "doc_type", "fieldname": "view_settings_section", "fieldtype": "Section Break", "label": "View Settings"}, {"fieldname": "column_break_29", "fieldtype": "Column Break"}, {"collapsible": 1, "collapsible_depends_on": "email_append_to", "depends_on": "doc_type", "fieldname": "email_settings_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON>s"}, {"collapsible": 1, "collapsible_depends_on": "links", "depends_on": "doc_type", "fieldname": "document_links_section", "fieldtype": "Section Break", "label": "Document Links"}, {"fieldname": "links", "fieldtype": "Table", "label": "Links", "options": "DocType Link"}, {"collapsible": 1, "collapsible_depends_on": "actions", "depends_on": "doc_type", "fieldname": "document_actions_section", "fieldtype": "Section Break", "label": "Document Actions"}, {"fieldname": "actions", "fieldtype": "Table", "label": "Actions", "options": "DocType Action"}, {"fieldname": "default_email_template", "fieldtype": "Link", "label": "De<PERSON>ult <PERSON>ail <PERSON>", "options": "<PERSON>ail Te<PERSON>late"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"collapsible": 1, "depends_on": "doc_type", "fieldname": "naming_section", "fieldtype": "Section Break", "label": "Naming"}, {"description": "Naming Options:\n<ol><li><b>field:[fieldname]</b> - By Field</li><li><b>naming_series:</b> - By Naming Series (field called naming_series must be present)</li><li><b>Prompt</b> - Prompt user for a name</li><li><b>[series]</b> - Series by prefix (separated by a dot); for example PRE.#####</li>\n<li><b>format:EXAMPLE-{MM}morewords{fieldname1}-{fieldname2}-{#####}</b> - Replace all braced words (fieldnames, date words (DD, MM, YY), series) with their value. Outside braces, any characters can be used.</li></ol>", "fieldname": "autoname", "fieldtype": "Data", "label": "Auto Name"}, {"collapsible": 1, "collapsible_depends_on": "states", "depends_on": "doc_type", "fieldname": "document_states_section", "fieldtype": "Section Break", "label": "Document States"}, {"fieldname": "states", "fieldtype": "Table", "label": "States", "options": "DocType State"}, {"default": "0", "fieldname": "show_title_field_in_link", "fieldtype": "Check", "label": "Show Title in Link Fields"}, {"default": "0", "fieldname": "translated_doctype", "fieldtype": "Check", "label": "Translate Link Fields"}, {"collapsible": 1, "fieldname": "form_settings_section", "fieldtype": "Section Break", "label": "Form Settings"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "make_attachments_public", "fieldtype": "Check", "label": "Make Attachments Public by De<PERSON>ult"}, {"default": "0", "description": "Enabling this will submit documents in background", "fieldname": "queue_in_background", "fieldtype": "Check", "label": "Queue in Background (BETA)"}, {"fieldname": "default_view", "fieldtype": "Select", "label": "Default View"}, {"default": "0", "depends_on": "default_view", "fieldname": "force_re_route_to_default_view", "fieldtype": "Check", "label": "Force Re-route to Default View"}, {"default": "0", "description": "Enables Calendar and Gantt views.", "fieldname": "is_calendar_and_gantt", "fieldtype": "Check", "label": "Is Calendar and Gantt"}, {"fieldname": "form_builder", "fieldtype": "HTML", "label": "Form Builder"}, {"fieldname": "form_tab", "fieldtype": "Tab Break", "label": "Form"}, {"fieldname": "link_filters", "fieldtype": "JSON", "hidden": 1, "label": "<PERSON>lters"}, {"fieldname": "details_tab", "fieldtype": "Tab Break", "label": "Details"}, {"depends_on": "email_append_to", "fieldname": "sender_name_field", "fieldtype": "Data", "label": "Sender Name Field"}, {"default": "0", "description": "Users are only able to delete attached files if the document is either in draft or if the document is canceled and they are also able to delete the document.", "fieldname": "protect_attached_files", "fieldtype": "Check", "label": "Protect Attached Files"}], "hide_toolbar": 1, "icon": "fa fa-glass", "idx": 1, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2025-03-27 18:22:32.618603", "modified_by": "Administrator", "module": "Custom", "name": "Customize Form", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "search_fields": "doc_type", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}