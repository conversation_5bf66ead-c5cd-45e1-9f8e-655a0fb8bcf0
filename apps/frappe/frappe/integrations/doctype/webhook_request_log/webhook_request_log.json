{"actions": [], "autoname": "hash", "creation": "2021-05-24 21:35:59.104776", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["webhook", "reference_document", "headers", "data", "column_break_4", "user", "url", "response", "error"], "fields": [{"fieldname": "url", "fieldtype": "Text", "label": "URL", "read_only": 1}, {"fieldname": "headers", "fieldtype": "Code", "label": "Headers", "options": "JSON", "read_only": 1}, {"fieldname": "response", "fieldtype": "Code", "label": "Response", "options": "JSON", "read_only": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "data", "fieldtype": "Code", "label": "Data", "options": "JSON", "read_only": 1}, {"fieldname": "user", "fieldtype": "Link", "label": "User", "options": "User", "read_only": 1}, {"fieldname": "reference_document", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference Document", "read_only": 1}, {"fieldname": "error", "fieldtype": "Text", "label": "Error", "read_only": 1}, {"fieldname": "webhook", "fieldtype": "Link", "label": "Webhook", "options": "Webhook"}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2025-02-25 15:16:24.028963", "modified_by": "Administrator", "module": "Integrations", "name": "Webhook Request Log", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}