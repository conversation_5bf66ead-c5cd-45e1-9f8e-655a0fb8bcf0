/* Works on Firefox */
* {
	scrollbar-width: thin;
	scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);
}

html {
	scrollbar-width: auto;
	scrollbar-gutter: stable;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar-thumb {
	background: var(--scrollbar-thumb-color);
	border-radius: 6px;
}

*::-webkit-scrollbar-track,
*::-webkit-scrollbar-corner {
	background: var(--scrollbar-track-color);
}

*:not(#page-form-builder *)::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

body::-webkit-scrollbar {
	width: 12px !important;
	height: 12px !important;
}
