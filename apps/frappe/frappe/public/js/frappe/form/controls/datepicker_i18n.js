import "air-datepicker/dist/js/i18n/datepicker.cs.js";
import "air-datepicker/dist/js/i18n/datepicker.da.js";
import "air-datepicker/dist/js/i18n/datepicker.de.js";
import "air-datepicker/dist/js/i18n/datepicker.en.js";
import "air-datepicker/dist/js/i18n/datepicker.es.js";
import "air-datepicker/dist/js/i18n/datepicker.fi.js";
import "air-datepicker/dist/js/i18n/datepicker.fr.js";
import "air-datepicker/dist/js/i18n/datepicker.hu.js";
import "air-datepicker/dist/js/i18n/datepicker.nl.js";
import "air-datepicker/dist/js/i18n/datepicker.pl.js";
import "air-datepicker/dist/js/i18n/datepicker.pt-BR.js";
import "air-datepicker/dist/js/i18n/datepicker.pt.js";
import "air-datepicker/dist/js/i18n/datepicker.ro.js";
import "air-datepicker/dist/js/i18n/datepicker.sk.js";
import "air-datepicker/dist/js/i18n/datepicker.zh.js";

(function ($) {
	$.fn.datepicker.language["ar"] = {
		days: ["الأحد", "الأثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعه", "السبت"],
		daysShort: ["الأحد", "الأثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعه", "السبت"],
		daysMin: ["الأحد", "الأثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعه", "السبت"],
		months: [
			"يناير",
			"فبراير",
			"مارس",
			"أبريل",
			"مايو",
			"يونيو",
			"يوليو",
			"أغسطس",
			"سبتمبر",
			"اكتوبر",
			"نوفمبر",
			"ديسمبر",
		],
		monthsShort: [
			"يناير",
			"فبراير",
			"مارس",
			"أبريل",
			"مايو",
			"يونيو",
			"يوليو",
			"أغسطس",
			"سبتمبر",
			"اكتوبر",
			"نوفمبر",
			"ديسمبر",
		],
		today: "اليوم",
		clear: "حذف",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii aa",
		firstDay: 6,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["gr"] = {
		days: ["Κυριακή", "Δευτέρα", "Τρίτη", "Τετάρτη", "Πέμπτη", "Παρασκευή", "Σάββατο"],
		daysShort: ["Κυρ", "Δευ", "Τρι", "Τετ", "Πεμ", "Παρ", "Σαβ"],
		daysMin: ["Κυ", "Δε", "Τρ", "Τε", "Πε", "Πα", "Σα"],
		months: [
			"Ιανουάριος",
			"Φεβρουάριος",
			"Μάρτιος",
			"Απρίλιος",
			"Μάιος",
			"Ιούνιος",
			"Ιούλιος",
			"Αύγουστος",
			"Σεπτέμβριος",
			"Οκτώβριος",
			"Νοέμβριος",
			"Δεκέμβριος",
		],
		monthsShort: [
			"Ιαν",
			"Φεβ",
			"Μαρ",
			"Απρ",
			"Μάι",
			"Ι/ν",
			"Ι/λ",
			"Αυγ",
			"Σεπ",
			"Οκτ",
			"Νοε",
			"Δεκ",
		],
		today: "Σήμερα",
		clear: "Καθαρισμός",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii aa",
		firstDay: 0,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["it"] = {
		days: ["Domenica", "Lunedì", "Martedì", "Mercoledì", "Giovedì", "Venerdì", "Sabato"],
		daysShort: ["Dom", "Lun", "Mar", "Mer", "Gio", "Ven", "Sab"],
		daysMin: ["Do", "Lu", "Ma", "Me", "Gi", "Ve", "Sa"],
		months: [
			"Gennaio",
			"Febbraio",
			"Marzo",
			"Aprile",
			"Maggio",
			"Giugno",
			"Luglio",
			"Agosto",
			"Settembre",
			"Ottobre",
			"Novembre",
			"Dicembre",
		],
		monthsShort: [
			"Gen",
			"Feb",
			"Mar",
			"Apr",
			"Mag",
			"Giu",
			"Lug",
			"Ago",
			"Set",
			"Ott",
			"Nov",
			"Dic",
		],
		today: "Oggi",
		clear: "Reset",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["tr"] = {
		days: ["Pazar", "Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"],
		daysShort: ["Paz", "Pzt", "Sal", "Çar", "Per", "Cum", "Cmt"],
		daysMin: ["Pz", "Pt", "Sa", "Ça", "Pe", "Cu", "Ct"],
		months: [
			"Ocak",
			"Şubat",
			"Mart",
			"Nisan",
			"Mayıs",
			"Haziran",
			"Temmuz",
			"Ağustos",
			"Eylül",
			"Ekim",
			"Kasım",
			"Aralık",
		],
		monthsShort: [
			"Oca",
			"Şub",
			"Mar",
			"Nis",
			"May",
			"Haz",
			"Tem",
			"Ağu",
			"Eyl",
			"Eki",
			"Kas",
			"Ara",
		],
		today: "Bugün",
		clear: "Temizle",
		dateFormat: "dd.mm.yyyy",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["sv"] = {
		days: ["Söndag", "Måndag", "Tisdag", "Onsdag", "Torsdag", "Fredag", "Lördag"],
		daysShort: ["Sön", "Mån", "Tis", "Ons", "Tor", "Fre", "Lör"],
		daysMin: ["Sö", "Må", "Ti", "On", "To", "Fr", "Lö"],
		months: [
			"Januari",
			"Februari",
			"Mars",
			"April",
			"Maj",
			"Juni",
			"Juli",
			"Augusti",
			"September",
			"Oktober",
			"November",
			"December",
		],
		monthsShort: [
			"Jan",
			"Feb",
			"Mar",
			"Apr",
			"Maj",
			"Jun",
			"Jul",
			"Aug",
			"Sep",
			"Okt",
			"Nov",
			"Dec",
		],
		today: "Idag",
		clear: "Återställ",
		dateFormat: "yyyy-mm-dd",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["bs"] = {
		days: ["Nedjelja", "Ponedjeljak", "Utorak", "Srijeda", "Četvrtak", "Petak", "Subota"],
		daysShort: ["Ned", "Pon", "Uto", "Sri", "Čet", "Pet", "Sub"],
		daysMin: ["Ne", "Po", "Ut", "Sr", "Če", "Pe", "Su"],
		months: [
			"Januar",
			"Februar",
			"Mart",
			"April",
			"Maj",
			"Juni",
			"Juli",
			"August",
			"Septembar",
			"Oktobar",
			"Novembar",
			"Decembar",
		],
		monthsShort: [
			"Jan",
			"Feb",
			"Mar",
			"Apr",
			"Maj",
			"Jun",
			"Jul",
			"Aug",
			"Sep",
			"Okt",
			"Nov",
			"Dec",
		],
		today: "Danas",
		clear: "Resetiraj",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["hr"] = {
		days: ["Nedjelja", "Ponedjeljak", "Utorak", "Srijeda", "Četvrtak", "Petak", "Subota"],
		daysShort: ["Ned", "Pon", "Uto", "Sri", "Čet", "Pet", "Sub"],
		daysMin: ["Ne", "Po", "Ut", "Sr", "Če", "Pe", "Su"],
		months: [
			"Sječanj",
			"Veljača",
			"Ožujak",
			"Travanj",
			"Svibanj",
			"Lipanj",
			"Srpanj",
			"Kolovoz",
			"Rujan",
			"Listopad",
			"Studeni",
			"Prosinac",
		],
		monthsShort: [
			"Sje",
			"Velj",
			"Ožu",
			"Tra",
			"Svi",
			"Lip",
			"Srp",
			"Kol",
			"Ruj",
			"Lis",
			"Stu",
			"Pro",
		],
		today: "Danas",
		clear: "Resetiraj",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["th"] = {
		days: ["อาทิตย์", "จันทร์", "อังคาร", "พุธ", "พฤหัสบดี", "ศุกร์", "เสาร์"],
		daysShort: ["อา.", "จ.", "อ.", "พ.", "พฤ.", "ศ.", "ส."],
		daysMin: ["อา.", "จ.", "อ.", "พ.", "พฤ.", "ศ.", "ส."],
		months: [
			"มกราคม",
			"กุมภาพันธ์",
			"มีนาคม",
			"เมษายน",
			"พฤษภาคม",
			"มิถุนายน",
			"กรกฎาคม",
			"สิงหาคม",
			"กันยายน",
			"ตุลาคม",
			"พฤศจิกายน",
			"ธันวาคม",
		],
		monthsShort: [
			"ม.ค.",
			"ก.พ.",
			"มี.ค.",
			"เม.ย.",
			"พ.ค.",
			"มิ.ย.",
			"ก.ค.",
			"ส.ค.",
			"ก.ย.",
			"ต.ค.",
			"พ.ย.",
			"ธ.ค.",
		],
		today: "ในวันนี้",
		clear: "เคลียร์",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii aa",
		firstDay: 0,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["sr"] = {
		days: ["Недеља", "Понедељак", "Уторак", "Среда", "Четвртак", "Петак", "Субота"],
		daysShort: ["Нед", "Пон", "Уто", "Сре", "Чет", "Пет", "Суб"],
		daysMin: ["Не", "По", "Ут", "Ср", "Че", "Пе", "Су"],
		months: [
			"Јануар",
			"Фебруар",
			"Март",
			"Април",
			"Мај",
			"Јун",
			"Јул",
			"Август",
			"Септембар",
			"Октобар",
			"Новембар",
			"Децембар",
		],
		monthsShort: [
			"Јан",
			"Феб",
			"Мар",
			"Апр",
			"Мај",
			"Јун",
			"Јул",
			"Авг",
			"Сеп",
			"Окт",
			"Нов",
			"Дец",
		],
		today: "Данас",
		clear: "Ресетуј",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);

(function ($) {
	$.fn.datepicker.language["sr-CS"] = {
		days: ["Nedelja", "Ponedeljak", "Utorak", "Sreda", "Četvrtak", "Petak", "Subota"],
		daysShort: ["Ned", "Pon", "Uto", "Sre", "Čet", "Pet", "Sub"],
		daysMin: ["Ne", "Po", "Ut", "Sr", "Če", "Pe", "Su"],
		months: [
			"Januar",
			"Februar",
			"Mart",
			"April",
			"Maj",
			"Juni",
			"Juli",
			"Avgust",
			"Septembar",
			"Oktobar",
			"Novembar",
			"Decembar",
		],
		monthsShort: [
			"Jan",
			"Feb",
			"Mar",
			"Apr",
			"Maj",
			"Jun",
			"Jul",
			"Avg",
			"Sep",
			"Okt",
			"Nov",
			"Dec",
		],
		today: "Danas",
		clear: "Resetiraj",
		dateFormat: "dd/mm/yyyy",
		timeFormat: "hh:ii",
		firstDay: 1,
	};
})(jQuery);
