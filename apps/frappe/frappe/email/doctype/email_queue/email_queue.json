{"actions": [], "autoname": "hash", "creation": "2012-08-02 15:17:28", "description": "Email <PERSON> records.", "doctype": "DocType", "document_type": "System", "engine": "InnoDB", "field_order": ["sender", "recipients", "show_as_cc", "message", "status", "error", "message_id", "reference_doctype", "reference_name", "communication", "send_after", "priority", "add_unsubscribe_link", "unsubscribe_param", "unsubscribe_method", "expose_recipients", "attachments", "retry", "email_account"], "fields": [{"fieldname": "sender", "fieldtype": "Data", "ignore_xss_filter": 1, "label": "Sender", "options": "Email"}, {"fieldname": "recipients", "fieldtype": "Table", "label": "Recipient", "options": "Email <PERSON>ue Recipient"}, {"fieldname": "show_as_cc", "fieldtype": "Small Text", "label": "Show as cc"}, {"fieldname": "message", "fieldtype": "Code", "label": "Message"}, {"default": "Not Sent", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Not Sent\nSending\nSent\nPartially Sent\nError"}, {"depends_on": "eval:doc.error", "fieldname": "error", "fieldtype": "Code", "label": "Error"}, {"fieldname": "message_id", "fieldtype": "Small Text", "label": "Message ID", "read_only": 1}, {"fieldname": "reference_doctype", "fieldtype": "Link", "label": "Reference Document Type", "options": "DocType", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Data", "label": "Reference DocName", "read_only": 1, "search_index": 1}, {"fieldname": "communication", "fieldtype": "Link", "label": "Communication", "options": "Communication", "search_index": 1}, {"fieldname": "send_after", "fieldtype": "Datetime", "label": "Send After", "no_copy": 1, "read_only": 1}, {"default": "1", "fieldname": "priority", "fieldtype": "Int", "label": "Priority", "read_only": 1}, {"default": "1", "fieldname": "add_unsubscribe_link", "fieldtype": "Check", "label": "Add Unsubscribe Link"}, {"fieldname": "unsubscribe_param", "fieldtype": "Data", "label": "Unsubscribe Param", "read_only": 1}, {"fieldname": "unsubscribe_method", "fieldtype": "Data", "label": "Unsubscribe Method"}, {"fieldname": "expose_recipients", "fieldtype": "Data", "label": "Expose Recipients"}, {"fieldname": "attachments", "fieldtype": "Code", "label": "Attachments", "read_only": 1}, {"default": "0", "fieldname": "retry", "fieldtype": "Int", "label": "Retry", "read_only": 1}, {"fieldname": "email_account", "fieldtype": "Link", "label": "<PERSON><PERSON> Account", "options": "<PERSON><PERSON> Account"}], "icon": "fa fa-envelope", "idx": 1, "in_create": 1, "links": [], "modified": "2025-02-20 19:21:09.652451", "modified_by": "Administrator", "module": "Email", "name": "<PERSON><PERSON>", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager"}], "row_format": "Compressed", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}