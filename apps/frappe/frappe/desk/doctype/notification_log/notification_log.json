{"actions": [], "creation": "2019-08-26 13:37:34.165254", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["subject", "for_user", "type", "email_content", "document_type", "read", "document_name", "attached_file", "attachment_link", "open_reference_document", "from_user", "link"], "fields": [{"fieldname": "subject", "fieldtype": "Text", "in_list_view": 1, "label": "Subject"}, {"fieldname": "for_user", "fieldtype": "Link", "hidden": 1, "label": "For User", "options": "User", "search_index": 1}, {"fieldname": "type", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Type", "options": "\nMention\nEnergy Point\nAssignment\nShare\nAlert"}, {"fieldname": "email_content", "fieldtype": "Text Editor", "label": "Message"}, {"fieldname": "document_type", "fieldtype": "Link", "hidden": 1, "label": "Document Type", "options": "DocType"}, {"fieldname": "document_name", "fieldtype": "Data", "hidden": 1, "label": "Document Link", "search_index": 1}, {"fieldname": "from_user", "fieldtype": "Link", "hidden": 1, "label": "From User", "options": "User"}, {"default": "0", "fieldname": "read", "fieldtype": "Check", "hidden": 1, "ignore_user_permissions": 1, "label": "Read"}, {"fieldname": "open_reference_document", "fieldtype": "<PERSON><PERSON>", "label": "Open Reference Document"}, {"fieldname": "attached_file", "fieldtype": "Code", "hidden": 1, "label": "Attached File", "options": "JSON"}, {"fieldname": "attachment_link", "fieldtype": "HTML", "label": "Attachment Link"}, {"fieldname": "link", "fieldtype": "Small Text", "hidden": 1, "label": "Link"}], "hide_toolbar": 1, "in_create": 1, "links": [], "modified": "2025-05-30 20:17:44.969738", "modified_by": "Administrator", "module": "Desk", "name": "Notification Log", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "subject", "track_seen": 1}