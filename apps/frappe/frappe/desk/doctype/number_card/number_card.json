{"actions": [], "allow_rename": 1, "creation": "2020-04-15 18:06:39.444683", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["is_standard", "module", "label", "type", "report_name", "method", "function", "aggregate_function_based_on", "column_break_2", "document_type", "parent_document_type", "report_field", "report_function", "is_public", "currency", "custom_configuration_section", "filters_config", "stats_section", "show_percentage_stats", "stats_time_interval", "filters_section", "filters_json", "dynamic_filters_section", "dynamic_filters_json", "section_break_16", "color", "column_break_xtre", "background_color"], "fields": [{"depends_on": "eval: doc.type == 'Document Type'", "fieldname": "document_type", "fieldtype": "Link", "in_list_view": 1, "label": "Document Type", "mandatory_depends_on": "eval: doc.type == 'Document Type'", "options": "DocType"}, {"depends_on": "eval: doc.type == 'Document Type'", "fieldname": "function", "fieldtype": "Select", "label": "Function", "mandatory_depends_on": "eval: doc.type == 'Document Type'", "options": "Count\nSum\nAverage\nMinimum\nMaximum"}, {"depends_on": "eval: doc.type === 'Document Type' && doc.function !== 'Count'", "fieldname": "aggregate_function_based_on", "fieldtype": "Select", "label": "Aggregate Function Based On", "mandatory_depends_on": "eval: doc.function !== 'Count'"}, {"fieldname": "filters_json", "fieldtype": "Code", "label": "Filters JSON", "options": "JSON"}, {"fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "reqd": 1}, {"fieldname": "color", "fieldtype": "Color", "label": "Color"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "filters_section", "fieldtype": "Section Break", "label": "Filters Section"}, {"default": "0", "description": "This card will be available to all Users if this is set", "fieldname": "is_public", "fieldtype": "Check", "label": "Is Public"}, {"default": "1", "fieldname": "show_percentage_stats", "fieldtype": "Check", "label": "Show Percentage Stats"}, {"default": "Daily", "depends_on": "eval: doc.show_percentage_stats", "description": "Show percentage difference according to this time interval", "fieldname": "stats_time_interval", "fieldtype": "Select", "label": "Stats Time Interval", "options": "Daily\nWeekly\nMonthly\nYearly"}, {"depends_on": "eval: doc.type == 'Document Type'", "fieldname": "stats_section", "fieldtype": "Section Break", "label": "Stats"}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard", "no_copy": 1, "read_only_depends_on": "eval: !frappe.boot.developer_mode"}, {"depends_on": "eval: doc.is_standard", "fieldname": "module", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "mandatory_depends_on": "eval: doc.is_standard", "options": "<PERSON><PERSON><PERSON>"}, {"fieldname": "dynamic_filters_json", "fieldtype": "Code", "label": "Dynamic Filters JSON", "options": "JSON"}, {"fieldname": "section_break_16", "fieldtype": "Section Break"}, {"fieldname": "dynamic_filters_section", "fieldtype": "Section Break", "label": "Dynamic Filters Section"}, {"fieldname": "type", "fieldtype": "Select", "label": "Type", "options": "Document Type\nReport\nCustom"}, {"depends_on": "eval: doc.type == 'Report'", "fieldname": "report_name", "fieldtype": "Link", "label": "Report Name", "mandatory_depends_on": "eval: doc.type == 'Report'", "options": "Report"}, {"depends_on": "eval: doc.type == 'Report'", "fieldname": "report_field", "fieldtype": "Select", "label": "Field", "mandatory_depends_on": "eval: doc.type == 'Report'"}, {"depends_on": "eval: doc.type == 'Custom'", "description": "Set the path to a whitelisted function that will return the data for the number card in the format:\n\n<pre class=\"small text-muted\"><code>\n{\n\t\"value\": value,\n\t\"fieldtype\": \"Currency\",\n\t\"route_options\": {\"from_date\": \"2023-05-23\"},\n\t\"route\": [\"query-report\", \"Permitted Documents For User\"]\n}</code></pre>", "fieldname": "method", "fieldtype": "Data", "label": "Method", "mandatory_depends_on": "eval: doc.type == 'Custom'"}, {"depends_on": "eval: doc.type == 'Custom'", "fieldname": "custom_configuration_section", "fieldtype": "Section Break", "label": "Custom Configuration"}, {"description": "Set the filters here. For example:\n<pre class=\"small text-muted\"><code>\n[{\n\tfieldname: \"company\",\n\tlabel: __(\"Company\"),\n\tfieldtype: \"Link\",\n\toptions: \"Company\",\n\tdefault: frappe.defaults.get_user_default(\"Company\"),\n\treqd: 1\n},\n{\n\tfieldname: \"account\",\n\tlabel: __(\"Account\"),\n\tfieldtype: \"<PERSON>\",\n\toptions: \"Account\",\n\treqd: 1\n}]\n</code></pre>", "fieldname": "filters_config", "fieldtype": "Code", "label": "Filters Configuration", "options": "JSON"}, {"depends_on": "eval: doc.type == 'Report'", "fieldname": "report_function", "fieldtype": "Select", "label": "Function", "mandatory_depends_on": "eval: doc.type == 'Report'", "options": "Sum\nAverage\nMinimum\nMaximum"}, {"depends_on": "eval: doc.type === 'Document Type'", "description": "The document type selected is a child table, so the parent document type is required.", "fieldname": "parent_document_type", "fieldtype": "Link", "label": "Parent Document Type", "options": "DocType"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "column_break_xtre", "fieldtype": "Column Break"}, {"fieldname": "background_color", "fieldtype": "Color", "label": "Background Color"}], "links": [], "modified": "2025-05-21 10:49:51.759985", "modified_by": "Administrator", "module": "Desk", "name": "Number Card", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Dashboard Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Desk User", "share": 1}], "row_format": "Dynamic", "search_fields": "label, document_type", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "label", "track_changes": 1}