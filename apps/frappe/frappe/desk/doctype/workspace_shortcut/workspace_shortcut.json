{"actions": [], "creation": "2020-01-23 13:44:59.248426", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["type", "link_to", "url", "doc_view", "kanban_board", "column_break_4", "label", "icon", "restrict_to_domain", "report_ref_doctype", "section_break_5", "stats_filter", "column_break_3", "color", "format"], "fields": [{"fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "DocType\nReport\nPage\nDashboard\nURL", "reqd": 1}, {"depends_on": "eval:doc.type != \"URL\"", "fieldname": "link_to", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Link To", "options": "type"}, {"depends_on": "eval:doc.type == \"DocType\"", "description": "Which view of the associated DocType should this shortcut take you to?", "fieldname": "doc_view", "fieldtype": "Select", "in_list_view": 1, "label": "DocType View", "options": "\nList\nReport Builder\nDashboard\nTree\nNew\nCalendar\nKanban"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "reqd": 1}, {"depends_on": "eval:frappe.boot.developer_mode", "fieldname": "icon", "fieldtype": "Data", "label": "Icon"}, {"depends_on": "eval:frappe.boot.developer_mode", "fieldname": "restrict_to_domain", "fieldtype": "Link", "label": "Restrict to Domain", "options": "Domain"}, {"depends_on": "eval:doc.type == \"DocType\"  && frappe.boot.developer_mode", "fieldname": "section_break_5", "fieldtype": "Section Break", "label": "Count <PERSON>"}, {"fieldname": "stats_filter", "fieldtype": "Code", "label": "Count <PERSON>", "options": "JSON"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "color", "fieldtype": "Color", "label": "Color"}, {"description": "For example: {} Open", "fieldname": "format", "fieldtype": "Data", "label": "Format"}, {"depends_on": "eval:doc.type == \"URL\"", "fieldname": "url", "fieldtype": "Data", "in_list_view": 1, "label": "URL"}, {"depends_on": "eval:doc.doc_view == \"Kanban\"", "fieldname": "kanban_board", "fieldtype": "Link", "label": "Kanban Board", "options": "Kanban Board"}, {"fieldname": "report_ref_doctype", "fieldtype": "Link", "label": "Report Ref DocType", "options": "DocType", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-03-27 20:23:13.192488", "modified_by": "Administrator", "module": "Desk", "name": "Workspace Shortcut", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}