{"actions": [], "autoname": "Prompt", "creation": "2019-10-23 15:00:48.392374", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["disable_count", "disable_comment_count", "disable_sidebar_stats", "disable_auto_refresh", "allow_edit", "disable_automatic_recency_filters", "total_fields", "fields_html", "fields"], "fields": [{"default": "0", "fieldname": "disable_count", "fieldtype": "Check", "label": "Disable Count"}, {"default": "0", "fieldname": "disable_sidebar_stats", "fieldtype": "Check", "label": "Disable Sidebar Stats"}, {"default": "0", "fieldname": "disable_auto_refresh", "fieldtype": "Check", "label": "Disable Auto Refresh"}, {"fieldname": "total_fields", "fieldtype": "Select", "label": "Maximum Number of Fields", "options": "\n4\n5\n6\n7\n8\n9\n10"}, {"fieldname": "fields_html", "fieldtype": "HTML", "label": "Fields"}, {"fieldname": "fields", "fieldtype": "Code", "hidden": 1, "label": "Fields", "read_only": 1}, {"default": "0", "fieldname": "disable_comment_count", "fieldtype": "Check", "label": "Disable Comment Count"}, {"default": "0", "description": "Allow editing even if the doctype has a workflow set up.\n\nDoes nothing if a workflow isn't set up.", "fieldname": "allow_edit", "fieldtype": "Check", "label": "Allow Bulk Editing"}, {"default": "0", "fieldname": "disable_automatic_recency_filters", "fieldtype": "Check", "label": "Disable Automatic Recency Filters"}], "grid_page_length": 50, "links": [], "modified": "2025-03-12 16:28:46.073808", "modified_by": "Administrator", "module": "Desk", "name": "List View Settings", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "read_only": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}