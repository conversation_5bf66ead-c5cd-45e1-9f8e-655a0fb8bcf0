---
base_template: "templates/base.html"
no_cache: 1
---

{%- block navbar -%} {% from "frappe/templates/includes/avatar_macro.html" import avatar %} {%-
endblock -%} {%- block footer -%} {%- endblock -%} {% block content %}
<div class="container">
	<div class="apps-container">
		<div class="title">{{ _('Select an app to continue') }}</div>
		{% set appsCount = apps|length if apps|length <= 6 else 6 %}
		<div class="apps" style="grid-template-columns: repeat({{ appsCount }}, 1fr);">
			{% for app in apps %}
			<a class="app-icon" style="cursor: pointer;">
				<div class="app-logo app-name-cls" app-name="{{ app.name }}" app-route="{{ app.route }}">
					<img src="{{ app.logo }}" />
					<div
						app-name="{{ app.name }}"
						class="set-default btn btn-sm {{ '' if app.is_default else 'hidden'}}"
						title="Set as default"
					>
						<svg
							width="18"
							height="18"
							viewBox="0 0 16 16"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15ZM11.1728 5.98483C11.3484 5.77177 11.3181 5.45664 11.1051 5.28097C10.892 5.10531 10.5769 5.13563 10.4012 5.34869L6.95187 9.5324L5.61031 7.79859C5.44132 7.58019 5.12729 7.54014 4.90889 7.70912C4.69049 7.87811 4.65044 8.19215 4.81942 8.41055L6.54403 10.6394C6.63706 10.7596 6.77974 10.831 6.93174 10.8334C7.08374 10.8357 7.22856 10.7688 7.32526 10.6515L11.1728 5.98483Z"
								fill="currentColor"
							/>
						</svg>
					</div>
				</div>
				<div class="app-title">{{ app.title }}</div>
			</a>
			{% endfor %}
		</div>
		<button class="logout-btn btn btn-default">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="16"
				height="16"
				viewBox="0 0 24 24"
				fill="none"
				stroke="currentColor"
				stroke-width="1.5"
				stroke-linecap="round"
				stroke-linejoin="round"
				class="lucide lucide-log-out"
			>
				<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
				<polyline points="16 17 21 12 16 7" />
				<line x1="21" x2="9" y1="12" y2="12" />
			</svg>
			<span> {{ _('Logout') }} </span>
		</button>
	</div>
</div>
{% endblock %} {% block script %}
<script>
	$(".set-default").on("click", function (e) {
		e.preventDefault();
		var appName = $(this).attr("app-name");
		frappe.call({
			method: "frappe.apps.set_app_as_default",
			args: { app_name: appName },
			callback: function () {
				location.reload();
			},
		});
	});

	$(".app-name-cls").on("click", function (e) {
		localStorage.current_app = $(this).attr("app-name");
		localStorage.current_route = $(this).attr("app-route");

		frappe.call({
			method: "frappe.apps.get_incomplete_setup_route",
			args: { current_app: localStorage.current_app, app_route:localStorage.current_route },
			callback: function (r) {
				window.location.href = r.message;
			},
		})

	});

	$(".logout-btn").on("click", function () {
		localStorage.current_app = "";
		localStorage.current_route = "";

		frappe.call({
			method: "logout",
			callback: function () {
				window.location.href = "/login";
			},
		});
	});
</script>
{% endblock %}
