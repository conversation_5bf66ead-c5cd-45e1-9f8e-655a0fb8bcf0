{"actions": [], "creation": "2013-02-22 01:27:36", "description": "Defines actions on states and the next step and allowed roles.", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["state", "action", "next_state", "allowed", "allow_self_approval", "send_email_to_creator", "conditions", "condition", "column_break_7", "example", "workflow_build_id"], "fields": [{"fieldname": "state", "fieldtype": "Link", "in_list_view": 1, "label": "State", "options": "Workflow State", "print_width": "200px", "reqd": 1, "width": "200px"}, {"fieldname": "action", "fieldtype": "Link", "in_list_view": 1, "label": "Action", "options": "Workflow Action Master", "print_width": "200px", "reqd": 1, "width": "200px"}, {"fieldname": "next_state", "fieldtype": "Link", "in_list_view": 1, "label": "Next State", "options": "Workflow State", "print_width": "200px", "reqd": 1, "width": "200px"}, {"fieldname": "allowed", "fieldtype": "Link", "in_list_view": 1, "label": "Allowed", "options": "Role", "print_width": "200px", "reqd": 1, "width": "200px"}, {"default": "1", "description": "Allow approval for creator of the document", "fieldname": "allow_self_approval", "fieldtype": "Check", "label": "Allow Self Approval"}, {"fieldname": "conditions", "fieldtype": "Section Break", "label": "Conditions"}, {"fieldname": "condition", "fieldtype": "Code", "label": "Condition", "options": "Python"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "example", "fieldtype": "HTML", "label": "Example", "options": "<pre><code>doc.grand_total &gt; 0</code></pre>\n\n<p>Conditions should be written in simple Python. Please use properties available in the form only.</p>\n<p>Allowed functions:\n</p><ul>\n<li>frappe.db.get_value</li>\n<li>frappe.db.get_list</li>\n<li>frappe.session</li>\n<li>frappe.utils.now_datetime</li>\n<li>frappe.utils.get_datetime</li>\n<li>frappe.utils.add_to_date</li>\n<li>frappe.utils.now</li>\n</ul>\n<p>Example: </p><pre><code>doc.creation &gt; frappe.utils.add_to_date(frappe.utils.now_datetime(), days=-5, as_string=True, as_datetime=True) </code></pre><p></p>"}, {"fieldname": "workflow_builder_id", "fieldtype": "Data", "hidden": 1, "label": "Workflow Builder ID"}, {"default": "0", "depends_on": "eval: doc.allow_self_approval == 1", "fieldname": "send_email_to_creator", "fieldtype": "Check", "label": "Send Email To Creator"}], "idx": 1, "istable": 1, "links": [], "modified": "2025-03-24 02:47:44.188152", "modified_by": "Administrator", "module": "Workflow", "name": "Workflow Transition", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}