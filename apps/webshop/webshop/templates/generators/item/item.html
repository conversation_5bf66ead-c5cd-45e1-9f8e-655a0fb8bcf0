{% extends "templates/web.html" %}
{% from "webshop/templates/includes/macros.html" import recommended_item_row %}

{% block title %} {{ title }} {% endblock %}

{% block breadcrumbs %}
<div class="item-breadcrumbs small text-muted">
	{% include "templates/includes/breadcrumbs.html" %}
</div>
{% endblock %}

{% block page_content %}
<div class="product-container item-main">
	{% from "webshop/templates/includes/macros.html" import product_image %}
	<div class="item-content">
		<div class="product-page-content" itemscope itemtype="http://schema.org/Product">
			<!-- Image, Description, Add to Cart -->
			<div class="row mb-5">
				{% include "templates/generators/item/item_image.html" %}
				{% include "templates/generators/item/item_details.html" %}
			</div>
		</div>
	</div>
</div>

<!-- Additional Info/Reviews, Recommendations -->
<div class="d-flex">
	{% set show_recommended_items = recommended_items and shopping_cart.cart_settings.enable_recommendations %}
	{% set info_col = 'col-9' if show_recommended_items else 'col-12' %}

	{% set padding_top = 'pt-0' if (show_tabs and tabs) else '' %}

	<div class="product-container mt-4 {{ padding_top }} {{ info_col }}">
		<div class="item-content {{ 'mt-minus-2' if (show_tabs and tabs) else '' }}">
			<div class="product-page-content" itemscope itemtype="http://schema.org/Product">
				<!-- Product Specifications Table Section -->
				{% if show_tabs and tabs %}
					<div class="category-tabs">
						<!-- tabs -->
							{{ web_block("Section with Tabs", values=tabs, add_container=0,
								add_top_padding=0, add_bottom_padding=0)
							}}
					</div>
				{% elif website_specifications %}
					{% include "templates/generators/item/item_specifications.html"%}
				{% endif %}

				<!-- Advanced Custom Website Content -->
				{{ doc.website_content or '' }}

				<!-- Reviews and Comments -->
				{% if shopping_cart.cart_settings.enable_reviews and not doc.has_variants %}
					{% include "templates/generators/item/item_reviews.html"%}
				{% endif %}
			</div>
		</div>
	</div>

	<!-- Recommended Items -->
	{% if show_recommended_items %}
		<div class="mt-4 col-3 recommended-item-section">
			<span class="recommendation-header">{{ _("Recommended") }}</span>
			<div class="product-container mt-2 recommendation-container">
				{% for item in recommended_items %}
					{{ recommended_item_row(item) }}
				{% endfor %}
			</div>
		</div>
	{% endif %}

</div>
{% endblock %}

{% block base_scripts %}
<!-- js should be loaded in body! -->
<script type="text/javascript" src="/assets/frappe/js/lib/jquery/jquery.min.js"></script>
{{ include_script("frappe-web.bundle.js") }}
{{ include_script("controls.bundle.js") }}
{{ include_script("dialog.bundle.js") }}
{% endblock %}
