{% extends "templates/web.html" %}

{% block title %} {{ _("Shopping Cart") }} {% endblock %}

{% block header %}<h3 class="shopping-cart-header mt-2 mb-6">{{ _("Shopping Cart") }}</h1>{% endblock %}

{% block header_actions %}
{% endblock %}

{% block page_content %}

{% from "templates/includes/macros.html" import item_name_and_description %}

{% if doc.items %}
<div class="cart-container">
	<div class="row m-0">
		<!-- Left section -->
		<div class="col-md-8">
			<div class="frappe-card p-5 mb-4">
				<div id="cart-error" class="alert alert-danger" style="display: none;"></div>
				<div class="cart-items-header">
					{{ _('Items') }}
				</div>
				<table class="table mt-3 cart-table">
					<thead>
						<tr>
							<th class="item-column">{{ _('Item') }}</th>
							<th width="20%">{{ _('Quantity') }}</th>
							{% if cart_settings.enable_checkout or cart_settings.show_price_in_quotation %}
								<th width="20" class="text-right column-sm-view">{{ _('Subtotal') }}</th>
							{% endif %}
							<th width="10%" class="column-sm-view"></th>
						</tr>
					</thead>
					<tbody class="cart-items">
						{% include "templates/includes/cart/cart_items.html" %}
					</tbody>

					{% if cart_settings.enable_checkout or cart_settings.show_price_in_quotation %}
						<tfoot class="cart-tax-items">
							{% include "templates/includes/cart/cart_items_total.html" %}
						</tfoot>
					{% endif %}
				</table>

				<div class="row mt-2">
					<div class="col-3">
						{% if cart_settings.enable_checkout %}
							<a class="btn btn-primary-light font-md" href="/orders">
								{{ _('Past Orders') }}
							</a>
						{% else %}
							<a class="btn btn-primary-light font-md" href="/quotations">
								{{ _('Past Quotes') }}
							</a>
						{% endif %}
					</div>
					<div class="col-9">
						{% if doc.items %}
						<div class="place-order-container">
							<a class="btn btn-primary-light mr-2 font-md" href="/all-products">
								{{ _('Continue Shopping') }}
							</a>
						</div>
						{% endif %}
					</div>
				</div>
			</div>

			<!-- Terms and Conditions -->
			{% if doc.items %}
				{% if doc.terms %}
					<div class="t-and-c-container mt-4 frappe-card">
						<h5>{{ _("Terms and Conditions") }}</h5>
						<div class="t-and-c-terms mt-2">
							{{ doc.terms }}
						</div>
					</div>
				{% endif %}
		</div>

		<!-- Right section -->
		<div class="col-md-4">
			<div class="cart-payment-addresses">
				<!-- Apply Coupon Code  -->
				{% set show_coupon_code = cart_settings.show_apply_coupon_code_in_website and cart_settings.enable_checkout %}
				{% set coupon_code = doc.coupon_code if doc.coupon_code else "" %}

				{% if show_coupon_code == 1%}
					{% if coupon_code %}
						{% include "templates/includes/cart/coupon_code.html" %}
					{% else %}
					<div class="mb-3">
						<div class="d-flex w-100">
							<input type="text" class="txtcoupon form-control mr-3 flex-grow-1 font-md" placeholder="{{ _("Enter coupon code") }}" name="txtcouponcode"  ></input>
							<button class="btn btn-secondary btn-sm bt-coupon font-md flex-shrink-0">{{ _("Apply") }}</button>
							<input type="hidden" class="txtreferral_sales_partner font-md" placeholder="Enter Sales Partner" name="txtreferral_sales_partner" type="text"></input>
							</div>
					</div>
					{% endif %}
				{% endif %}

				<div class="mb-3 frappe-card p-5">
					<div class="payment-summary">
						{% include "templates/includes/cart/cart_payment_summary.html" %}
					</div>

					<div class="place-order">
						{% include "templates/includes/cart/place_order.html" %}
					</div>
				</div>

				{% include "templates/includes/cart/cart_address.html" %}
			</div>
		</div>
		{% endif %}
	</div>
</div>
{% else %}
<div class="cart-empty frappe-card">
	<div class="cart-empty-state">
		<img src="/assets/webshop/images/cart-empty-state.png" alt="Empty State">
	</div>
	<div class="cart-empty-message mt-4">{{ _('Your cart is Empty') }}</p>
	{% if cart_settings.enable_checkout %}
		<a class="btn btn-outline-primary" href="/orders" style="font-size: 16px;">
			{{ _('See past orders') }}
		</a>
		{% else %}
		<a class="btn btn-outline-primary" href="/quotations" style="font-size: 16px;">
			{{ _('See past quotations') }}
		</a>
	{% endif %}
</div>
{% endif %}

{% endblock %}

{% block base_scripts %}
<!-- js should be loaded in body! -->
{{ include_script("frappe-web.bundle.js") }}
{{ include_script("controls.bundle.js") }}
{{ include_script("dialog.bundle.js") }}
{% endblock %}
