# 🚀 دليل التطوير الشامل على ERPNext - من الصفر إلى الاحتراف

## 📚 فهرس المحتويات

1. [مقدمة عن ERPNext و Frappe Framework](#مقدمة)
2. [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
3. [هيكل المشروع والملفات](#هيكل-المشروع)
4. [إنشاء التطبيقات والمديولات](#إنشاء-التطبيقات)
5. [أنواع الملفات وكيفية إنشائها](#أنواع-الملفات)
6. [الربط بين الملفات](#الربط-بين-الملفات)
7. [دوال Frappe الأساسية](#دوال-frappe)
8. [العمل مع قاعدة البيانات](#قاعدة-البيانات)
9. [APIs والتكامل](#apis-والتكامل)
10. [التطوير على DocTypes](#تطوير-doctypes)
11. [حل المشاكل والأخطاء](#حل-المشاكل)
12. [أمثلة عملية متقدمة](#أمثلة-عملية)

---

## 🎯 مقدمة عن ERPNext و Frappe Framework {#مقدمة}

### **ما هو ERPNext؟**
ERPNext هو نظام تخطيط موارد المؤسسات (ERP) مفتوح المصدر مبني على Frappe Framework.

### **ما هو Frappe Framework؟**
Frappe هو إطار عمل Python/JavaScript لبناء تطبيقات الويب بسرعة وسهولة.

### **المكونات الأساسية:**
- **Python**: للمنطق الخلفي (Backend)
- **JavaScript**: للواجهة الأمامية (Frontend)
- **MariaDB/MySQL**: قاعدة البيانات
- **Redis**: للتخزين المؤقت
- **HTML/CSS**: للتصميم

---

## ⚙️ إعداد بيئة التطوير {#إعداد-بيئة-التطوير}

### **المتطلبات الأساسية:**
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Python و pip
sudo apt install python3 python3-pip python3-dev -y

# تثبيت Node.js و npm
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt install nodejs -y

# تثبيت MariaDB
sudo apt install mariadb-server mariadb-client -y

# تثبيت Redis
sudo apt install redis-server -y

# تثبيت Git
sudo apt install git -y
```

### **إعداد Frappe Bench:**
```bash
# تثبيت frappe-bench
pip3 install frappe-bench

# إنشاء مجلد العمل
bench init frappe-bench --frappe-branch version-15

# الدخول إلى مجلد العمل
cd frappe-bench

# إنشاء موقع جديد
bench new-site mysite.local

# تثبيت ERPNext
bench get-app erpnext
bench --site mysite.local install-app erpnext
```

---

## 📁 هيكل المشروع والملفات {#هيكل-المشروع}

### **هيكل Frappe Bench:**
```
frappe-bench/
├── apps/                    # التطبيقات
│   ├── frappe/             # إطار العمل الأساسي
│   ├── erpnext/            # تطبيق ERPNext
│   └── custom_app/         # تطبيقك المخصص
├── sites/                  # المواقع
│   ├── mysite.local/       # موقعك
│   └── common_site_config.json
├── config/                 # ملفات الإعداد
├── logs/                   # ملفات السجلات
└── env/                    # البيئة الافتراضية
```

### **هيكل التطبيق:**
```
custom_app/
├── custom_app/
│   ├── __init__.py
│   ├── hooks.py            # ملف الربط الرئيسي
│   ├── modules.txt         # قائمة المديولات
│   ├── patches.txt         # تحديثات قاعدة البيانات
│   └── custom_module/      # مديول مخصص
│       ├── __init__.py
│       ├── doctype/        # أنواع المستندات
│       ├── page/           # الصفحات
│       ├── report/         # التقارير
│       └── dashboard/      # لوحات المعلومات
├── requirements.txt        # مكتبات Python
├── setup.py               # إعداد التطبيق
└── README.md              # وثائق التطبيق
```

---

## 🏗️ إنشاء التطبيقات والمديولات {#إنشاء-التطبيقات}

### **إنشاء تطبيق جديد:**
```bash
# إنشاء تطبيق
bench new-app custom_app

# إضافة التطبيق إلى الموقع
bench --site mysite.local install-app custom_app

# بدء التطوير
bench start
```

### **إنشاء مديول جديد:**
```bash
# الدخول إلى مجلد التطبيق
cd apps/custom_app

# إنشاء مديول
mkdir -p custom_app/custom_module
touch custom_app/custom_module/__init__.py

# إضافة المديول إلى modules.txt
echo "Custom Module" >> custom_app/modules.txt
```

### **إنشاء DocType جديد:**
```bash
# إنشاء DocType من خلال الواجهة
# أو باستخدام الأمر
bench make-doctype "Custom DocType" --module "Custom Module"
```

---

## 📄 أنواع الملفات وكيفية إنشائها {#أنواع-الملفات}

### **1. ملفات Python (.py):**

#### **إنشاء ملف Python للمنطق:**
```python
# custom_app/custom_module/utils.py

import frappe
from frappe import _

def custom_function():
    """دالة مخصصة للمعالجة"""
    try:
        # منطق العمل هنا
        result = frappe.db.get_list("User", fields=["name", "email"])
        return result
    except Exception as e:
        frappe.log_error(f"خطأ في custom_function: {str(e)}")
        return None

@frappe.whitelist()
def api_function(param1, param2):
    """دالة API يمكن استدعاؤها من JavaScript"""
    if not frappe.has_permission("User", "read"):
        frappe.throw(_("ليس لديك صلاحية"))

    # معالجة البيانات
    data = {
        "param1": param1,
        "param2": param2,
        "timestamp": frappe.utils.now()
    }

    return data

class CustomClass:
    """كلاس مخصص للمعالجة"""

    def __init__(self, doctype, name):
        self.doctype = doctype
        self.name = name
        self.doc = frappe.get_doc(doctype, name)

    def process_data(self):
        """معالجة البيانات"""
        # منطق المعالجة
        self.doc.custom_field = "قيمة محدثة"
        self.doc.save()

    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.doc.required_field:
            frappe.throw(_("الحقل المطلوب فارغ"))
```

#### **إنشاء ملف Controller للـ DocType:**
```python
# custom_app/custom_module/doctype/custom_doctype/custom_doctype.py

import frappe
from frappe.model.document import Document
from frappe import _

class CustomDoctype(Document):
    """Controller للـ DocType المخصص"""

    def validate(self):
        """التحقق قبل الحفظ"""
        self.validate_required_fields()
        self.calculate_totals()

    def before_save(self):
        """قبل الحفظ"""
        self.set_naming_series()

    def after_insert(self):
        """بعد الإنشاء"""
        self.send_notification()

    def on_submit(self):
        """عند التأكيد"""
        self.update_related_documents()

    def on_cancel(self):
        """عند الإلغاء"""
        self.reverse_entries()

    def validate_required_fields(self):
        """التحقق من الحقول المطلوبة"""
        required_fields = ["customer", "date", "amount"]
        for field in required_fields:
            if not self.get(field):
                frappe.throw(_("الحقل {0} مطلوب").format(_(field)))

    def calculate_totals(self):
        """حساب المجاميع"""
        total = 0
        for item in self.items:
            item.amount = item.qty * item.rate
            total += item.amount
        self.total_amount = total

    def send_notification(self):
        """إرسال إشعار"""
        frappe.sendmail(
            recipients=[self.customer_email],
            subject=_("تم إنشاء مستند جديد"),
            message=_("تم إنشاء المستند رقم {0}").format(self.name)
        )
```

### **2. ملفات JavaScript (.js):**

#### **إنشاء ملف JavaScript للواجهة:**
```javascript
// custom_app/custom_module/doctype/custom_doctype/custom_doctype.js

frappe.ui.form.on('Custom Doctype', {
    // عند تحميل النموذج
    refresh: function(frm) {
        // إضافة أزرار مخصصة
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button(__('طباعة'), function() {
                print_document(frm);
            });
        }

        // إخفاء/إظهار حقول
        toggle_fields(frm);

        // تطبيق فلاتر
        apply_filters(frm);
    },

    // عند تغيير حقل معين
    customer: function(frm) {
        if (frm.doc.customer) {
            get_customer_details(frm);
        }
    },

    // عند تغيير التاريخ
    date: function(frm) {
        validate_date(frm);
    },

    // قبل الحفظ
    before_save: function(frm) {
        calculate_totals(frm);
    }
});

// دالة طباعة المستند
function print_document(frm) {
    frappe.call({
        method: 'custom_app.custom_module.utils.generate_pdf',
        args: {
            doctype: frm.doc.doctype,
            name: frm.doc.name
        },
        callback: function(r) {
            if (r.message) {
                window.open(r.message);
            }
        }
    });
}

// دالة جلب تفاصيل العميل
function get_customer_details(frm) {
    frappe.call({
        method: 'frappe.client.get',
        args: {
            doctype: 'Customer',
            name: frm.doc.customer
        },
        callback: function(r) {
            if (r.message) {
                frm.set_value('customer_name', r.message.customer_name);
                frm.set_value('customer_email', r.message.email_id);
                frm.refresh_fields();
            }
        }
    });
}

// دالة التحقق من التاريخ
function validate_date(frm) {
    if (frm.doc.date < frappe.datetime.get_today()) {
        frappe.msgprint(__('لا يمكن اختيار تاريخ في الماضي'));
        frm.set_value('date', frappe.datetime.get_today());
    }
}

// دالة حساب المجاميع
function calculate_totals(frm) {
    let total = 0;

    frm.doc.items.forEach(function(item) {
        item.amount = item.qty * item.rate;
        total += item.amount;
    });

    frm.set_value('total_amount', total);
    frm.refresh_field('items');
}

// دالة تطبيق الفلاتر
function apply_filters(frm) {
    frm.set_query('customer', function() {
        return {
            filters: {
                'disabled': 0
            }
        };
    });

    frm.set_query('item_code', 'items', function() {
        return {
            filters: {
                'disabled': 0,
                'is_sales_item': 1
            }
        };
    });
}

// دالة إخفاء/إظهار الحقول
function toggle_fields(frm) {
    if (frm.doc.type === 'Service') {
        frm.toggle_display('items', false);
        frm.toggle_reqd('service_details', true);
    } else {
        frm.toggle_display('items', true);
        frm.toggle_reqd('service_details', false);
    }
}
```

### **3. ملفات HTML (.html):**

#### **إنشاء قالب طباعة:**
```html
<!-- custom_app/custom_module/print_format/custom_print/custom_print.html -->

<div class="print-format">
    <div class="header">
        <h1>{{ doc.title or doc.name }}</h1>
        <div class="company-info">
            <h3>{{ frappe.defaults.get_user_default("Company") }}</h3>
            <p>{{ frappe.get_doc("Company", frappe.defaults.get_user_default("Company")).address }}</p>
        </div>
    </div>

    <div class="document-info">
        <table class="table table-bordered">
            <tr>
                <td><strong>رقم المستند:</strong></td>
                <td>{{ doc.name }}</td>
                <td><strong>التاريخ:</strong></td>
                <td>{{ frappe.format_date(doc.date) }}</td>
            </tr>
            <tr>
                <td><strong>العميل:</strong></td>
                <td>{{ doc.customer_name }}</td>
                <td><strong>البريد الإلكتروني:</strong></td>
                <td>{{ doc.customer_email }}</td>
            </tr>
        </table>
    </div>

    {% if doc.items %}
    <div class="items-section">
        <h4>الأصناف</h4>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>المبلغ</th>
                </tr>
            </thead>
            <tbody>
                {% for item in doc.items %}
                <tr>
                    <td>{{ item.item_name }}</td>
                    <td>{{ item.qty }}</td>
                    <td>{{ frappe.format_currency(item.rate) }}</td>
                    <td>{{ frappe.format_currency(item.amount) }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="3"><strong>المجموع الكلي:</strong></td>
                    <td><strong>{{ frappe.format_currency(doc.total_amount) }}</strong></td>
                </tr>
            </tfoot>
        </table>
    </div>
    {% endif %}

    <div class="footer">
        <p>تم الإنشاء بواسطة: {{ doc.owner }}</p>
        <p>تاريخ الطباعة: {{ frappe.format_datetime(frappe.utils.now()) }}</p>
    </div>
</div>

<style>
.print-format {
    font-family: Arial, sans-serif;
    direction: rtl;
    text-align: right;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #333;
    padding-bottom: 20px;
}

.document-info, .items-section {
    margin: 20px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.footer {
    margin-top: 30px;
    text-align: center;
    font-size: 12px;
    color: #666;
}
</style>
```

### **4. ملفات JSON (.json):**

#### **إنشاء ملف إعدادات DocType:**
```json
{
 "actions": [],
 "allow_rename": 1,
 "autoname": "naming_series:",
 "creation": "2024-01-01 00:00:00.000000",
 "doctype": "DocType",
 "editable_grid": 1,
 "engine": "InnoDB",
 "field_order": [
  "naming_series",
  "title",
  "customer",
  "customer_name",
  "date",
  "section_break_5",
  "items",
  "section_break_7",
  "total_amount",
  "status"
 ],
 "fields": [
  {
   "fieldname": "naming_series",
   "fieldtype": "Select",
   "label": "سلسلة الترقيم",
   "options": "CUST-.YYYY.-",
   "reqd": 1
  },
  {
   "fieldname": "title",
   "fieldtype": "Data",
   "label": "العنوان",
   "reqd": 1
  },
  {
   "fieldname": "customer",
   "fieldtype": "Link",
   "label": "العميل",
   "options": "Customer",
   "reqd": 1
  },
  {
   "fieldname": "customer_name",
   "fieldtype": "Data",
   "label": "اسم العميل",
   "read_only": 1
  },
  {
   "fieldname": "date",
   "fieldtype": "Date",
   "label": "التاريخ",
   "reqd": 1
  },
  {
   "fieldname": "section_break_5",
   "fieldtype": "Section Break"
  },
  {
   "fieldname": "items",
   "fieldtype": "Table",
   "label": "الأصناف",
   "options": "Custom Doctype Item"
  },
  {
   "fieldname": "section_break_7",
   "fieldtype": "Section Break"
  },
  {
   "fieldname": "total_amount",
   "fieldtype": "Currency",
   "label": "المبلغ الإجمالي",
   "read_only": 1
  },
  {
   "fieldname": "status",
   "fieldtype": "Select",
   "label": "الحالة",
   "options": "Draft\nSubmitted\nCancelled"
  }
 ],
 "index_web_pages_for_search": 1,
 "is_submittable": 1,
 "links": [],
 "modified": "2024-01-01 00:00:00.000000",
 "modified_by": "Administrator",
 "module": "Custom Module",
 "name": "Custom Doctype",
 "naming_rule": "By \"Naming Series\" field",
 "owner": "Administrator",
 "permissions": [
  {
   "create": 1,
   "delete": 1,
   "email": 1,
   "export": 1,
   "print": 1,
   "read": 1,
   "report": 1,
   "role": "System Manager",
   "share": 1,
   "submit": 1,
   "write": 1
  }
 ],
 "sort_field": "modified",
 "sort_order": "DESC",
 "states": [],
 "track_changes": 1
}
```

---

## 🔗 الربط بين الملفات {#الربط-بين-الملفات}

### **1. ملف hooks.py - مركز الربط:**
```python
# custom_app/custom_app/hooks.py

from . import __version__ as app_version

app_name = "custom_app"
app_title = "Custom App"
app_publisher = "Your Company"
app_description = "تطبيق مخصص للشركة"
app_icon = "octicon octicon-file-directory"
app_color = "grey"
app_email = "<EMAIL>"
app_license = "MIT"

# تضمين ملفات CSS و JS
app_include_css = [
    "/assets/custom_app/css/custom.css"
]

app_include_js = [
    "/assets/custom_app/js/custom.js"
]

# ربط ملفات JS مع DocTypes محددة
doctype_js = {
    "Sales Invoice": "public/js/sales_invoice.js",
    "Customer": "public/js/customer.js"
}

# ربط ملفات CSS مع DocTypes محددة
doctype_css = {
    "Sales Invoice": "public/css/sales_invoice.css"
}

# ربط دوال Python مع أحداث النظام
doc_events = {
    "Sales Invoice": {
        "validate": "custom_app.custom_module.sales_invoice.validate_sales_invoice",
        "on_submit": "custom_app.custom_module.sales_invoice.on_submit_sales_invoice",
        "on_cancel": "custom_app.custom_module.sales_invoice.on_cancel_sales_invoice"
    },
    "Customer": {
        "after_insert": "custom_app.custom_module.customer.after_insert_customer"
    }
}

# ربط دوال مع أحداث النظام العامة
scheduler_events = {
    "daily": [
        "custom_app.custom_module.tasks.daily_cleanup"
    ],
    "weekly": [
        "custom_app.custom_module.tasks.weekly_report"
    ],
    "monthly": [
        "custom_app.custom_module.tasks.monthly_backup"
    ]
}

# ربط دوال مع تسجيل الدخول/الخروج
on_session_creation = [
    "custom_app.custom_module.auth.on_login"
]

on_logout = [
    "custom_app.custom_module.auth.on_logout"
]

# إضافة صلاحيات مخصصة
permission_query_conditions = {
    "Custom Doctype": "custom_app.custom_module.permissions.get_permission_query_conditions"
}

# فلاتر البيانات
has_permission = {
    "Custom Doctype": "custom_app.custom_module.permissions.has_permission"
}

# إضافة تقارير مخصصة
standard_queries = {
    "Custom Report": "custom_app.custom_module.queries.custom_report"
}

# ربط مع Jinja Templates
jinja = {
    "methods": [
        "custom_app.custom_module.utils.get_custom_data"
    ]
}

# إضافة مسارات API مخصصة
website_route_rules = [
    {"from_route": "/api/custom/<path:path>", "to_route": "custom_api"},
]

# تخصيص البريد الإلكتروني
email_brand_image = "/assets/custom_app/images/logo.png"

# إضافة fixtures (بيانات افتراضية)
fixtures = [
    {"dt": "Custom Fields", "filters": [["module", "=", "Custom Module"]]},
    {"dt": "Property Setter", "filters": [["module", "=", "Custom Module"]]},
]
```

### **2. ربط ملفات Python:**
```python
# custom_app/custom_module/sales_invoice.py

import frappe
from frappe import _
from frappe.utils import flt, getdate

def validate_sales_invoice(doc, method):
    """التحقق من فاتورة المبيعات قبل الحفظ"""
    validate_customer_credit_limit(doc)
    validate_item_availability(doc)
    calculate_custom_taxes(doc)

def on_submit_sales_invoice(doc, method):
    """عند تأكيد فاتورة المبيعات"""
    update_customer_balance(doc)
    create_loyalty_points(doc)
    send_invoice_notification(doc)

def on_cancel_sales_invoice(doc, method):
    """عند إلغاء فاتورة المبيعات"""
    reverse_customer_balance(doc)
    cancel_loyalty_points(doc)

def validate_customer_credit_limit(doc):
    """التحقق من حد الائتمان للعميل"""
    customer = frappe.get_doc("Customer", doc.customer)
    if customer.credit_limit:
        outstanding = get_customer_outstanding(doc.customer)
        if outstanding + doc.grand_total > customer.credit_limit:
            frappe.throw(_("تجاوز حد الائتمان المسموح للعميل"))

def get_customer_outstanding(customer):
    """حساب المبلغ المستحق على العميل"""
    outstanding = frappe.db.sql("""
        SELECT SUM(outstanding_amount)
        FROM `tabSales Invoice`
        WHERE customer = %s AND docstatus = 1
    """, customer)[0][0] or 0

    return flt(outstanding)
```

### **3. ربط JavaScript مع Python:**
```javascript
// custom_app/public/js/sales_invoice.js

frappe.ui.form.on('Sales Invoice', {
    customer: function(frm) {
        // استدعاء دالة Python من JavaScript
        frappe.call({
            method: 'custom_app.custom_module.sales_invoice.get_customer_details',
            args: {
                customer: frm.doc.customer
            },
            callback: function(r) {
                if (r.message) {
                    frm.set_value('customer_group', r.message.customer_group);
                    frm.set_value('territory', r.message.territory);
                }
            }
        });
    },

    validate: function(frm) {
        // التحقق من البيانات قبل الحفظ
        return new Promise((resolve) => {
            frappe.call({
                method: 'custom_app.custom_module.sales_invoice.validate_before_save',
                args: {
                    doc: frm.doc
                },
                callback: function(r) {
                    if (r.message && r.message.valid) {
                        resolve();
                    } else {
                        frappe.msgprint(r.message.error);
                        frappe.validated = false;
                    }
                }
            });
        });
    }
});
```

---

## 🐍 دوال Frappe الأساسية {#دوال-frappe}

### **1. دوال قاعدة البيانات:**

#### **القراءة من قاعدة البيانات:**
```python
# جلب مستند واحد
doc = frappe.get_doc("Customer", "CUST-001")

# جلب قيمة حقل واحد
customer_name = frappe.db.get_value("Customer", "CUST-001", "customer_name")

# جلب عدة حقول
customer_data = frappe.db.get_value("Customer", "CUST-001",
                                   ["customer_name", "email_id", "mobile_no"], as_dict=True)

# جلب قائمة مستندات
customers = frappe.db.get_list("Customer",
                              filters={"disabled": 0},
                              fields=["name", "customer_name", "customer_group"],
                              order_by="customer_name")

# استعلام SQL مخصص
result = frappe.db.sql("""
    SELECT c.name, c.customer_name, SUM(si.grand_total) as total_sales
    FROM `tabCustomer` c
    LEFT JOIN `tabSales Invoice` si ON c.name = si.customer
    WHERE c.disabled = 0 AND si.docstatus = 1
    GROUP BY c.name
    ORDER BY total_sales DESC
""", as_dict=True)

# جلب مستند واحد بالفلاتر
customer = frappe.db.get("Customer", {"email_id": "<EMAIL>"})

# التحقق من وجود مستند
exists = frappe.db.exists("Customer", {"email_id": "<EMAIL>"})

# عد المستندات
count = frappe.db.count("Customer", {"disabled": 0})
```

#### **الكتابة في قاعدة البيانات:**
```python
# إنشاء مستند جديد
customer = frappe.new_doc("Customer")
customer.customer_name = "عميل جديد"
customer.customer_type = "Individual"
customer.customer_group = "All Customer Groups"
customer.territory = "All Territories"
customer.insert()

# تحديث مستند موجود
customer = frappe.get_doc("Customer", "CUST-001")
customer.mobile_no = "123456789"
customer.save()

# تحديث قيمة مباشرة
frappe.db.set_value("Customer", "CUST-001", "mobile_no", "123456789")

# تحديث عدة قيم
frappe.db.set_value("Customer", "CUST-001", {
    "mobile_no": "123456789",
    "email_id": "<EMAIL>"
})

# حذف مستند
frappe.delete_doc("Customer", "CUST-001")

# تنفيذ SQL مخصص
frappe.db.sql("""
    UPDATE `tabCustomer`
    SET customer_group = 'VIP'
    WHERE name IN (
        SELECT customer FROM `tabSales Invoice`
        WHERE grand_total > 100000 AND docstatus = 1
    )
""")

# حفظ التغييرات
frappe.db.commit()
```

### **2. دوال المصادقة والصلاحيات:**
```python
# التحقق من تسجيل الدخول
if frappe.session.user == "Guest":
    frappe.throw(_("يجب تسجيل الدخول أولاً"))

# التحقق من الصلاحيات
if not frappe.has_permission("Customer", "create"):
    frappe.throw(_("ليس لديك صلاحية إنشاء عملاء"))

# التحقق من صلاحية مستند محدد
if not frappe.has_permission("Customer", "write", "CUST-001"):
    frappe.throw(_("ليس لديك صلاحية تعديل هذا العميل"))

# الحصول على المستخدم الحالي
current_user = frappe.session.user

# الحصول على أدوار المستخدم
user_roles = frappe.get_roles(frappe.session.user)

# التحقق من دور محدد
if "Sales Manager" not in frappe.get_roles():
    frappe.throw(_("هذه العملية تتطلب دور مدير المبيعات"))

# تسجيل الخروج
frappe.local.login_manager.logout()
```

### **3. دوال المرافق (Utilities):**
```python
# التاريخ والوقت
from frappe.utils import now, today, getdate, add_days, date_diff

current_datetime = now()  # التاريخ والوقت الحالي
current_date = today()    # التاريخ الحالي
parsed_date = getdate("2024-01-01")  # تحويل نص إلى تاريخ
future_date = add_days(today(), 30)  # إضافة 30 يوم
days_diff = date_diff(future_date, today())  # الفرق بالأيام

# التعامل مع الأرقام
from frappe.utils import flt, cint, cstr

number = flt("123.45")    # تحويل إلى رقم عشري
integer = cint("123")     # تحويل إلى رقم صحيح
string = cstr(123.45)     # تحويل إلى نص

# التعامل مع JSON
import json
data = {"name": "test", "value": 123}
json_string = json.dumps(data)
parsed_data = json.loads(json_string)

# التعامل مع الملفات
import os
file_path = frappe.get_app_path("custom_app", "templates", "test.html")
file_exists = os.path.exists(file_path)

# إنشاء URL
url = frappe.utils.get_url("/api/method/custom_app.api.test")

# تشفير كلمة المرور
from frappe.utils.password import encrypt, decrypt
encrypted = encrypt("password123")
decrypted = decrypt(encrypted)
```

### **4. دوال الرسائل والإشعارات:**
```python
# رسائل للمستخدم
frappe.msgprint(_("تم الحفظ بنجاح"))
frappe.msgprint(_("رسالة تحذيرية"), indicator="orange")
frappe.msgprint(_("رسالة خطأ"), indicator="red")

# رسائل منبثقة
frappe.show_alert(_("تم التحديث"), 3)  # تظهر لمدة 3 ثواني

# إيقاف العملية مع رسالة خطأ
frappe.throw(_("حدث خطأ في البيانات"))

# تسجيل الأخطاء
frappe.log_error("تفاصيل الخطأ", "عنوان الخطأ")

# إرسال بريد إلكتروني
frappe.sendmail(
    recipients=["<EMAIL>"],
    subject="موضوع الرسالة",
    message="محتوى الرسالة",
    attachments=[{
        "fname": "file.pdf",
        "fcontent": file_content
    }]
)

# إنشاء إشعار في النظام
frappe.publish_realtime(
    event="custom_notification",
    message={"title": "إشعار جديد", "content": "محتوى الإشعار"},
    user=frappe.session.user
)
```

### **5. دوال التخزين المؤقت:**
```python
# حفظ في التخزين المؤقت
frappe.cache().set_value("my_key", {"data": "value"}, expires_in_sec=3600)

# جلب من التخزين المؤقت
cached_data = frappe.cache().get_value("my_key")

# حذف من التخزين المؤقت
frappe.cache().delete_value("my_key")

# مسح كامل للتخزين المؤقت
frappe.clear_cache()

# تخزين مؤقت للدوال
@frappe.cache_manager.cache_result(ttl=3600)
def expensive_function():
    # عملية معقدة تستغرق وقت
    return complex_calculation()
```

---

## 🗄️ العمل مع قاعدة البيانات {#قاعدة-البيانات}

### **1. إنشاء جداول مخصصة:**
```python
# إنشاء جدول مخصص
def create_custom_table():
    if not frappe.db.table_exists("tabCustom Log"):
        frappe.db.sql("""
            CREATE TABLE `tabCustom Log` (
                `name` varchar(140) NOT NULL,
                `creation` datetime(6) DEFAULT NULL,
                `modified` datetime(6) DEFAULT NULL,
                `modified_by` varchar(140) DEFAULT NULL,
                `owner` varchar(140) DEFAULT NULL,
                `docstatus` int(1) NOT NULL DEFAULT 0,
                `idx` int(8) NOT NULL DEFAULT 0,
                `user` varchar(140) DEFAULT NULL,
                `action` varchar(140) DEFAULT NULL,
                `timestamp` datetime(6) DEFAULT NULL,
                `details` longtext,
                PRIMARY KEY (`name`),
                KEY `modified` (`modified`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        frappe.db.commit()

# إدراج بيانات في الجدول المخصص
def log_user_action(user, action, details):
    frappe.db.sql("""
        INSERT INTO `tabCustom Log`
        (name, creation, modified, owner, user, action, timestamp, details)
        VALUES (%(name)s, %(now)s, %(now)s, %(user)s, %(user)s, %(action)s, %(now)s, %(details)s)
    """, {
        "name": frappe.generate_hash(length=10),
        "now": frappe.utils.now(),
        "user": user,
        "action": action,
        "details": details
    })
    frappe.db.commit()
```

### **2. استعلامات معقدة:**
```python
# استعلام مع JOIN
def get_sales_report():
    return frappe.db.sql("""
        SELECT
            c.customer_name,
            c.customer_group,
            COUNT(si.name) as invoice_count,
            SUM(si.grand_total) as total_sales,
            AVG(si.grand_total) as avg_invoice_value,
            MAX(si.posting_date) as last_sale_date
        FROM `tabCustomer` c
        LEFT JOIN `tabSales Invoice` si ON c.name = si.customer AND si.docstatus = 1
        WHERE c.disabled = 0
        GROUP BY c.name
        HAVING total_sales > 0
        ORDER BY total_sales DESC
        LIMIT 100
    """, as_dict=True)

# استعلام مع subquery
def get_top_customers():
    return frappe.db.sql("""
        SELECT
            customer,
            customer_name,
            total_sales,
            RANK() OVER (ORDER BY total_sales DESC) as sales_rank
        FROM (
            SELECT
                si.customer,
                c.customer_name,
                SUM(si.grand_total) as total_sales
            FROM `tabSales Invoice` si
            JOIN `tabCustomer` c ON si.customer = c.name
            WHERE si.docstatus = 1
            AND si.posting_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
            GROUP BY si.customer
        ) as customer_sales
        WHERE total_sales > 50000
        ORDER BY sales_rank
    """, as_dict=True)

# استعلام مع CTE (Common Table Expression)
def get_customer_hierarchy():
    return frappe.db.sql("""
        WITH RECURSIVE customer_tree AS (
            SELECT name, customer_name, parent_customer, 0 as level
            FROM `tabCustomer`
            WHERE parent_customer IS NULL OR parent_customer = ''

            UNION ALL

            SELECT c.name, c.customer_name, c.parent_customer, ct.level + 1
            FROM `tabCustomer` c
            JOIN customer_tree ct ON c.parent_customer = ct.name
        )
        SELECT * FROM customer_tree ORDER BY level, customer_name
    """, as_dict=True)
```

### **3. تحسين الأداء:**
```python
# إنشاء فهارس مخصصة
def create_custom_indexes():
    # فهرس على حقل واحد
    frappe.db.sql("CREATE INDEX idx_customer_group ON `tabCustomer` (customer_group)")

    # فهرس مركب
    frappe.db.sql("""
        CREATE INDEX idx_sales_invoice_customer_date
        ON `tabSales Invoice` (customer, posting_date)
    """)

    # فهرس جزئي
    frappe.db.sql("""
        CREATE INDEX idx_active_customers
        ON `tabCustomer` (customer_name)
        WHERE disabled = 0
    """)

# استخدام EXPLAIN لتحليل الاستعلامات
def analyze_query():
    result = frappe.db.sql("""
        EXPLAIN SELECT * FROM `tabSales Invoice`
        WHERE customer = 'CUST-001' AND posting_date >= '2024-01-01'
    """, as_dict=True)

    for row in result:
        print(f"Table: {row.table}, Type: {row.type}, Key: {row.key}")

# تحسين الاستعلامات الكبيرة
def process_large_dataset():
    # معالجة البيانات على دفعات
    batch_size = 1000
    offset = 0

    while True:
        records = frappe.db.sql("""
            SELECT name, customer, grand_total
            FROM `tabSales Invoice`
            WHERE docstatus = 1
            ORDER BY name
            LIMIT %s OFFSET %s
        """, (batch_size, offset), as_dict=True)

        if not records:
            break

        # معالجة الدفعة
        for record in records:
            process_invoice(record)

        offset += batch_size
        frappe.db.commit()  # حفظ كل دفعة
```

### **4. النسخ الاحتياطي والاستعادة:**
```python
# إنشاء نسخة احتياطية
def create_backup():
    import subprocess
    import datetime

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"/tmp/backup_{timestamp}.sql"

    # تصدير قاعدة البيانات
    cmd = f"mysqldump -u {frappe.conf.db_name} -p{frappe.conf.db_password} {frappe.conf.db_name} > {backup_file}"
    subprocess.run(cmd, shell=True)

    return backup_file

# استعادة من نسخة احتياطية
def restore_backup(backup_file):
    import subprocess

    cmd = f"mysql -u {frappe.conf.db_name} -p{frappe.conf.db_password} {frappe.conf.db_name} < {backup_file}"
    subprocess.run(cmd, shell=True)

# تنظيف البيانات القديمة
def cleanup_old_data():
    # حذف السجلات القديمة
    frappe.db.sql("""
        DELETE FROM `tabError Log`
        WHERE creation < DATE_SUB(NOW(), INTERVAL 30 DAY)
    """)

    # حذف الملفات المؤقتة
    frappe.db.sql("""
        DELETE FROM `tabFile`
        WHERE is_private = 1
        AND creation < DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND file_name LIKE 'temp_%'
    """)

    frappe.db.commit()
```

---

## 🌐 APIs والتكامل {#apis-والتكامل}

### **1. إنشاء APIs مخصصة:**

#### **API بسيط:**
```python
# custom_app/custom_module/api.py

import frappe
from frappe import _

@frappe.whitelist()
def get_customer_info(customer_id):
    """API لجلب معلومات العميل"""
    try:
        # التحقق من الصلاحيات
        if not frappe.has_permission("Customer", "read"):
            frappe.throw(_("ليس لديك صلاحية قراءة بيانات العملاء"))

        # جلب بيانات العميل
        customer = frappe.get_doc("Customer", customer_id)

        # جلب إحصائيات المبيعات
        sales_stats = frappe.db.sql("""
            SELECT
                COUNT(*) as total_invoices,
                SUM(grand_total) as total_sales,
                AVG(grand_total) as avg_invoice_value
            FROM `tabSales Invoice`
            WHERE customer = %s AND docstatus = 1
        """, customer_id, as_dict=True)[0]

        # تجهيز البيانات للإرجاع
        response = {
            "customer_info": {
                "name": customer.name,
                "customer_name": customer.customer_name,
                "email": customer.email_id,
                "mobile": customer.mobile_no,
                "customer_group": customer.customer_group,
                "territory": customer.territory
            },
            "sales_statistics": sales_stats,
            "status": "success"
        }

        return response

    except Exception as e:
        frappe.log_error(f"خطأ في API get_customer_info: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

@frappe.whitelist(allow_guest=True)
def public_api_endpoint():
    """API عام يمكن الوصول إليه بدون تسجيل دخول"""
    return {
        "message": "مرحباً من API العام",
        "timestamp": frappe.utils.now(),
        "version": "1.0"
    }

@frappe.whitelist(methods=["POST"])
def create_customer_api():
    """API لإنشاء عميل جديد - POST فقط"""
    try:
        # جلب البيانات من الطلب
        data = frappe.local.form_dict

        # التحقق من البيانات المطلوبة
        required_fields = ["customer_name", "customer_type"]
        for field in required_fields:
            if not data.get(field):
                frappe.throw(_("الحقل {0} مطلوب").format(field))

        # إنشاء العميل
        customer = frappe.new_doc("Customer")
        customer.customer_name = data.get("customer_name")
        customer.customer_type = data.get("customer_type")
        customer.customer_group = data.get("customer_group", "All Customer Groups")
        customer.territory = data.get("territory", "All Territories")
        customer.email_id = data.get("email")
        customer.mobile_no = data.get("mobile")

        customer.insert()

        return {
            "status": "success",
            "customer_id": customer.name,
            "message": _("تم إنشاء العميل بنجاح")
        }

    except Exception as e:
        frappe.log_error(f"خطأ في إنشاء العميل: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

@frappe.whitelist()
def bulk_update_customers():
    """API لتحديث عدة عملاء دفعة واحدة"""
    try:
        data = frappe.local.form_dict
        customers_data = data.get("customers", [])

        if not customers_data:
            frappe.throw(_("لا توجد بيانات عملاء للتحديث"))

        updated_customers = []
        errors = []

        for customer_data in customers_data:
            try:
                customer_id = customer_data.get("customer_id")
                if not customer_id:
                    continue

                customer = frappe.get_doc("Customer", customer_id)

                # تحديث الحقول
                if customer_data.get("customer_name"):
                    customer.customer_name = customer_data.get("customer_name")
                if customer_data.get("email"):
                    customer.email_id = customer_data.get("email")
                if customer_data.get("mobile"):
                    customer.mobile_no = customer_data.get("mobile")

                customer.save()
                updated_customers.append(customer_id)

            except Exception as e:
                errors.append({
                    "customer_id": customer_data.get("customer_id"),
                    "error": str(e)
                })

        return {
            "status": "success",
            "updated_customers": updated_customers,
            "errors": errors,
            "total_updated": len(updated_customers)
        }

    except Exception as e:
        frappe.log_error(f"خطأ في التحديث المجمع: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }
```

### **2. استدعاء APIs من JavaScript:**

#### **استدعاء API بسيط:**
```javascript
// custom_app/public/js/customer_api.js

// دالة لجلب معلومات العميل
function getCustomerInfo(customerId) {
    frappe.call({
        method: 'custom_app.custom_module.api.get_customer_info',
        args: {
            customer_id: customerId
        },
        callback: function(response) {
            if (response.message && response.message.status === 'success') {
                displayCustomerInfo(response.message);
            } else {
                frappe.msgprint(__('خطأ في جلب بيانات العميل'));
            }
        },
        error: function(error) {
            console.error('API Error:', error);
            frappe.msgprint(__('حدث خطأ في الاتصال'));
        }
    });
}

// دالة لعرض معلومات العميل
function displayCustomerInfo(data) {
    const customerInfo = data.customer_info;
    const salesStats = data.sales_statistics;

    const html = `
        <div class="customer-info-card">
            <h4>${customerInfo.customer_name}</h4>
            <p><strong>البريد الإلكتروني:</strong> ${customerInfo.email || 'غير محدد'}</p>
            <p><strong>الهاتف:</strong> ${customerInfo.mobile || 'غير محدد'}</p>
            <p><strong>مجموعة العملاء:</strong> ${customerInfo.customer_group}</p>

            <hr>
            <h5>إحصائيات المبيعات:</h5>
            <p><strong>عدد الفواتير:</strong> ${salesStats.total_invoices}</p>
            <p><strong>إجمالي المبيعات:</strong> ${format_currency(salesStats.total_sales)}</p>
            <p><strong>متوسط قيمة الفاتورة:</strong> ${format_currency(salesStats.avg_invoice_value)}</p>
        </div>
    `;

    frappe.msgprint({
        title: __('معلومات العميل'),
        message: html,
        wide: true
    });
}

// دالة لإنشاء عميل جديد
function createCustomer(customerData) {
    frappe.call({
        method: 'custom_app.custom_module.api.create_customer_api',
        type: 'POST',
        args: customerData,
        callback: function(response) {
            if (response.message && response.message.status === 'success') {
                frappe.show_alert({
                    message: __('تم إنشاء العميل بنجاح'),
                    indicator: 'green'
                });

                // إعادة تحميل القائمة أو الانتقال للعميل الجديد
                if (cur_list) {
                    cur_list.refresh();
                }
            } else {
                frappe.msgprint({
                    title: __('خطأ'),
                    message: response.message.message,
                    indicator: 'red'
                });
            }
        }
    });
}

// دالة للتحديث المجمع
function bulkUpdateCustomers(customersData) {
    frappe.call({
        method: 'custom_app.custom_module.api.bulk_update_customers',
        args: {
            customers: customersData
        },
        callback: function(response) {
            if (response.message && response.message.status === 'success') {
                const result = response.message;

                let message = `تم تحديث ${result.total_updated} عميل بنجاح`;

                if (result.errors.length > 0) {
                    message += `\n\nأخطاء في ${result.errors.length} عميل:`;
                    result.errors.forEach(error => {
                        message += `\n- ${error.customer_id}: ${error.error}`;
                    });
                }

                frappe.msgprint({
                    title: __('نتائج التحديث المجمع'),
                    message: message,
                    indicator: result.errors.length > 0 ? 'orange' : 'green'
                });
            }
        }
    });
}

// استدعاء API مع معالجة التحميل
function callAPIWithLoader(method, args, callback) {
    frappe.show_progress(__('جاري التحميل...'), 0, 100);

    frappe.call({
        method: method,
        args: args,
        callback: function(response) {
            frappe.hide_progress();
            if (callback) callback(response);
        },
        error: function(error) {
            frappe.hide_progress();
            frappe.msgprint(__('حدث خطأ في الاتصال'));
        }
    });
}
```

### **3. APIs خارجية (REST APIs):**

#### **إنشاء REST API endpoints:**
```python
# custom_app/custom_module/rest_api.py

import frappe
from frappe import _
import json

@frappe.whitelist(allow_guest=True, methods=["GET"])
def get_customers():
    """REST API لجلب قائمة العملاء"""
    try:
        # التحقق من API Key
        api_key = frappe.get_request_header("Authorization")
        if not validate_api_key(api_key):
            frappe.local.response.http_status_code = 401
            return {"error": "Unauthorized"}

        # جلب العملاء
        customers = frappe.db.get_list("Customer",
            filters={"disabled": 0},
            fields=["name", "customer_name", "email_id", "mobile_no"],
            limit_page_length=100
        )

        frappe.local.response.http_status_code = 200
        return {
            "status": "success",
            "data": customers,
            "count": len(customers)
        }

    except Exception as e:
        frappe.log_error(f"REST API Error: {str(e)}")
        frappe.local.response.http_status_code = 500
        return {"error": "Internal Server Error"}

@frappe.whitelist(allow_guest=True, methods=["POST"])
def create_customer_rest():
    """REST API لإنشاء عميل جديد"""
    try:
        # التحقق من API Key
        api_key = frappe.get_request_header("Authorization")
        if not validate_api_key(api_key):
            frappe.local.response.http_status_code = 401
            return {"error": "Unauthorized"}

        # جلب البيانات من JSON
        data = json.loads(frappe.local.request.data)

        # التحقق من البيانات
        if not data.get("customer_name"):
            frappe.local.response.http_status_code = 400
            return {"error": "customer_name is required"}

        # إنشاء العميل
        customer = frappe.new_doc("Customer")
        customer.customer_name = data.get("customer_name")
        customer.customer_type = data.get("customer_type", "Individual")
        customer.customer_group = data.get("customer_group", "All Customer Groups")
        customer.territory = data.get("territory", "All Territories")
        customer.email_id = data.get("email")
        customer.mobile_no = data.get("mobile")

        customer.insert()

        frappe.local.response.http_status_code = 201
        return {
            "status": "success",
            "data": {
                "customer_id": customer.name,
                "customer_name": customer.customer_name
            }
        }

    except Exception as e:
        frappe.log_error(f"REST API Create Error: {str(e)}")
        frappe.local.response.http_status_code = 500
        return {"error": "Internal Server Error"}

def validate_api_key(api_key):
    """التحقق من صحة API Key"""
    if not api_key:
        return False

    # إزالة "Bearer " من بداية المفتاح
    if api_key.startswith("Bearer "):
        api_key = api_key[7:]

    # التحقق من المفتاح في قاعدة البيانات
    user = frappe.db.get_value("User", {"api_key": api_key}, "name")
    if user:
        frappe.set_user(user)
        return True

    return False

@frappe.whitelist(allow_guest=True, methods=["PUT"])
def update_customer_rest(customer_id):
    """REST API لتحديث عميل"""
    try:
        api_key = frappe.get_request_header("Authorization")
        if not validate_api_key(api_key):
            frappe.local.response.http_status_code = 401
            return {"error": "Unauthorized"}

        # التحقق من وجود العميل
        if not frappe.db.exists("Customer", customer_id):
            frappe.local.response.http_status_code = 404
            return {"error": "Customer not found"}

        # جلب البيانات الجديدة
        data = json.loads(frappe.local.request.data)

        # تحديث العميل
        customer = frappe.get_doc("Customer", customer_id)

        if data.get("customer_name"):
            customer.customer_name = data.get("customer_name")
        if data.get("email"):
            customer.email_id = data.get("email")
        if data.get("mobile"):
            customer.mobile_no = data.get("mobile")

        customer.save()

        frappe.local.response.http_status_code = 200
        return {
            "status": "success",
            "message": "Customer updated successfully"
        }

    except Exception as e:
        frappe.log_error(f"REST API Update Error: {str(e)}")
        frappe.local.response.http_status_code = 500
        return {"error": "Internal Server Error"}
```

### **4. استدعاء APIs خارجية من ERPNext:**

#### **استدعاء APIs خارجية:**
```python
# custom_app/custom_module/external_api.py

import frappe
import requests
import json
from frappe import _

def call_external_api(url, method="GET", data=None, headers=None):
    """دالة عامة لاستدعاء APIs خارجية"""
    try:
        # إعداد الرؤوس الافتراضية
        default_headers = {
            "Content-Type": "application/json",
            "User-Agent": "ERPNext-Custom-App/1.0"
        }

        if headers:
            default_headers.update(headers)

        # تحديد نوع الطلب
        if method.upper() == "GET":
            response = requests.get(url, headers=default_headers, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=default_headers, timeout=30)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=default_headers, timeout=30)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=default_headers, timeout=30)
        else:
            raise ValueError(f"HTTP method {method} not supported")

        # التحقق من حالة الاستجابة
        response.raise_for_status()

        # إرجاع البيانات
        return {
            "success": True,
            "data": response.json() if response.content else None,
            "status_code": response.status_code
        }

    except requests.exceptions.Timeout:
        frappe.log_error("API Timeout", "External API Call")
        return {"success": False, "error": "Request timeout"}

    except requests.exceptions.ConnectionError:
        frappe.log_error("API Connection Error", "External API Call")
        return {"success": False, "error": "Connection error"}

    except requests.exceptions.HTTPError as e:
        frappe.log_error(f"API HTTP Error: {str(e)}", "External API Call")
        return {"success": False, "error": f"HTTP Error: {e.response.status_code}"}

    except Exception as e:
        frappe.log_error(f"API General Error: {str(e)}", "External API Call")
        return {"success": False, "error": str(e)}

def sync_customer_with_external_system(customer_id):
    """مزامنة بيانات العميل مع نظام خارجي"""
    try:
        # جلب بيانات العميل
        customer = frappe.get_doc("Customer", customer_id)

        # تحضير البيانات للإرسال
        customer_data = {
            "id": customer.name,
            "name": customer.customer_name,
            "email": customer.email_id,
            "phone": customer.mobile_no,
            "group": customer.customer_group,
            "territory": customer.territory
        }

        # إعداد الرؤوس مع API Key
        headers = {
            "Authorization": "Bearer YOUR_API_KEY",
            "Content-Type": "application/json"
        }

        # استدعاء API الخارجي
        external_api_url = "https://external-system.com/api/customers"
        result = call_external_api(external_api_url, "POST", customer_data, headers)

        if result["success"]:
            # حفظ معرف النظام الخارجي
            customer.external_system_id = result["data"].get("id")
            customer.save()

            frappe.msgprint(_("تم مزامنة العميل مع النظام الخارجي بنجاح"))
            return True
        else:
            frappe.throw(_("فشل في مزامنة العميل: {0}").format(result["error"]))

    except Exception as e:
        frappe.log_error(f"Customer Sync Error: {str(e)}")
        frappe.throw(_("حدث خطأ في المزامنة"))

def get_exchange_rates():
    """جلب أسعار الصرف من API خارجي"""
    try:
        api_url = "https://api.exchangerate-api.com/v4/latest/USD"
        result = call_external_api(api_url, "GET")

        if result["success"]:
            rates = result["data"]["rates"]

            # تحديث أسعار الصرف في النظام
            for currency, rate in rates.items():
                if frappe.db.exists("Currency", currency):
                    frappe.db.set_value("Currency Exchange",
                        {"from_currency": "USD", "to_currency": currency},
                        "exchange_rate", rate)

            frappe.db.commit()
            return rates
        else:
            frappe.log_error("Failed to fetch exchange rates")
            return None

    except Exception as e:
        frappe.log_error(f"Exchange Rate Error: {str(e)}")
        return None

@frappe.whitelist()
def send_sms_notification(mobile, message):
    """إرسال رسالة SMS عبر API خارجي"""
    try:
        # إعداد بيانات الرسالة
        sms_data = {
            "to": mobile,
            "message": message,
            "from": "YourCompany"
        }

        # إعداد الرؤوس
        headers = {
            "Authorization": "Bearer YOUR_SMS_API_KEY",
            "Content-Type": "application/json"
        }

        # استدعاء SMS API
        sms_api_url = "https://sms-provider.com/api/send"
        result = call_external_api(sms_api_url, "POST", sms_data, headers)

        if result["success"]:
            # تسجيل الرسالة المرسلة
            sms_log = frappe.new_doc("SMS Log")
            sms_log.mobile_no = mobile
            sms_log.message = message
            sms_log.status = "Sent"
            sms_log.insert()

            return {"success": True, "message": "SMS sent successfully"}
        else:
            return {"success": False, "error": result["error"]}

    except Exception as e:
        frappe.log_error(f"SMS Error: {str(e)}")
        return {"success": False, "error": str(e)}
```

### **5. WebHooks - استقبال البيانات من أنظمة خارجية:**

#### **إنشاء WebHook endpoint:**
```python
# custom_app/custom_module/webhooks.py

import frappe
import json
from frappe import _

@frappe.whitelist(allow_guest=True, methods=["POST"])
def payment_webhook():
    """WebHook لاستقبال إشعارات الدفع"""
    try:
        # جلب البيانات من الطلب
        data = json.loads(frappe.local.request.data)

        # التحقق من التوقيع (للأمان)
        signature = frappe.get_request_header("X-Signature")
        if not verify_webhook_signature(frappe.local.request.data, signature):
            frappe.local.response.http_status_code = 401
            return {"error": "Invalid signature"}

        # معالجة إشعار الدفع
        payment_id = data.get("payment_id")
        status = data.get("status")
        amount = data.get("amount")
        reference = data.get("reference")

        # البحث عن الفاتورة المرتبطة
        invoice = frappe.db.get_value("Sales Invoice",
            {"custom_payment_reference": reference}, "name")

        if invoice:
            # تحديث حالة الدفع
            invoice_doc = frappe.get_doc("Sales Invoice", invoice)

            if status == "completed":
                # إنشاء سند دفع
                payment_entry = frappe.new_doc("Payment Entry")
                payment_entry.payment_type = "Receive"
                payment_entry.party_type = "Customer"
                payment_entry.party = invoice_doc.customer
                payment_entry.paid_amount = amount
                payment_entry.received_amount = amount
                payment_entry.reference_no = payment_id
                payment_entry.reference_date = frappe.utils.today()

                # ربط الدفع بالفاتورة
                payment_entry.append("references", {
                    "reference_doctype": "Sales Invoice",
                    "reference_name": invoice,
                    "allocated_amount": amount
                })

                payment_entry.insert()
                payment_entry.submit()

                # إرسال إشعار للعميل
                send_payment_confirmation(invoice_doc, payment_id)

            elif status == "failed":
                # تسجيل فشل الدفع
                frappe.log_error(f"Payment failed for invoice {invoice}: {data}")

        frappe.local.response.http_status_code = 200
        return {"status": "success"}

    except Exception as e:
        frappe.log_error(f"Payment Webhook Error: {str(e)}")
        frappe.local.response.http_status_code = 500
        return {"error": "Internal Server Error"}

def verify_webhook_signature(payload, signature):
    """التحقق من توقيع WebHook"""
    import hmac
    import hashlib

    # المفتاح السري (يجب حفظه في إعدادات آمنة)
    secret_key = frappe.get_conf().get("webhook_secret_key", "")

    # حساب التوقيع المتوقع
    expected_signature = hmac.new(
        secret_key.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()

    # مقارنة التوقيعات
    return hmac.compare_digest(signature, expected_signature)

@frappe.whitelist(allow_guest=True, methods=["POST"])
def inventory_webhook():
    """WebHook لاستقبال تحديثات المخزون من نظام خارجي"""
    try:
        data = json.loads(frappe.local.request.data)

        # التحقق من التوقيع
        signature = frappe.get_request_header("X-Signature")
        if not verify_webhook_signature(frappe.local.request.data, signature):
            frappe.local.response.http_status_code = 401
            return {"error": "Invalid signature"}

        # معالجة تحديثات المخزون
        for item_update in data.get("items", []):
            item_code = item_update.get("item_code")
            warehouse = item_update.get("warehouse")
            qty_change = item_update.get("qty_change")

            if item_code and warehouse and qty_change:
                # إنشاء Stock Entry
                stock_entry = frappe.new_doc("Stock Entry")
                stock_entry.stock_entry_type = "Material Receipt" if qty_change > 0 else "Material Issue"
                stock_entry.company = frappe.defaults.get_user_default("Company")

                stock_entry.append("items", {
                    "item_code": item_code,
                    "qty": abs(qty_change),
                    "t_warehouse": warehouse if qty_change > 0 else None,
                    "s_warehouse": warehouse if qty_change < 0 else None
                })

                stock_entry.insert()
                stock_entry.submit()

        frappe.local.response.http_status_code = 200
        return {"status": "success", "processed_items": len(data.get("items", []))}

    except Exception as e:
        frappe.log_error(f"Inventory Webhook Error: {str(e)}")
        frappe.local.response.http_status_code = 500
        return {"error": "Internal Server Error"}

def send_payment_confirmation(invoice, payment_id):
    """إرسال تأكيد الدفع للعميل"""
    try:
        # إرسال بريد إلكتروني
        frappe.sendmail(
            recipients=[invoice.contact_email],
            subject=f"تأكيد استلام الدفع - فاتورة {invoice.name}",
            message=f"""
            عزيزي {invoice.customer_name},

            تم استلام دفعتكم بنجاح للفاتورة رقم {invoice.name}
            رقم المعاملة: {payment_id}
            المبلغ: {invoice.grand_total}

            شكراً لكم
            """
        )

        # إرسال SMS (إذا كان متوفراً)
        if invoice.contact_mobile:
            send_sms_notification(
                invoice.contact_mobile,
                f"تم استلام دفعتكم للفاتورة {invoice.name}. رقم المعاملة: {payment_id}"
            )

    except Exception as e:
        frappe.log_error(f"Payment Confirmation Error: {str(e)}")
```

---

## 📋 التطوير على DocTypes {#تطوير-doctypes}

### **1. إنشاء DocType جديد:**

#### **إنشاء DocType من خلال الواجهة:**
```bash
# الانتقال إلى Developer Mode
bench --site site1.local set-config developer_mode 1

# إعادة تشغيل النظام
bench restart
```

#### **إنشاء DocType برمجياً:**
```python
# custom_app/custom_module/install.py

import frappe

def create_custom_doctype():
    """إنشاء DocType مخصص برمجياً"""

    if frappe.db.exists("DocType", "Project Task Assignment"):
        return

    # إنشاء DocType
    doctype = frappe.new_doc("DocType")
    doctype.name = "Project Task Assignment"
    doctype.module = "Custom Module"
    doctype.custom = 1
    doctype.is_submittable = 1
    doctype.autoname = "naming_series:"
    doctype.title_field = "task_title"
    doctype.sort_field = "modified"
    doctype.sort_order = "DESC"

    # إضافة الحقول
    fields = [
        {
            "fieldname": "naming_series",
            "fieldtype": "Select",
            "label": "سلسلة الترقيم",
            "options": "PTA-.YYYY.-",
            "reqd": 1
        },
        {
            "fieldname": "task_title",
            "fieldtype": "Data",
            "label": "عنوان المهمة",
            "reqd": 1
        },
        {
            "fieldname": "project",
            "fieldtype": "Link",
            "label": "المشروع",
            "options": "Project",
            "reqd": 1
        },
        {
            "fieldname": "assigned_to",
            "fieldtype": "Link",
            "label": "مُكلف إلى",
            "options": "User",
            "reqd": 1
        },
        {
            "fieldname": "section_break_1",
            "fieldtype": "Section Break"
        },
        {
            "fieldname": "description",
            "fieldtype": "Text Editor",
            "label": "الوصف"
        },
        {
            "fieldname": "priority",
            "fieldtype": "Select",
            "label": "الأولوية",
            "options": "Low\nMedium\nHigh\nUrgent",
            "default": "Medium"
        },
        {
            "fieldname": "column_break_1",
            "fieldtype": "Column Break"
        },
        {
            "fieldname": "start_date",
            "fieldtype": "Date",
            "label": "تاريخ البداية",
            "reqd": 1
        },
        {
            "fieldname": "end_date",
            "fieldtype": "Date",
            "label": "تاريخ النهاية",
            "reqd": 1
        },
        {
            "fieldname": "status",
            "fieldtype": "Select",
            "label": "الحالة",
            "options": "Open\nWorking\nPending Review\nCompleted\nCancelled",
            "default": "Open"
        },
        {
            "fieldname": "section_break_2",
            "fieldtype": "Section Break",
            "label": "تفاصيل إضافية"
        },
        {
            "fieldname": "estimated_hours",
            "fieldtype": "Float",
            "label": "الساعات المقدرة"
        },
        {
            "fieldname": "actual_hours",
            "fieldtype": "Float",
            "label": "الساعات الفعلية",
            "read_only": 1
        },
        {
            "fieldname": "progress",
            "fieldtype": "Percent",
            "label": "نسبة الإنجاز",
            "default": 0
        }
    ]

    for field in fields:
        doctype.append("fields", field)

    # إضافة الصلاحيات
    permissions = [
        {
            "role": "System Manager",
            "read": 1,
            "write": 1,
            "create": 1,
            "delete": 1,
            "submit": 1,
            "cancel": 1,
            "amend": 1
        },
        {
            "role": "Project Manager",
            "read": 1,
            "write": 1,
            "create": 1,
            "submit": 1,
            "cancel": 1
        },
        {
            "role": "Employee",
            "read": 1,
            "write": 1
        }
    ]

    for perm in permissions:
        doctype.append("permissions", perm)

    # حفظ DocType
    doctype.insert()

    print(f"تم إنشاء DocType: {doctype.name}")

def create_child_doctype():
    """إنشاء Child DocType للمهام الفرعية"""

    if frappe.db.exists("DocType", "Task Subtask"):
        return

    doctype = frappe.new_doc("DocType")
    doctype.name = "Task Subtask"
    doctype.module = "Custom Module"
    doctype.custom = 1
    doctype.istable = 1  # Child DocType

    # حقول Child DocType
    fields = [
        {
            "fieldname": "subtask_title",
            "fieldtype": "Data",
            "label": "عنوان المهمة الفرعية",
            "reqd": 1,
            "in_list_view": 1
        },
        {
            "fieldname": "description",
            "fieldtype": "Small Text",
            "label": "الوصف",
            "in_list_view": 1
        },
        {
            "fieldname": "assigned_to",
            "fieldtype": "Link",
            "label": "مُكلف إلى",
            "options": "User",
            "in_list_view": 1
        },
        {
            "fieldname": "status",
            "fieldtype": "Select",
            "label": "الحالة",
            "options": "Open\nCompleted",
            "default": "Open",
            "in_list_view": 1
        },
        {
            "fieldname": "estimated_hours",
            "fieldtype": "Float",
            "label": "الساعات المقدرة",
            "in_list_view": 1
        }
    ]

    for field in fields:
        doctype.append("fields", field)

    doctype.insert()

    # إضافة جدول المهام الفرعية إلى DocType الرئيسي
    main_doctype = frappe.get_doc("DocType", "Project Task Assignment")
    main_doctype.append("fields", {
        "fieldname": "subtasks",
        "fieldtype": "Table",
        "label": "المهام الفرعية",
        "options": "Task Subtask"
    })
    main_doctype.save()

    print("تم إنشاء Child DocType: Task Subtask")
```

### **2. تخصيص DocType موجود:**

#### **إضافة حقول مخصصة:**
```python
# custom_app/custom_module/custom_fields.py

import frappe

def add_custom_fields():
    """إضافة حقول مخصصة للـ DocTypes الموجودة"""

    # إضافة حقول للعميل
    customer_fields = [
        {
            "doctype": "Customer",
            "fieldname": "customer_rating",
            "fieldtype": "Rating",
            "label": "تقييم العميل",
            "insert_after": "customer_group"
        },
        {
            "doctype": "Customer",
            "fieldname": "vip_customer",
            "fieldtype": "Check",
            "label": "عميل مميز",
            "insert_after": "customer_rating"
        },
        {
            "doctype": "Customer",
            "fieldname": "credit_limit_currency",
            "fieldtype": "Link",
            "label": "عملة حد الائتمان",
            "options": "Currency",
            "insert_after": "credit_limit"
        }
    ]

    # إضافة حقول لفاتورة المبيعات
    sales_invoice_fields = [
        {
            "doctype": "Sales Invoice",
            "fieldname": "delivery_note_required",
            "fieldtype": "Check",
            "label": "يتطلب إذن تسليم",
            "insert_after": "update_stock"
        },
        {
            "doctype": "Sales Invoice",
            "fieldname": "payment_reference",
            "fieldtype": "Data",
            "label": "مرجع الدفع",
            "insert_after": "due_date"
        },
        {
            "doctype": "Sales Invoice",
            "fieldname": "external_invoice_id",
            "fieldtype": "Data",
            "label": "معرف الفاتورة الخارجي",
            "read_only": 1,
            "insert_after": "payment_reference"
        }
    ]

    # دمج جميع الحقول
    all_fields = customer_fields + sales_invoice_fields

    for field in all_fields:
        create_custom_field(field)

def create_custom_field(field_dict):
    """إنشاء حقل مخصص"""
    try:
        # التحقق من وجود الحقل
        if frappe.db.exists("Custom Field", {
            "dt": field_dict["doctype"],
            "fieldname": field_dict["fieldname"]
        }):
            print(f"الحقل {field_dict['fieldname']} موجود بالفعل في {field_dict['doctype']}")
            return

        # إنشاء الحقل المخصص
        custom_field = frappe.new_doc("Custom Field")
        custom_field.dt = field_dict["doctype"]
        custom_field.fieldname = field_dict["fieldname"]
        custom_field.fieldtype = field_dict["fieldtype"]
        custom_field.label = field_dict["label"]
        custom_field.insert_after = field_dict.get("insert_after")
        custom_field.options = field_dict.get("options")
        custom_field.reqd = field_dict.get("reqd", 0)
        custom_field.read_only = field_dict.get("read_only", 0)
        custom_field.hidden = field_dict.get("hidden", 0)
        custom_field.in_list_view = field_dict.get("in_list_view", 0)

        custom_field.insert()

        print(f"تم إنشاء الحقل المخصص: {field_dict['fieldname']} في {field_dict['doctype']}")

    except Exception as e:
        print(f"خطأ في إنشاء الحقل {field_dict['fieldname']}: {str(e)}")

def add_property_setters():
    """تعديل خصائص الحقول الموجودة"""

    property_setters = [
        {
            "doctype": "Customer",
            "field_name": "customer_name",
            "property": "reqd",
            "value": 1
        },
        {
            "doctype": "Customer",
            "field_name": "mobile_no",
            "property": "unique",
            "value": 1
        },
        {
            "doctype": "Sales Invoice",
            "field_name": "due_date",
            "property": "reqd",
            "value": 1
        }
    ]

    for prop in property_setters:
        create_property_setter(prop)

def create_property_setter(prop_dict):
    """إنشاء Property Setter"""
    try:
        # التحقق من وجود Property Setter
        if frappe.db.exists("Property Setter", {
            "doc_type": prop_dict["doctype"],
            "field_name": prop_dict["field_name"],
            "property": prop_dict["property"]
        }):
            return

        # إنشاء Property Setter
        property_setter = frappe.new_doc("Property Setter")
        property_setter.doc_type = prop_dict["doctype"]
        property_setter.field_name = prop_dict["field_name"]
        property_setter.property = prop_dict["property"]
        property_setter.value = prop_dict["value"]
        property_setter.property_type = "Check" if isinstance(prop_dict["value"], bool) else "Data"

        property_setter.insert()

        print(f"تم إنشاء Property Setter: {prop_dict['property']} للحقل {prop_dict['field_name']}")

    except Exception as e:
        print(f"خطأ في إنشاء Property Setter: {str(e)}")
```

### **3. Controllers متقدمة للـ DocTypes:**

#### **Controller شامل:**
```python
# custom_app/custom_module/doctype/project_task_assignment/project_task_assignment.py

import frappe
from frappe.model.document import Document
from frappe import _
from frappe.utils import getdate, date_diff, flt
import json

class ProjectTaskAssignment(Document):
    """Controller للـ DocType المخصص"""

    def validate(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        self.validate_dates()
        self.validate_user_assignment()
        self.calculate_progress()
        self.set_title()

    def before_save(self):
        """قبل الحفظ"""
        self.update_project_progress()

    def after_insert(self):
        """بعد الإنشاء"""
        self.create_todo_for_assignee()
        self.send_assignment_notification()

    def on_submit(self):
        """عند التأكيد"""
        self.validate_completion()
        self.update_project_status()
        self.create_timesheet_entries()

    def on_cancel(self):
        """عند الإلغاء"""
        self.cancel_related_todos()
        self.reverse_project_updates()

    def validate_dates(self):
        """التحقق من صحة التواريخ"""
        if self.start_date and self.end_date:
            if getdate(self.start_date) > getdate(self.end_date):
                frappe.throw(_("تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية"))

        # التحقق من تواريخ المشروع
        if self.project:
            project = frappe.get_doc("Project", self.project)
            if project.expected_start_date and self.start_date:
                if getdate(self.start_date) < getdate(project.expected_start_date):
                    frappe.throw(_("تاريخ بداية المهمة لا يمكن أن يكون قبل بداية المشروع"))

            if project.expected_end_date and self.end_date:
                if getdate(self.end_date) > getdate(project.expected_end_date):
                    frappe.throw(_("تاريخ نهاية المهمة لا يمكن أن يكون بعد نهاية المشروع"))

    def validate_user_assignment(self):
        """التحقق من صحة تكليف المستخدم"""
        if self.assigned_to:
            # التحقق من أن المستخدم نشط
            user = frappe.get_doc("User", self.assigned_to)
            if user.enabled == 0:
                frappe.throw(_("لا يمكن تكليف مستخدم غير نشط"))

            # التحقق من عبء العمل
            self.check_user_workload()

    def check_user_workload(self):
        """فحص عبء العمل للمستخدم"""
        if not self.start_date or not self.end_date:
            return

        # حساب المهام المتداخلة
        overlapping_tasks = frappe.db.sql("""
            SELECT COUNT(*) as count, SUM(estimated_hours) as total_hours
            FROM `tabProject Task Assignment`
            WHERE assigned_to = %s
            AND docstatus = 1
            AND status NOT IN ('Completed', 'Cancelled')
            AND (
                (start_date <= %s AND end_date >= %s) OR
                (start_date <= %s AND end_date >= %s) OR
                (start_date >= %s AND end_date <= %s)
            )
            AND name != %s
        """, (
            self.assigned_to,
            self.start_date, self.start_date,
            self.end_date, self.end_date,
            self.start_date, self.end_date,
            self.name or ""
        ), as_dict=True)[0]

        if overlapping_tasks.count > 5:
            frappe.msgprint(_("تحذير: المستخدم لديه {0} مهام متداخلة").format(overlapping_tasks.count))

        if overlapping_tasks.total_hours and overlapping_tasks.total_hours > 40:
            frappe.msgprint(_("تحذير: إجمالي ساعات العمل المقدرة {0} ساعة").format(overlapping_tasks.total_hours))

    def calculate_progress(self):
        """حساب نسبة الإنجاز بناءً على المهام الفرعية"""
        if not self.subtasks:
            return

        total_subtasks = len(self.subtasks)
        completed_subtasks = len([task for task in self.subtasks if task.status == "Completed"])

        if total_subtasks > 0:
            self.progress = (completed_subtasks / total_subtasks) * 100

            # تحديث الحالة بناءً على التقدم
            if self.progress == 100:
                self.status = "Completed"
            elif self.progress > 0:
                self.status = "Working"

    def set_title(self):
        """تعيين العنوان للمستند"""
        if self.task_title and self.project:
            project_name = frappe.db.get_value("Project", self.project, "project_name")
            self.title = f"{self.task_title} - {project_name}"

    def update_project_progress(self):
        """تحديث تقدم المشروع"""
        if not self.project:
            return

        # حساب تقدم جميع مهام المشروع
        project_tasks = frappe.db.sql("""
            SELECT AVG(progress) as avg_progress
            FROM `tabProject Task Assignment`
            WHERE project = %s AND docstatus = 1
        """, self.project, as_dict=True)

        if project_tasks and project_tasks[0].avg_progress is not None:
            frappe.db.set_value("Project", self.project, "percent_complete", project_tasks[0].avg_progress)

    def create_todo_for_assignee(self):
        """إنشاء مهمة في قائمة المهام للمُكلف"""
        if not self.assigned_to:
            return

        todo = frappe.new_doc("ToDo")
        todo.owner = self.assigned_to
        todo.description = f"مهمة جديدة: {self.task_title}"
        todo.reference_type = self.doctype
        todo.reference_name = self.name
        todo.priority = self.priority
        todo.date = self.end_date
        todo.insert()

    def send_assignment_notification(self):
        """إرسال إشعار التكليف"""
        if not self.assigned_to:
            return

        # إرسال بريد إلكتروني
        frappe.sendmail(
            recipients=[self.assigned_to],
            subject=f"مهمة جديدة: {self.task_title}",
            template="task_assignment",
            args={
                "task": self,
                "project_name": frappe.db.get_value("Project", self.project, "project_name")
            }
        )

        # إنشاء إشعار في النظام
        frappe.publish_realtime(
            event="task_assigned",
            message={
                "title": "مهمة جديدة",
                "message": f"تم تكليفك بمهمة: {self.task_title}",
                "task_id": self.name
            },
            user=self.assigned_to
        )

    def validate_completion(self):
        """التحقق من إمكانية إكمال المهمة"""
        if self.status != "Completed":
            frappe.throw(_("لا يمكن تأكيد المهمة إلا إذا كانت مكتملة"))

        if self.progress < 100:
            frappe.throw(_("لا يمكن تأكيد المهمة قبل إكمال جميع المهام الفرعية"))

    def create_timesheet_entries(self):
        """إنشاء إدخالات الوقت"""
        if not self.actual_hours or self.actual_hours <= 0:
            return

        timesheet = frappe.new_doc("Timesheet")
        timesheet.employee = frappe.db.get_value("Employee", {"user_id": self.assigned_to}, "name")

        if timesheet.employee:
            timesheet.append("time_logs", {
                "activity_type": "Task Execution",
                "from_time": f"{self.start_date} 09:00:00",
                "to_time": f"{self.end_date} 17:00:00",
                "hours": self.actual_hours,
                "project": self.project,
                "task": self.name
            })

            timesheet.insert()
            timesheet.submit()

    @frappe.whitelist()
    def update_subtask_status(self, subtask_idx, status):
        """تحديث حالة المهمة الفرعية"""
        if int(subtask_idx) < len(self.subtasks):
            self.subtasks[int(subtask_idx)].status = status
            self.calculate_progress()
            self.save()

            return {
                "success": True,
                "new_progress": self.progress
            }

        return {"success": False, "error": "Subtask not found"}

    @frappe.whitelist()
    def add_time_log(self, hours, description=""):
        """إضافة سجل وقت"""
        if not self.actual_hours:
            self.actual_hours = 0

        self.actual_hours += flt(hours)

        # إنشاء سجل تفصيلي
        time_log = frappe.new_doc("Task Time Log")
        time_log.task = self.name
        time_log.user = frappe.session.user
        time_log.hours = hours
        time_log.description = description
        time_log.log_date = frappe.utils.today()
        time_log.insert()

        self.save()

        return {
            "success": True,
            "total_hours": self.actual_hours
        }
```

---

## 🔧 حل المشاكل والأخطاء {#حل-المشاكل}

### **1. أخطاء قاعدة البيانات:**

#### **مشاكل الاتصال بقاعدة البيانات:**
```python
# custom_app/custom_module/troubleshooting.py

import frappe
from frappe import _

def diagnose_database_issues():
    """تشخيص مشاكل قاعدة البيانات"""

    issues = []

    try:
        # فحص الاتصال بقاعدة البيانات
        frappe.db.sql("SELECT 1")
        print("✅ الاتصال بقاعدة البيانات يعمل بشكل طبيعي")
    except Exception as e:
        issues.append(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")

    try:
        # فحص حجم قاعدة البيانات
        db_size = frappe.db.sql("""
            SELECT
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'DB Size in MB'
            FROM information_schema.tables
            WHERE table_schema = %s
        """, frappe.conf.db_name)[0][0]

        print(f"📊 حجم قاعدة البيانات: {db_size} MB")

        if db_size > 1000:  # أكثر من 1 GB
            issues.append(f"⚠️ قاعدة البيانات كبيرة الحجم: {db_size} MB")

    except Exception as e:
        issues.append(f"❌ خطأ في فحص حجم قاعدة البيانات: {str(e)}")

    try:
        # فحص الجداول المعطلة
        corrupted_tables = frappe.db.sql("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s AND engine IS NULL
        """, frappe.conf.db_name)

        if corrupted_tables:
            issues.append(f"❌ جداول معطلة: {[table[0] for table in corrupted_tables]}")
        else:
            print("✅ جميع الجداول سليمة")

    except Exception as e:
        issues.append(f"❌ خطأ في فحص الجداول: {str(e)}")

    # فحص الفهارس المفقودة
    try:
        missing_indexes = check_missing_indexes()
        if missing_indexes:
            issues.append(f"⚠️ فهارس مفقودة: {missing_indexes}")
    except Exception as e:
        issues.append(f"❌ خطأ في فحص الفهارس: {str(e)}")

    return issues

def check_missing_indexes():
    """فحص الفهارس المفقودة"""

    # فحص الجداول الكبيرة بدون فهارس مناسبة
    large_tables = frappe.db.sql("""
        SELECT
            table_name,
            table_rows
        FROM information_schema.tables
        WHERE table_schema = %s
        AND table_rows > 10000
        ORDER BY table_rows DESC
    """, frappe.conf.db_name, as_dict=True)

    missing_indexes = []

    for table in large_tables:
        # فحص وجود فهارس على الحقول المهمة
        indexes = frappe.db.sql("""
            SELECT column_name
            FROM information_schema.statistics
            WHERE table_schema = %s AND table_name = %s
        """, (frappe.conf.db_name, table.table_name))

        indexed_columns = [idx[0] for idx in indexes]

        # الحقول التي يجب أن تكون مفهرسة
        important_columns = ['modified', 'creation', 'owner', 'docstatus']

        for col in important_columns:
            if col not in indexed_columns:
                # التحقق من وجود العمود
                column_exists = frappe.db.sql("""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s
                """, (frappe.conf.db_name, table.table_name, col))

                if column_exists:
                    missing_indexes.append(f"{table.table_name}.{col}")

    return missing_indexes

def fix_database_issues():
    """إصلاح مشاكل قاعدة البيانات"""

    try:
        # إصلاح الجداول المعطلة
        frappe.db.sql("REPAIR TABLE")

        # تحسين الجداول
        frappe.db.sql("OPTIMIZE TABLE")

        # إعادة بناء الفهارس
        frappe.db.sql("ANALYZE TABLE")

        print("✅ تم إصلاح مشاكل قاعدة البيانات")

    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {str(e)}")

def create_missing_indexes():
    """إنشاء الفهارس المفقودة"""

    missing_indexes = check_missing_indexes()

    for index in missing_indexes:
        try:
            table_name, column_name = index.split('.')

            # إنشاء الفهرس
            frappe.db.sql(f"""
                CREATE INDEX idx_{column_name}
                ON `{table_name}` ({column_name})
            """)

            print(f"✅ تم إنشاء فهرس: {index}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء فهرس {index}: {str(e)}")
```

#### **مشاكل الأداء:**
```python
def analyze_slow_queries():
    """تحليل الاستعلامات البطيئة"""

    try:
        # تفعيل slow query log
        frappe.db.sql("SET GLOBAL slow_query_log = 'ON'")
        frappe.db.sql("SET GLOBAL long_query_time = 2")

        # جلب الاستعلامات البطيئة
        slow_queries = frappe.db.sql("""
            SELECT
                sql_text,
                exec_count,
                avg_timer_wait/1000000000 as avg_time_seconds,
                sum_timer_wait/1000000000 as total_time_seconds
            FROM performance_schema.events_statements_summary_by_digest
            WHERE avg_timer_wait > 2000000000
            ORDER BY avg_timer_wait DESC
            LIMIT 10
        """, as_dict=True)

        print("🐌 أبطأ الاستعلامات:")
        for query in slow_queries:
            print(f"- الوقت المتوسط: {query.avg_time_seconds:.2f}s")
            print(f"  الاستعلام: {query.sql_text[:100]}...")
            print()

    except Exception as e:
        print(f"❌ خطأ في تحليل الاستعلامات البطيئة: {str(e)}")

def optimize_database_performance():
    """تحسين أداء قاعدة البيانات"""

    optimizations = []

    try:
        # فحص إعدادات MySQL
        mysql_vars = frappe.db.sql("""
            SHOW VARIABLES WHERE Variable_name IN (
                'innodb_buffer_pool_size',
                'query_cache_size',
                'tmp_table_size',
                'max_heap_table_size'
            )
        """, as_dict=True)

        for var in mysql_vars:
            if var.Variable_name == 'innodb_buffer_pool_size':
                size_mb = int(var.Value) / 1024 / 1024
                if size_mb < 128:
                    optimizations.append("زيادة innodb_buffer_pool_size إلى 128MB على الأقل")

            elif var.Variable_name == 'query_cache_size':
                if int(var.Value) == 0:
                    optimizations.append("تفعيل query_cache_size")

        # فحص الجداول التي تحتاج تحسين
        tables_to_optimize = frappe.db.sql("""
            SELECT table_name, data_free
            FROM information_schema.tables
            WHERE table_schema = %s
            AND data_free > 1024*1024
        """, frappe.conf.db_name, as_dict=True)

        if tables_to_optimize:
            optimizations.append(f"تحسين {len(tables_to_optimize)} جدول يحتوي على مساحة فارغة")

        return optimizations

    except Exception as e:
        print(f"❌ خطأ في فحص الأداء: {str(e)}")
        return []
```

### **2. أخطاء البرمجة:**

#### **تشخيص أخطاء Python:**
```python
def debug_python_errors():
    """تشخيص أخطاء Python"""

    import traceback
    import sys

    def custom_exception_handler(exc_type, exc_value, exc_traceback):
        """معالج مخصص للأخطاء"""

        # تسجيل الخطأ
        error_details = {
            "type": exc_type.__name__,
            "message": str(exc_value),
            "traceback": traceback.format_exception(exc_type, exc_value, exc_traceback),
            "user": frappe.session.user,
            "site": frappe.local.site,
            "timestamp": frappe.utils.now()
        }

        # حفظ في سجل الأخطاء
        frappe.log_error(
            message=error_details["traceback"],
            title=f"{error_details['type']}: {error_details['message']}"
        )

        # إرسال تنبيه للمطورين
        if frappe.conf.get("developer_mode"):
            send_error_notification(error_details)

    # تعيين معالج الأخطاء
    sys.excepthook = custom_exception_handler

def send_error_notification(error_details):
    """إرسال تنبيه بالأخطاء للمطورين"""

    try:
        developers = frappe.get_all("User",
            filters={"role_profile_name": "Developer"},
            fields=["email"]
        )

        if developers:
            frappe.sendmail(
                recipients=[dev.email for dev in developers],
                subject=f"خطأ في النظام: {error_details['type']}",
                message=f"""
                حدث خطأ في النظام:

                النوع: {error_details['type']}
                الرسالة: {error_details['message']}
                المستخدم: {error_details['user']}
                الموقع: {error_details['site']}
                الوقت: {error_details['timestamp']}

                تفاصيل الخطأ:
                {''.join(error_details['traceback'])}
                """
            )
    except Exception as e:
        print(f"خطأ في إرسال تنبيه الخطأ: {str(e)}")

def validate_code_quality():
    """فحص جودة الكود"""

    import ast
    import os

    issues = []

    # فحص ملفات Python في التطبيق
    app_path = frappe.get_app_path("custom_app")

    for root, dirs, files in os.walk(app_path):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # فحص صحة بناء الجملة
                    try:
                        ast.parse(content)
                    except SyntaxError as e:
                        issues.append(f"خطأ في بناء الجملة في {file_path}: {str(e)}")

                    # فحص مشاكل شائعة
                    if 'frappe.db.sql(' in content and 'as_dict=True' not in content:
                        issues.append(f"استعلام SQL بدون as_dict=True في {file_path}")

                    if 'frappe.throw(' in content and '_(' not in content:
                        issues.append(f"رسالة خطأ غير مترجمة في {file_path}")

                except Exception as e:
                    issues.append(f"خطأ في قراءة الملف {file_path}: {str(e)}")

    return issues
```

#### **تشخيص أخطاء JavaScript:**
```javascript
// custom_app/public/js/error_handler.js

// معالج أخطاء JavaScript
window.addEventListener('error', function(event) {
    const errorDetails = {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error ? event.error.stack : null,
        user: frappe.session.user,
        timestamp: new Date().toISOString()
    };

    // إرسال الخطأ إلى الخادم
    frappe.call({
        method: 'custom_app.custom_module.troubleshooting.log_js_error',
        args: {
            error_details: errorDetails
        },
        callback: function(r) {
            console.log('تم تسجيل الخطأ');
        }
    });
});

// معالج الأخطاء غير المعالجة في Promise
window.addEventListener('unhandledrejection', function(event) {
    const errorDetails = {
        message: event.reason.message || 'Unhandled Promise Rejection',
        stack: event.reason.stack,
        user: frappe.session.user,
        timestamp: new Date().toISOString()
    };

    frappe.call({
        method: 'custom_app.custom_module.troubleshooting.log_js_error',
        args: {
            error_details: errorDetails
        }
    });
});

// دالة لتشخيص مشاكل الأداء
function diagnosePerformanceIssues() {
    // فحص استهلاك الذاكرة
    if (performance.memory) {
        const memoryInfo = {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };

        console.log('استهلاك الذاكرة:', memoryInfo);

        if (memoryInfo.used > memoryInfo.limit * 0.8) {
            console.warn('تحذير: استهلاك ذاكرة عالي');
        }
    }

    // فحص أداء التحميل
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        console.log(`وقت تحميل الصفحة: ${loadTime}ms`);

        if (loadTime > 3000) {
            console.warn('تحذير: وقت تحميل بطيء');
        }
    }
}

// تشغيل التشخيص عند تحميل الصفحة
$(document).ready(function() {
    diagnosePerformanceIssues();
});
```

### **3. أدوات التشخيص:**

#### **أداة شاملة للتشخيص:**
```python
# custom_app/custom_module/diagnostic_tool.py

import frappe
from frappe import _
import psutil
import os

@frappe.whitelist()
def run_system_diagnostics():
    """تشغيل تشخيص شامل للنظام"""

    diagnostics = {
        "system_info": get_system_info(),
        "database_health": check_database_health(),
        "application_status": check_application_status(),
        "performance_metrics": get_performance_metrics(),
        "security_check": run_security_check(),
        "recommendations": []
    }

    # تحليل النتائج وإضافة التوصيات
    diagnostics["recommendations"] = generate_recommendations(diagnostics)

    return diagnostics

def get_system_info():
    """معلومات النظام"""

    try:
        return {
            "os": os.name,
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_total": round(psutil.virtual_memory().total / 1024 / 1024 / 1024, 2),
            "memory_used": round(psutil.virtual_memory().used / 1024 / 1024 / 1024, 2),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": {
                "total": round(psutil.disk_usage('/').total / 1024 / 1024 / 1024, 2),
                "used": round(psutil.disk_usage('/').used / 1024 / 1024 / 1024, 2),
                "percent": round(psutil.disk_usage('/').percent, 2)
            }
        }
    except Exception as e:
        return {"error": str(e)}

def check_database_health():
    """فحص صحة قاعدة البيانات"""

    health_status = {
        "connection": False,
        "size_mb": 0,
        "table_count": 0,
        "corrupted_tables": [],
        "slow_queries": []
    }

    try:
        # فحص الاتصال
        frappe.db.sql("SELECT 1")
        health_status["connection"] = True

        # حجم قاعدة البيانات
        size_result = frappe.db.sql("""
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = %s
        """, frappe.conf.db_name)

        if size_result:
            health_status["size_mb"] = size_result[0][0] or 0

        # عدد الجداول
        table_count = frappe.db.sql("""
            SELECT COUNT(*) FROM information_schema.tables
            WHERE table_schema = %s
        """, frappe.conf.db_name)

        if table_count:
            health_status["table_count"] = table_count[0][0]

    except Exception as e:
        health_status["error"] = str(e)

    return health_status

def check_application_status():
    """فحص حالة التطبيق"""

    status = {
        "installed_apps": [],
        "custom_fields": 0,
        "custom_doctypes": 0,
        "active_users": 0,
        "recent_errors": []
    }

    try:
        # التطبيقات المثبتة
        status["installed_apps"] = frappe.get_installed_apps()

        # الحقول المخصصة
        status["custom_fields"] = frappe.db.count("Custom Field")

        # DocTypes المخصصة
        status["custom_doctypes"] = frappe.db.count("DocType", {"custom": 1})

        # المستخدمون النشطون
        status["active_users"] = frappe.db.count("User", {"enabled": 1})

        # الأخطاء الأخيرة
        recent_errors = frappe.get_all("Error Log",
            filters={"creation": [">", frappe.utils.add_days(frappe.utils.today(), -7)]},
            fields=["error", "creation"],
            limit=5,
            order_by="creation desc"
        )
        status["recent_errors"] = recent_errors

    except Exception as e:
        status["error"] = str(e)

    return status

def get_performance_metrics():
    """مقاييس الأداء"""

    metrics = {
        "response_time": 0,
        "cache_hit_ratio": 0,
        "active_sessions": 0,
        "background_jobs": 0
    }

    try:
        # قياس وقت الاستجابة
        import time
        start_time = time.time()
        frappe.db.sql("SELECT COUNT(*) FROM `tabUser`")
        metrics["response_time"] = round((time.time() - start_time) * 1000, 2)

        # الجلسات النشطة
        metrics["active_sessions"] = frappe.db.count("Sessions")

        # المهام في الخلفية
        metrics["background_jobs"] = frappe.db.count("RQ Job")

    except Exception as e:
        metrics["error"] = str(e)

    return metrics

def run_security_check():
    """فحص الأمان"""

    security_issues = []

    try:
        # فحص المستخدمين بدون كلمة مرور
        users_without_password = frappe.db.sql("""
            SELECT name FROM `tabUser`
            WHERE enabled = 1 AND password IS NULL
        """)

        if users_without_password:
            security_issues.append(f"مستخدمون بدون كلمة مرور: {len(users_without_password)}")

        # فحص الصلاحيات المفرطة
        admin_users = frappe.db.sql("""
            SELECT COUNT(*) FROM `tabHas Role`
            WHERE role = 'Administrator' AND parent != 'Administrator'
        """)[0][0]

        if admin_users > 2:
            security_issues.append(f"عدد كبير من المديرين: {admin_users}")

        # فحص إعدادات الأمان
        if not frappe.conf.get("encryption_key"):
            security_issues.append("مفتاح التشفير غير محدد")

    except Exception as e:
        security_issues.append(f"خطأ في فحص الأمان: {str(e)}")

    return security_issues

def generate_recommendations(diagnostics):
    """توليد التوصيات بناءً على التشخيص"""

    recommendations = []

    # توصيات النظام
    if diagnostics["system_info"].get("memory_percent", 0) > 80:
        recommendations.append("زيادة ذاكرة النظام - الاستخدام الحالي عالي")

    if diagnostics["system_info"].get("disk_usage", {}).get("percent", 0) > 85:
        recommendations.append("تنظيف مساحة القرص الصلب")

    # توصيات قاعدة البيانات
    if diagnostics["database_health"].get("size_mb", 0) > 1000:
        recommendations.append("تحسين قاعدة البيانات وأرشفة البيانات القديمة")

    # توصيات الأداء
    if diagnostics["performance_metrics"].get("response_time", 0) > 1000:
        recommendations.append("تحسين أداء الاستعلامات")

    # توصيات الأمان
    if diagnostics["security_check"]:
        recommendations.extend([f"إصلاح مشكلة أمان: {issue}" for issue in diagnostics["security_check"]])

    return recommendations

@frappe.whitelist()
def export_diagnostic_report():
    """تصدير تقرير التشخيص"""

    diagnostics = run_system_diagnostics()

    # إنشاء ملف PDF
    html_content = frappe.render_template("diagnostic_report.html", {"data": diagnostics})

    pdf = frappe.utils.pdf.get_pdf(html_content)

    # حفظ الملف
    file_name = f"diagnostic_report_{frappe.utils.today()}.pdf"

    file_doc = frappe.new_doc("File")
    file_doc.file_name = file_name
    file_doc.content = pdf
    file_doc.is_private = 1
    file_doc.insert()

    return file_doc.file_url
```