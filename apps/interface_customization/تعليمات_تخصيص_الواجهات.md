# تعليمات تخصيص الواجهات - دليل المطور الشامل

## 📋 نظرة عامة على التطبيق

**اسم التطبيق**: `interface_customization`
**الهدف**: تبسيط واجهات ERPNext للمستخدمين العرب
**النوع**: تطبيق Frappe مخصص
**الإصدار**: 1.0.0

---

## 🗂️ بنية المجلدات والملفات

```
apps/interface_customization/
├── interface_customization/
│   ├── __init__.py                           # ملف التهيئة الأساسي
│   ├── hooks.py                              # ملف الإعدادات الرئيسي
│   ├── install.py                            # ملف التثبيت والإعداد
│   ├── modules.txt                           # قائمة الوحدات
│   ├── patches.txt                           # ملف التحديثات
│   ├── config/
│   │   └── interface_customization.py       # إعدادات التطبيق في القائمة
│   ├── custom_interface/
│   │   ├── __init__.py                       # تهيئة الوحدة
│   │   ├── sales_invoice_customization.py   # منطق فاتورة المبيعات
│   │   └── pos_invoice_customization.py     # منطق الفاتورة النقدية
│   ├── public/
│   │   ├── js/
│   │   │   ├── sales_invoice_custom.js       # تخصيصات JS للفاتورة العادية
│   │   │   └── pos_invoice_custom.js         # تخصيصات JS للفاتورة النقدية
│   │   └── css/
│   │       ├── sales_invoice_custom.css      # تنسيقات الفاتورة العادية
│   │       └── pos_invoice_custom.css        # تنسيقات الفاتورة النقدية
│   └── templates/
├── README.md                                 # دليل التطبيق
├── SUMMARY.md                                # ملخص شامل للتطبيق
├── POS_INVOICE_GUIDE.md                      # دليل الفواتير النقدية
├── تعليمات_تخصيص_الواجهات.md                # هذا الملف
├── setup.py                                  # ملف إعداد Python
├── requirements.txt                          # متطلبات Python
├── package.json                              # إعدادات Node.js
└── license.txt                               # ملف الترخيص
```

---

## 📄 شرح تفصيلي لكل ملف

### 1. ملف الإعدادات الرئيسي - `hooks.py`

**الموقع**: `interface_customization/hooks.py`
**الوظيفة**: ملف التحكم الرئيسي في التطبيق

#### المحتوى والوظائف:

```python
# معلومات التطبيق الأساسية
app_name = "interface_customization"
app_title = "تخصيص الواجهات"
app_publisher = "Moneer"
app_description = "تطبيق لتبسيط واجهات ERPNext"
app_version = "0.0.1"

# ربط ملفات CSS و JavaScript
app_include_css = [
    "/assets/interface_customization/css/sales_invoice_custom.css",
    "/assets/interface_customization/css/pos_invoice_custom.css"
]

# ربط ملفات JavaScript بالـ Doctypes
doctype_js = {
    "Sales Invoice": "public/js/sales_invoice_custom.js",
    "POS Invoice": "public/js/pos_invoice_custom.js"
}

# ربط الأحداث بالـ Doctypes
doc_events = {
    "Sales Invoice": {
        "before_save": "interface_customization.custom_interface.sales_invoice_customization.before_save",
        "on_submit": "interface_customization.custom_interface.sales_invoice_customization.on_submit_sales_invoice",
        "validate": "interface_customization.custom_interface.sales_invoice_customization.validate_sales_invoice_permissions"
    },
    "POS Invoice": {
        "before_save": "interface_customization.custom_interface.pos_invoice_customization.before_save_pos",
        "on_submit": "interface_customization.custom_interface.pos_invoice_customization.on_submit_pos_invoice",
        "validate": "interface_customization.custom_interface.pos_invoice_customization.validate_pos_invoice"
    }
}

# إعدادات التثبيت
after_install = "interface_customization.install.after_install"
before_uninstall = "interface_customization.install.before_uninstall"
```

#### الارتباطات:
- **يربط مع**: جميع ملفات التطبيق
- **يتحكم في**: تحميل CSS/JS، ربط الأحداث، التثبيت
- **يؤثر على**: Sales Invoice, POS Invoice

---

### 2. ملف تخصيصات فاتورة المبيعات - `sales_invoice_custom.js`

**الموقع**: `interface_customization/public/js/sales_invoice_custom.js`
**الوظيفة**: تخصيص واجهة فاتورة المبيعات العادية

#### الوظائف الرئيسية:

```javascript
// الحدث الرئيسي عند تحميل النموذج
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        hide_unnecessary_fields(frm);      // إخفاء الحقول غير الضرورية
        reorganize_fields(frm);            // إعادة ترتيب الحقول
        customize_labels(frm);             // تطبيق التسميات العربية
        add_custom_sections(frm);          // إضافة أقسام مخصصة
        customize_items_table(frm);        // تخصيص جدول البنود
        add_helpful_info(frm);             // إضافة معلومات مفيدة
    },

    customer: function(frm) {
        show_customer_info(frm);           // عرض معلومات العميل
    }
});

// تخصيص بنود الفاتورة
frappe.ui.form.on('Sales Invoice Item', {
    item_code: function(frm, cdt, cdn) {
        // تحديث معلومات المخزون عند اختيار صنف
    },
    qty: function(frm, cdt, cdn) {
        // التحقق من الكمية المتاحة
    }
});
```

#### الوظائف المساعدة:

1. **`hide_unnecessary_fields(frm)`**
   - إخفاء 40+ حقل غير ضروري
   - إخفاء أقسام كاملة معقدة

2. **`customize_labels(frm)`**
   - تطبيق تسميات عربية واضحة
   - تحسين فهم المستخدم

3. **`customize_items_table(frm)`**
   - إخفاء أعمدة غير ضرورية
   - إعادة ترتيب الأعمدة المهمة
   - إضافة معلومات المخزون

4. **`add_stock_info_to_items(frm)`**
   - استدعاء API للحصول على معلومات المخزون
   - عرض تحذيرات نقص المخزون

#### الارتباطات:
- **يربط مع**: `sales_invoice_customization.py`
- **يستدعي**: APIs من الملف Python
- **يؤثر على**: واجهة Sales Invoice فقط

---

### 3. ملف تخصيصات الفاتورة النقدية - `pos_invoice_custom.js`

**الموقع**: `interface_customization/public/js/pos_invoice_custom.js`
**الوظيفة**: تخصيص واجهة الفاتورة النقدية

#### الوظائف المتخصصة للفواتير النقدية:

```javascript
frappe.ui.form.on('POS Invoice', {
    refresh: function(frm) {
        hide_unnecessary_pos_fields(frm);     // إخفاء 50+ حقل خاص بالنقدي
        customize_payment_section(frm);       // تحسين قسم الدفع
        apply_pos_styling(frm);               // تطبيق تصميم النقدي
    },

    grand_total: function(frm) {
        update_payment_info(frm);             // تحديث معلومات الدفع
    }
});
```

#### الوظائف المتخصصة:

1. **`customize_payment_section(frm)`**
   - تحسين عرض طرق الدفع
   - إضافة ملخص الدفع الواضح

2. **`add_payment_summary(frm)`**
   - عرض المجموع، المدفوع، الباقي
   - تحديث فوري عند التغيير

3. **`apply_pos_styling(frm)`**
   - تطبيق ألوان مخصصة للنقدي
   - تحسين الأيقونات والتأثيرات

#### الارتباطات:
- **يربط مع**: `pos_invoice_customization.py`
- **يستدعي**: APIs خاصة بالفواتير النقدية
- **يؤثر على**: واجهة POS Invoice فقط

---

### 4. ملف منطق فاتورة المبيعات - `sales_invoice_customization.py`

**الموقع**: `interface_customization/custom_interface/sales_invoice_customization.py`
**الوظيفة**: المنطق الخلفي لفاتورة المبيعات

#### الوظائف الرئيسية:

```python
def before_save(doc, method):
    """معالجة قبل حفظ الفاتورة"""
    validate_basic_data(doc)          # التحقق من البيانات
    update_stock_info(doc)            # تحديث معلومات المخزون
    apply_custom_business_rules(doc)  # تطبيق قواعد العمل

def validate_basic_data(doc):
    """التحقق من البيانات الأساسية"""
    # التحقق من وجود العميل والبنود
    # التحقق من الكميات والأسعار

def apply_custom_business_rules(doc):
    """تطبيق قواعد العمل المخصصة"""
    apply_vip_customer_discount(doc)  # خصم العملاء المميزين
    set_default_cost_center(doc)      # مركز التكلفة التلقائي
    apply_custom_pricing_rules(doc)   # قواعد التسعير

@frappe.whitelist()
def get_item_stock_info(item_code, warehouse=None):
    """API للحصول على معلومات المخزون"""
    # إرجاع الكمية المتاحة ومعدل التقييم

@frappe.whitelist()
def get_customer_summary(customer):
    """API للحصول على ملخص العميل"""
    # إرجاع إجمالي المبيعات وعدد الفواتير
```

#### الارتباطات:
- **يستدعى من**: `sales_invoice_custom.js`
- **يتفاعل مع**: Sales Invoice doctype
- **يستخدم**: ERPNext stock utils

---

### 5. ملف منطق الفاتورة النقدية - `pos_invoice_customization.py`

**الموقع**: `interface_customization/custom_interface/pos_invoice_customization.py`
**الوظيفة**: المنطق الخلفي للفاتورة النقدية

#### الوظائف المتخصصة:

```python
def before_save_pos(doc, method):
    """معالجة قبل حفظ الفاتورة النقدية"""
    validate_pos_basic_data(doc)      # التحقق من البيانات النقدية
    update_pos_payment_info(doc)      # تحديث معلومات الدفع

def validate_pos_payment_data(doc):
    """التحقق من بيانات الدفع"""
    # التحقق من طرق الدفع
    # حساب مبلغ الباقي

def apply_pos_business_rules(doc):
    """قواعد العمل للفواتير النقدية"""
    apply_pos_vip_discount(doc)       # خصم 10% للعملاء المميزين
    apply_pos_quick_pricing(doc)      # خصومات الكمية المحسنة

def get_default_pos_customer(company):
    """الحصول على العميل الافتراضي"""
    # إنشاء "عميل نقدي" إذا لم يكن موجود

@frappe.whitelist()
def get_pos_daily_summary():
    """API للملخص اليومي لنقطة البيع"""
    # إرجاع إجمالي المبيعات وعدد الفواتير اليوم

@frappe.whitelist()
def get_pos_item_info(item_code, warehouse=None):
    """API لمعلومات الصنف في النقدي"""
    # إرجاع الكمية المتاحة وآخر سعر بيع
```

#### الارتباطات:
- **يستدعى من**: `pos_invoice_custom.js`
- **يتفاعل مع**: POS Invoice doctype
- **ينشئ**: عميل افتراضي للمبيعات النقدية

---

### 6. ملفات التنسيق CSS

#### أ. `sales_invoice_custom.css`
**الوظيفة**: تنسيق فاتورة المبيعات العادية

```css
/* تحسين عرض النموذج العام */
.form-layout .form-page {
    background: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تنسيق قسم العميل */
.customer-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

/* تنسيق جدول البنود */
.grid-header-row {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
}

/* تنسيق المجموع الإجمالي */
.form-control[data-fieldname="grand_total"] {
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}
```

#### ب. `pos_invoice_custom.css`
**الوظيفة**: تنسيق الفاتورة النقدية

```css
/* تصميم خاص للفواتير النقدية */
.pos-customer-section {
    background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    color: white;
}

/* قسم الدفع للفواتير النقدية */
.pos-payment-section {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

/* تحسين المجموع الإجمالي للنقدي */
.form-control[data-fieldname="grand_total"] {
    font-size: 28px;
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

/* تحسين مبلغ الباقي */
.form-control[data-fieldname="change_amount"] {
    font-size: 24px;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
}
```

#### الارتباطات:
- **يتم تحميلها من**: `hooks.py`
- **تؤثر على**: واجهات المستخدم
- **تعمل مع**: ملفات JavaScript

---

### 7. ملف التثبيت - `install.py`

**الموقع**: `interface_customization/install.py`
**الوظيفة**: إعداد التطبيق عند التثبيت

#### الوظائف:

```python
def after_install():
    """تنفيذ بعد التثبيت"""
    create_custom_fields_for_app()    # إنشاء الحقول المخصصة
    apply_property_setters()          # تطبيق إعدادات الخصائص
    create_default_data()             # إنشاء البيانات الافتراضية
    setup_permissions()               # إعداد الصلاحيات

def create_custom_fields_for_app():
    """إنشاء الحقول المخصصة"""
    # إضافة حقول: actual_qty, valuation_rate للبنود
    # إضافة حقل: is_vip_customer للعملاء

def create_default_data():
    """إنشاء البيانات الافتراضية"""
    # إنشاء مجموعة عملاء VIP
    # إنشاء قالب ضرائب مبسط

def before_uninstall():
    """تنظيف قبل إلغاء التثبيت"""
    remove_custom_fields()            # حذف الحقول المخصصة
    remove_property_setters()         # حذف إعدادات الخصائص
```

#### الارتباطات:
- **يستدعى من**: `hooks.py`
- **ينشئ**: حقول مخصصة، بيانات افتراضية
- **يؤثر على**: قاعدة البيانات

---

### 8. ملف إعدادات التطبيق - `config/interface_customization.py`

**الوظيفة**: إعدادات التطبيق في قائمة ERPNext

```python
def get_data():
    """إعدادات التطبيق في القائمة"""
    return [
        {
            "label": _("تخصيص الواجهات"),
            "icon": "fa fa-cogs",
            "items": [
                {
                    "type": "doctype",
                    "name": "Sales Invoice",
                    "label": _("فاتورة المبيعات المبسطة")
                },
                {
                    "type": "doctype",
                    "name": "POS Invoice",
                    "label": _("فاتورة نقدية مبسطة")
                }
            ]
        }
    ]
```

---

## 🔧 الأوامر والعمليات

### أوامر التثبيت:

```bash
# 1. إنشاء التطبيق
bench new-app interface_customization

# 2. تثبيت التطبيق على الموقع
bench --site site1.local install-app interface_customization

# 3. بناء الأصول
bench build --app interface_customization

# 4. مسح الذاكرة المؤقتة
bench --site site1.local clear-cache

# 5. إعادة تشغيل النظام
bench restart
```

### أوامر التطوير:

```bash
# بناء التطبيق فقط
bench build --app interface_customization

# مراقبة التغييرات
bench watch

# تشغيل الاختبارات
bench --site site1.local run-tests --app interface_customization

# تحديث قاعدة البيانات
bench --site site1.local migrate
```

### أوامر استكشاف الأخطاء:

```bash
# عرض سجلات الأخطاء
bench --site site1.local logs

# فحص حالة التطبيق
bench --site site1.local list-apps

# إعادة تثبيت التطبيق
bench --site site1.local uninstall-app interface_customization
bench --site site1.local install-app interface_customization
```

---

## 🔗 مخطط الارتباطات

```
hooks.py (المتحكم الرئيسي)
├── يحمل CSS Files
│   ├── sales_invoice_custom.css
│   └── pos_invoice_custom.css
├── يربط JS Files
│   ├── sales_invoice_custom.js → Sales Invoice
│   └── pos_invoice_custom.js → POS Invoice
├── يربط Python Events
│   ├── sales_invoice_customization.py → Sales Invoice Events
│   └── pos_invoice_customization.py → POS Invoice Events
└── يستدعي install.py عند التثبيت

JavaScript Files
├── sales_invoice_custom.js
│   ├── يستدعي APIs من sales_invoice_customization.py
│   ├── يطبق تنسيقات من sales_invoice_custom.css
│   └── يؤثر على Sales Invoice Form
└── pos_invoice_custom.js
    ├── يستدعي APIs من pos_invoice_customization.py
    ├── يطبق تنسيقات من pos_invoice_custom.css
    └── يؤثر على POS Invoice Form

Python Files
├── sales_invoice_customization.py
│   ├── يوفر APIs للـ JavaScript
│   ├── يتفاعل مع Sales Invoice Events
│   └── يستخدم ERPNext Stock Utils
└── pos_invoice_customization.py
    ├── يوفر APIs للـ JavaScript
    ├── يتفاعل مع POS Invoice Events
    └── ينشئ بيانات افتراضية للنقدي
```

---

## 📊 جدول الوظائف والملفات

| الوظيفة | الملف المسؤول | النوع | الارتباطات |
|---------|---------------|-------|------------|
| إخفاء الحقول | `*_custom.js` | JavaScript | hooks.py |
| التسميات العربية | `*_custom.js` | JavaScript | - |
| التنسيق البصري | `*_custom.css` | CSS | hooks.py |
| التحقق من البيانات | `*_customization.py` | Python | doc_events |
| معلومات المخزون | `*_customization.py` | Python | APIs |
| خصومات العملاء | `*_customization.py` | Python | before_save |
| إنشاء الحقول | `install.py` | Python | after_install |
| إعدادات القائمة | `config/*.py` | Python | - |

---

## 🎯 نصائح للتطوير

### 1. إضافة حقل جديد للإخفاء:
```javascript
// في ملف *_custom.js
const fields_to_hide = [
    'existing_field',
    'new_field_to_hide'  // أضف هنا
];
```

### 2. إضافة تسمية عربية جديدة:
```javascript
// في ملف *_custom.js
const arabic_labels = {
    'field_name': 'التسمية العربية الجديدة'
};
```

### 3. إضافة API جديد:
```python
# في ملف *_customization.py
@frappe.whitelist()
def new_api_function():
    """وصف الوظيفة"""
    return {"result": "data"}
```

### 4. تعديل الألوان:
```css
/* في ملف *_custom.css */
.new-section {
    background: linear-gradient(135deg, #color1, #color2);
}
```

---

## 🚨 تحذيرات مهمة

1. **لا تعدل ملفات ERPNext الأساسية** - استخدم التخصيصات فقط
2. **اختبر التغييرات** على بيئة تطوير أولاً
3. **احتفظ بنسخ احتياطية** قبل التحديثات
4. **استخدم أوامر bench** للعمليات
5. **تأكد من الصلاحيات** قبل النشر

---

## 📚 أمثلة عملية للتخصيص

### مثال 1: إضافة حقل جديد للإخفاء

**الهدف**: إخفاء حقل "shipping_rule" من فاتورة المبيعات

**الخطوات**:
1. افتح ملف `sales_invoice_custom.js`
2. ابحث عن `fields_to_hide`
3. أضف الحقل الجديد:

```javascript
const fields_to_hide = [
    'title', 'naming_series', 'tax_id',
    'shipping_rule',  // الحقل الجديد المراد إخفاؤه
    // باقي الحقول...
];
```

4. احفظ الملف وشغل:
```bash
bench build --app interface_customization
bench --site site1.local clear-cache
```

### مثال 2: إضافة تسمية عربية جديدة

**الهدف**: تغيير تسمية حقل "territory" إلى "المنطقة الجغرافية"

**الخطوات**:
1. افتح ملف `sales_invoice_custom.js`
2. ابحث عن `arabic_labels`
3. أضف أو عدّل:

```javascript
const arabic_labels = {
    'customer': 'العميل',
    'territory': 'المنطقة الجغرافية',  // تسمية جديدة
    // باقي التسميات...
};
```

### مثال 3: إنشاء API جديد

**الهدف**: إنشاء API للحصول على أفضل العملاء

**الخطوات**:
1. افتح ملف `sales_invoice_customization.py`
2. أضف الدالة الجديدة:

```python
@frappe.whitelist()
def get_top_customers(limit=10):
    """
    الحصول على أفضل العملاء حسب المبيعات
    Get top customers by sales
    """
    try:
        customers = frappe.db.sql("""
            SELECT
                customer,
                customer_name,
                SUM(grand_total) as total_sales,
                COUNT(*) as invoice_count
            FROM `tabSales Invoice`
            WHERE docstatus = 1
            GROUP BY customer
            ORDER BY total_sales DESC
            LIMIT %s
        """, limit, as_dict=True)

        return customers
    except Exception as e:
        frappe.log_error(f"Error getting top customers: {str(e)}")
        return []
```

3. استدعاء API من JavaScript:

```javascript
// في ملف sales_invoice_custom.js
frappe.call({
    method: 'interface_customization.custom_interface.sales_invoice_customization.get_top_customers',
    args: {
        limit: 5
    },
    callback: function(r) {
        if (r.message) {
            console.log('أفضل العملاء:', r.message);
        }
    }
});
```

### مثال 4: تخصيص ألوان جديدة

**الهدف**: تغيير لون قسم العميل إلى الأحمر

**الخطوات**:
1. افتح ملف `sales_invoice_custom.css`
2. عدّل أو أضف:

```css
/* تخصيص لون قسم العميل */
.customer-section {
    background: linear-gradient(135deg, #e53935 0%, #c62828 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

/* تخصيص حقل العميل */
.form-control[data-fieldname="customer"] {
    border: 3px solid #e53935;
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}
```

---

## 🔍 دليل استكشاف الأخطاء

### خطأ: التخصيصات لا تظهر

**الأسباب المحتملة**:
1. لم يتم بناء التطبيق
2. الذاكرة المؤقتة لم تُمسح
3. خطأ في مسار الملفات

**الحلول**:
```bash
# 1. بناء التطبيق
bench build --app interface_customization

# 2. مسح الذاكرة المؤقتة
bench --site site1.local clear-cache

# 3. إعادة تشغيل النظام
bench restart

# 4. فحص سجلات الأخطاء
bench --site site1.local logs
```

### خطأ: JavaScript لا يعمل

**الأسباب المحتملة**:
1. خطأ في صيغة JavaScript
2. مسار خاطئ في hooks.py
3. ملف غير موجود

**الحلول**:
1. فحص وحدة تحكم المتصفح (F12)
2. التأكد من مسار الملف في hooks.py:
```python
doctype_js = {
    "Sales Invoice": "public/js/sales_invoice_custom.js"  # تأكد من المسار
}
```

### خطأ: CSS لا يطبق

**الأسباب المحتملة**:
1. مسار خاطئ في hooks.py
2. خطأ في صيغة CSS
3. تضارب مع CSS آخر

**الحلول**:
1. التأكد من مسار CSS في hooks.py:
```python
app_include_css = [
    "/assets/interface_customization/css/sales_invoice_custom.css"
]
```

2. استخدام `!important` عند الضرورة:
```css
.form-control[data-fieldname="customer"] {
    background: red !important;
}
```

### خطأ: Python API لا يعمل

**الأسباب المحتملة**:
1. خطأ في صيغة Python
2. عدم وجود `@frappe.whitelist()`
3. خطأ في مسار الاستدعاء

**الحلول**:
1. التأكد من وجود decorator:
```python
@frappe.whitelist()
def my_function():
    pass
```

2. فحص سجلات الأخطاء:
```bash
bench --site site1.local logs
```

---

## 📋 قائمة مراجعة التطوير

### قبل إضافة تخصيص جديد:
- [ ] تحديد الهدف من التخصيص
- [ ] اختيار الملف المناسب للتعديل
- [ ] عمل نسخة احتياطية من الملف
- [ ] اختبار التخصيص على بيئة تطوير

### بعد إضافة التخصيص:
- [ ] بناء التطبيق: `bench build --app interface_customization`
- [ ] مسح الذاكرة المؤقتة: `bench --site site1.local clear-cache`
- [ ] اختبار الوظيفة الجديدة
- [ ] فحص وحدة تحكم المتصفح للأخطاء
- [ ] توثيق التغيير

### قبل النشر للإنتاج:
- [ ] اختبار شامل على بيئة التطوير
- [ ] مراجعة الكود مع فريق العمل
- [ ] عمل نسخة احتياطية من قاعدة البيانات
- [ ] تطبيق التحديث في وقت قليل الاستخدام
- [ ] مراقبة النظام بعد النشر

---

## 🎓 مصادر التعلم الإضافية

### وثائق Frappe Framework:
- [Frappe Framework Documentation](https://frappeframework.com/docs)
- [ERPNext Developer Guide](https://docs.erpnext.com/docs/user/manual/en/customize-erpnext)

### أمثلة كود مفيدة:
```javascript
// الحصول على قيمة حقل
let customer = frm.doc.customer;

// تعيين قيمة حقل
frm.set_value('customer', 'CUST-001');

// إخفاء/إظهار حقل
frm.toggle_display('field_name', false);

// إضافة زر مخصص
frm.add_custom_button('اسم الزر', function() {
    // الكود هنا
});

// استدعاء API
frappe.call({
    method: 'app.module.file.function',
    args: {
        'param': 'value'
    },
    callback: function(r) {
        console.log(r.message);
    }
});
```

### نصائح الأداء:
1. **استخدم setTimeout** للعمليات الثقيلة
2. **تجنب الاستدعاءات المتكررة** للـ APIs
3. **استخدم CSS بدلاً من JavaScript** للتنسيق
4. **اختبر على بيانات كبيرة** قبل النشر

---

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
- **البريد الإلكتروني**: <EMAIL>
- **المجتمع**: [Frappe Community Forum](https://discuss.frappe.io/)
- **التوثيق**: ملفات README في التطبيق

### للإبلاغ عن مشاكل:
1. وصف المشكلة بالتفصيل
2. إرفاق لقطات شاشة
3. تضمين رسائل الأخطاء
4. ذكر خطوات إعادة إنتاج المشكلة

---

هذا المستند الشامل يوضح كل تفاصيل التطبيق المخصص ويمكن استخدامه كمرجع كامل للتطوير والصيانة والتطوير المستقبلي.
