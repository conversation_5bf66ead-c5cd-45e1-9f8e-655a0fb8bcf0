# دليل الفواتير النقدية المبسطة - POS Invoice Guide

## 🎯 نظرة عامة

تم تطوير واجهة مبسطة خصيصاً للفواتير النقدية (POS Invoice) لتسهيل عملية البيع النقدي وتسريع المعاملات في نقاط البيع.

## ✨ المميزات الجديدة للفواتير النقدية

### 🎨 تصميم مخصص للمبيعات النقدية
- **ألوان مميزة**: تدرجات زرقاء وخضراء مخصصة للفواتير النقدية
- **أقسام واضحة**: تقسيم منطقي للعميل، البنود، والدفع
- **أيقونات تعبيرية**: 🧑‍💼 للعميل، 🛒 للبنود، 💳 للدفع

### 📱 واجهة سريعة ومبسطة
- **حقول أقل**: إخفاء أكثر من 50 حقل غير ضروري للمبيعات النقدية
- **تركيز على الأساسيات**: العميل، البنود، المجموع، الدفع
- **مسح باركود محسن**: حقل بارز لمسح الأصناف بسرعة

### 💰 إدارة دفع ذكية
- **ملخص دفع واضح**: عرض المجموع، المدفوع، والباقي
- **حساب تلقائي للباقي**: تحديث فوري عند تغيير المبلغ
- **طرق دفع متعددة**: دعم النقد، البطاقات، والتحويلات

## 🚀 كيفية الاستخدام

### 1. إنشاء فاتورة نقدية جديدة
```
المبيعات > فاتورة نقدية (POS Invoice) > جديد
```

### 2. الحقول المعروضة (المبسطة)
- **بيانات العميل**: العميل، اسم العميل، التاريخ
- **إعدادات نقطة البيع**: ملف نقطة البيع، المخزن
- **البنود**: رمز الصنف، اسم الصنف، الكمية، السعر، المبلغ
- **المجاميع**: المجموع، الضرائب، المجموع الإجمالي
- **الدفع**: طريقة الدفع، المبلغ المدفوع، الباقي

### 3. الحقول المخفية (المبسطة)
- تفاصيل العناوين المعقدة
- معلومات أوامر الشراء
- تفاصيل العملة والتحويل
- قواعد التسعير المعقدة
- الجداول الزمنية والمشاريع
- فريق المبيعات والعمولات

## 🎨 التحسينات البصرية

### ألوان الأقسام
- **قسم العميل**: تدرج أزرق (#1e88e5 → #1565c0)
- **قسم البنود**: تدرج أخضر (#43a047 → #2e7d32)  
- **قسم المجاميع**: تدرج برتقالي (#ff9800 → #f57c00)
- **قسم الدفع**: تدرج أخضر داكن (#4caf50 → #388e3c)

### الحقول المهمة
- **المجموع الإجمالي**: خط كبير (28px) بخلفية برتقالية
- **المبلغ المدفوع**: خط كبير (24px) بخلفية خضراء
- **مبلغ الباقي**: خط كبير (24px) بخلفية زرقاء
- **مسح الباركود**: حقل بارز بخلفية بنفسجية

## ⚡ الوظائف الذكية

### 1. إدارة العملاء
- **عميل افتراضي**: إنشاء "عميل نقدي" تلقائياً إذا لم يحدد
- **معلومات العميل**: عرض ملخص سريع للعميل المختار
- **خصومات VIP**: خصم 10% تلقائي للعملاء المميزين

### 2. إدارة المخزون
- **تحديث تلقائي**: ربط مع إعدادات ملف نقطة البيع
- **تحذيرات المخزون**: تنبيه عند نقص الكمية المتاحة
- **معلومات الأصناف**: عرض آخر سعر بيع للصنف

### 3. خصومات الكمية
- **10+ قطع**: خصم 5%
- **20+ قطعة**: خصم 10%  
- **50+ قطعة**: خصم 15%

### 4. إدارة الوقت
- **وقت تلقائي**: تسجيل وقت الفاتورة تلقائياً
- **تاريخ اليوم**: تعيين تاريخ اليوم افتراضياً

## 📊 التقارير والإحصائيات

### ملخص يومي لنقطة البيع
```javascript
// API للحصول على ملخص يومي
frappe.call({
    method: 'interface_customization.custom_interface.pos_invoice_customization.get_pos_daily_summary',
    callback: function(r) {
        console.log(r.message);
        // {
        //     total_sales: 15000,
        //     invoice_count: 25,
        //     total_qty: 150,
        //     avg_invoice_value: 600
        // }
    }
});
```

### معلومات الأصناف
```javascript
// API للحصول على معلومات الصنف
frappe.call({
    method: 'interface_customization.custom_interface.pos_invoice_customization.get_pos_item_info',
    args: {
        item_code: 'ITEM001',
        warehouse: 'Main Store'
    },
    callback: function(r) {
        console.log(r.message);
        // {
        //     actual_qty: 50,
        //     last_rate: 100,
        //     item_name: 'Product Name'
        // }
    }
});
```

## 🔧 التخصيص والإعداد

### إعداد ملف نقطة البيع
1. انتقل إلى **المحاسبة > إعداد > ملف نقطة البيع**
2. أنشئ ملف جديد أو عدّل موجود
3. حدد:
   - الشركة والمخزن
   - طرق الدفع المتاحة
   - تفعيل تحديث المخزون
   - العملاء والأصناف المسموحة

### إنشاء عميل افتراضي
```python
# سيتم إنشاؤه تلقائياً بالاسم "عميل نقدي"
# أو يمكن إنشاؤه يدوياً:
# العملاء > جديد
# الاسم: عميل نقدي
# النوع: فردي
# المجموعة: فردي
```

## 📱 التصميم المتجاوب

### الشاشات الكبيرة (Desktop)
- عرض كامل للأقسام جنباً إلى جنب
- خطوط كبيرة للأرقام المهمة
- تأثيرات بصرية متقدمة

### الشاشات الصغيرة (Mobile/Tablet)
- ترتيب عمودي للأقسام
- خطوط مناسبة للمس
- تمرير أفقي للجداول

## 🎯 نصائح للاستخدام الأمثل

### 1. إعداد سريع
- استخدم مسح الباركود لإضافة الأصناف بسرعة
- اضبط ملف نقطة البيع مسبقاً
- حدد المخزن الافتراضي

### 2. تسريع المعاملات
- استخدم العميل الافتراضي للمبيعات النقدية السريعة
- فعّل تحديث المخزون التلقائي
- استخدم طرق الدفع المحددة مسبقاً

### 3. تجنب الأخطاء
- تحقق من الكميات المتاحة قبل البيع
- راجع المجموع قبل الدفع
- تأكد من صحة مبلغ الباقي

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. لا تظهر التخصيصات
```bash
# مسح الذاكرة المؤقتة
bench --site site1.local clear-cache

# إعادة بناء التطبيق
bench build --app interface_customization
```

#### 2. خطأ في العميل الافتراضي
- تأكد من وجود عميل بالاسم "عميل نقدي"
- أو أنشئه يدوياً في قائمة العملاء

#### 3. مشاكل في ملف نقطة البيع
- تحقق من تفعيل ملف نقطة البيع
- تأكد من إضافة طرق الدفع
- حدد المخزن الصحيح

## 📈 الفوائد المحققة

### للكاشير/موظف المبيعات
- ✅ واجهة أسرع وأبسط
- ✅ تقليل الأخطاء
- ✅ توفير الوقت في كل معاملة
- ✅ سهولة التدريب

### للإدارة
- ✅ تحسين كفاءة نقطة البيع
- ✅ تقليل وقت المعاملات
- ✅ دقة أكبر في البيانات
- ✅ تقارير فورية

### للعملاء
- ✅ خدمة أسرع
- ✅ فواتير واضحة
- ✅ تجربة أفضل

## 🔮 التطوير المستقبلي

### مميزات مقترحة
- [ ] واجهة تعمل باللمس للأجهزة اللوحية
- [ ] تكامل مع أجهزة الدفع الإلكتروني
- [ ] تقارير مبيعات فورية
- [ ] إدارة الخصومات المتقدمة
- [ ] دعم العروض والباقات

---

## 📞 الدعم

للحصول على المساعدة:
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: ملفات README في التطبيق

---

**ملاحظة**: هذه التخصيصات مصممة خصيصاً لتبسيط عملية البيع النقدي وتحسين تجربة المستخدم في نقاط البيع.
