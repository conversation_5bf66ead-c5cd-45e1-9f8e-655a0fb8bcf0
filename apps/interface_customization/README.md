# تطبيق تخصيص الواجهات - Interface Customization

## الوصف
تطبيق مخصص لتبسيط واجهات نظام ERPNext للمستخدمين العرب، مع التركيز على تحسين تجربة المستخدم لموظفي المبيعات.

## المميزات الرئيسية

### 🎯 تبسيط واجهة فاتورة المبيعات
- **إخفاء الحقول غير الضرورية**: إزالة الحقول المعقدة التي لا يحتاجها موظف المبيعات العادي
- **ترتيب الحقول حسب الأولوية**: وضع أهم الحقول في المقدمة (العميل، التاريخ، البنود، المجموع)
- **تقسيم الواجهة إلى أقسام واضحة**: تنظيم المعلومات في مجموعات منطقية
- **تسميات عربية واضحة**: استخدام مصطلحات مفهومة للمستخدمين العرب

### 📊 تحسين جدول البنود
- **عرض الحقول المهمة فقط**: رمز الصنف، الكمية، السعر، المبلغ
- **إضافة معلومات المخزون**: عرض الكمية المتاحة ومعدل التقييم
- **تحذيرات ذكية**: تنبيه عند طلب كمية أكبر من المتاح
- **مركز التكلفة**: إضافة حقل مركز التكلفة للبنود

### 🎨 تحسينات بصرية
- **تصميم عصري**: ألوان متدرجة وتأثيرات بصرية جذابة
- **أقسام ملونة**: كل قسم له لون مميز لسهولة التمييز
- **معلومات سريعة**: عرض ملخص الفاتورة في الأعلى
- **معلومات العميل**: عرض تفاصيل العميل بشكل واضح

### ⚡ وظائف ذكية
- **تحديث تلقائي للمخزون**: عرض الكميات المتاحة عند اختيار الأصناف
- **خصومات تلقائية**: تطبيق خصومات للعملاء المميزين والكميات الكبيرة
- **مركز التكلفة التلقائي**: تعيين مركز التكلفة الافتراضي
- **التحقق من البيانات**: فحص شامل للبيانات قبل الحفظ

## التثبيت

### 1. تحميل التطبيق
```bash
cd frappe-bench
bench get-app https://github.com/your-repo/interface_customization
```

### 2. تثبيت التطبيق على الموقع
```bash
bench --site your-site.local install-app interface_customization
```

### 3. بناء الأصول
```bash
bench build --app interface_customization
```

### 4. إعادة تشغيل النظام
```bash
bench restart
```

## الاستخدام

### فاتورة المبيعات المبسطة
1. انتقل إلى **المبيعات > فاتورة المبيعات**
2. انقر على **جديد**
3. ستلاحظ الواجهة المبسطة مع:
   - حقول أقل وأكثر وضوحاً
   - تسميات عربية
   - معلومات المخزون التلقائية
   - تحذيرات ذكية

### الحقول المعروضة
- **بيانات العميل**: العميل، اسم العميل، التاريخ، تاريخ الاستحقاق
- **بنود الفاتورة**: رمز الصنف، الكمية، السعر، المبلغ، الكمية المتاحة، مركز التكلفة
- **المجاميع**: المجموع، الضرائب، المجموع الإجمالي

### الحقول المخفية
- الحقول التقنية المعقدة
- معلومات الأسعار التفصيلية
- إعدادات المخزون المتقدمة
- الحقول المحاسبية المعقدة

## التخصيص

### إضافة حقول جديدة
يمكنك تعديل ملف `sales_invoice_custom.js` لإضافة أو إزالة حقول:

```javascript
const fields_to_hide = [
    'field_name_1',
    'field_name_2'
    // أضف الحقول التي تريد إخفاءها
];
```

### تغيير التسميات
عدّل قاموس `arabic_labels` في نفس الملف:

```javascript
const arabic_labels = {
    'field_name': 'التسمية العربية',
    // أضف المزيد من التسميات
};
```

### تخصيص الألوان
عدّل ملف `sales_invoice_custom.css` لتغيير الألوان والتصميم.

## الملفات الرئيسية

```
interface_customization/
├── hooks.py                           # إعدادات التطبيق
├── public/
│   ├── js/
│   │   └── sales_invoice_custom.js    # تخصيصات JavaScript
│   └── css/
│       └── sales_invoice_custom.css   # تنسيقات CSS
└── custom_interface/
    └── sales_invoice_customization.py # منطق Python
```

## المساهمة
نرحب بمساهماتكم لتحسين التطبيق:

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة
3. اكتب الكود مع التوثيق
4. أرسل Pull Request

## الدعم
للحصول على الدعم:
- أنشئ Issue في GitHub
- راسلنا على: <EMAIL>

## الترخيص
MIT License

---

**ملاحظة**: هذا التطبيق مصمم خصيصاً للمستخدمين العرب ويركز على تبسيط العمليات اليومية لموظفي المبيعات.