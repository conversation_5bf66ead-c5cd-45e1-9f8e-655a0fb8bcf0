# مرجع سريع للمطورين - Interface Customization

## 🚀 الأوامر الأساسية

### أوامر التطوير اليومية
```bash
# بناء التطبيق
bench build --app interface_customization

# مسح الذاكرة المؤقتة
bench --site site1.local clear-cache

# إعادة تشغيل النظام
bench restart

# مراقبة التغييرات (للتطوير)
bench watch
```

### أوامر استكشاف الأخطاء
```bash
# عرض سجلات الأخطاء
bench --site site1.local logs

# فحص حالة التطبيق
bench --site site1.local list-apps

# تشغيل وحدة تحكم Python
bench --site site1.local console

# تحديث قاعدة البيانات
bench --site site1.local migrate
```

---

## 📁 مرجع الملفات السريع

| الملف | الوظيفة | متى تعدله |
|-------|---------|-----------|
| `hooks.py` | إعدادات التطبيق الرئيسية | عند إضافة ملفات جديدة أو أحداث |
| `sales_invoice_custom.js` | تخصيص فاتورة المبيعات | لتعديل واجهة الفاتورة العادية |
| `pos_invoice_custom.js` | تخصيص الفاتورة النقدية | لتعديل واجهة الفاتورة النقدية |
| `sales_invoice_custom.css` | تنسيق الفاتورة العادية | لتغيير الألوان والتصميم |
| `pos_invoice_custom.css` | تنسيق الفاتورة النقدية | لتغيير ألوان النقدي |
| `sales_invoice_customization.py` | منطق الفاتورة العادية | لإضافة وظائف خلفية |
| `pos_invoice_customization.py` | منطق الفاتورة النقدية | لإضافة وظائف النقدي |
| `install.py` | إعدادات التثبيت | عند إضافة حقول أو بيانات افتراضية |

---

## 🎯 مهام شائعة وحلولها

### 1. إخفاء حقل جديد
**الملف**: `*_custom.js`
```javascript
const fields_to_hide = [
    'existing_field',
    'new_field_name'  // أضف هنا
];
```

### 2. إضافة تسمية عربية
**الملف**: `*_custom.js`
```javascript
const arabic_labels = {
    'field_name': 'التسمية العربية'
};
```

### 3. تغيير لون قسم
**الملف**: `*_custom.css`
```css
.section-name {
    background: linear-gradient(135deg, #color1, #color2);
}
```

### 4. إنشاء API جديد
**الملف**: `*_customization.py`
```python
@frappe.whitelist()
def my_api_function(param1, param2):
    """وصف الوظيفة"""
    return {"result": "data"}
```

### 5. استدعاء API من JavaScript
**الملف**: `*_custom.js`
```javascript
frappe.call({
    method: 'interface_customization.custom_interface.file_name.function_name',
    args: {'param': 'value'},
    callback: function(r) {
        console.log(r.message);
    }
});
```

---

## 🔧 أكواد مفيدة جاهزة

### JavaScript - التحكم في النموذج
```javascript
// إخفاء/إظهار حقل
frm.toggle_display('field_name', false);

// تعيين قيمة
frm.set_value('field_name', 'value');

// الحصول على قيمة
let value = frm.doc.field_name;

// إضافة زر مخصص
frm.add_custom_button('اسم الزر', function() {
    // الكود هنا
});

// تحديث حقل
frm.refresh_field('field_name');

// إضافة رسالة
frappe.msgprint('رسالة للمستخدم');

// إضافة تحذير
frappe.show_alert({
    message: 'رسالة التحذير',
    indicator: 'orange'
}, 5);
```

### Python - قاعدة البيانات
```python
# الحصول على مستند
doc = frappe.get_doc('Doctype', 'name')

# البحث في قاعدة البيانات
results = frappe.db.sql("""
    SELECT field1, field2 
    FROM `tabDoctype` 
    WHERE condition = %s
""", value, as_dict=True)

# الحصول على قيمة واحدة
value = frappe.db.get_value('Doctype', 'name', 'field')

# إنشاء مستند جديد
new_doc = frappe.get_doc({
    'doctype': 'Doctype',
    'field1': 'value1',
    'field2': 'value2'
})
new_doc.insert()

# تسجيل خطأ
frappe.log_error(f"Error message: {str(e)}")
```

### CSS - تنسيقات شائعة
```css
/* تدرج لوني */
.my-section {
    background: linear-gradient(135deg, #start-color, #end-color);
}

/* تأثير الظل */
.my-element {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* تأثير الحركة */
.my-element {
    transition: all 0.3s ease;
}

.my-element:hover {
    transform: translateY(-2px);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .my-element {
        font-size: 14px;
        padding: 10px;
    }
}
```

---

## 🎨 مرجع الألوان المستخدمة

### فاتورة المبيعات العادية
```css
/* قسم العميل */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* جدول البنود */
background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);

/* المجاميع */
background: linear-gradient(135deg, #28a745 0%, #20c997 100%);

/* معلومات العميل */
background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);

/* معلومات سريعة */
background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
```

### الفاتورة النقدية
```css
/* قسم العميل */
background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);

/* جدول البنود */
background: linear-gradient(135deg, #43a047 0%, #2e7d32 100%);

/* قسم الدفع */
background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);

/* المجموع الإجمالي */
background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);

/* مبلغ الباقي */
background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
```

---

## 📊 جدول الحقول المخفية

### فاتورة المبيعات العادية (40+ حقل)
| الحقل | السبب |
|-------|--------|
| `title` | غير ضروري للمستخدم العادي |
| `naming_series` | تعقيد غير مطلوب |
| `set_posting_time` | للمستخدمين المتقدمين |
| `conversion_rate` | تعقيد العملات |
| `price_list_currency` | تفاصيل تقنية |
| `ignore_pricing_rule` | للمطورين |
| `shipping_rule` | تعقيد الشحن |
| `packed_items` | تفاصيل التعبئة |
| `timesheets` | للمشاريع فقط |

### الفاتورة النقدية (50+ حقل)
| الحقل | السبب |
|-------|--------|
| `po_no` | غير مهم للنقدي |
| `shipping_address` | مبسط للنقدي |
| `payment_terms_template` | غير شائع في النقدي |
| `advances` | نادر في النقدي |
| `sales_team` | تعقيد غير مطلوب |
| `subscription_section` | غير متعلق بالنقدي |

---

## 🔍 دليل استكشاف الأخطاء السريع

| المشكلة | السبب المحتمل | الحل السريع |
|---------|---------------|-------------|
| التخصيصات لا تظهر | لم يتم البناء | `bench build --app interface_customization` |
| JavaScript لا يعمل | خطأ في الكود | فحص وحدة تحكم المتصفح (F12) |
| CSS لا يطبق | مسار خاطئ | فحص `hooks.py` |
| API لا يعمل | نسيان `@frappe.whitelist()` | إضافة decorator |
| خطأ في Python | خطأ في الصيغة | `bench --site site1.local logs` |

---

## 📋 قائمة مراجعة سريعة

### قبل كل تعديل:
- [ ] نسخة احتياطية من الملف
- [ ] تحديد الهدف من التعديل
- [ ] اختيار الملف الصحيح

### بعد كل تعديل:
- [ ] `bench build --app interface_customization`
- [ ] `bench --site site1.local clear-cache`
- [ ] اختبار التغيير
- [ ] فحص وحدة تحكم المتصفح

### قبل النشر:
- [ ] اختبار شامل
- [ ] نسخة احتياطية من قاعدة البيانات
- [ ] توثيق التغيير

---

## 🎯 نصائح الأداء

1. **استخدم `setTimeout`** للعمليات الثقيلة
2. **تجنب الاستدعاءات المتكررة** للـ APIs
3. **استخدم CSS** بدلاً من JavaScript للتنسيق
4. **اختبر على بيانات كبيرة**
5. **استخدم `console.log`** للتتبع أثناء التطوير

---

## 📞 مساعدة سريعة

### أخطاء شائعة:
- **"Module not found"**: تأكد من مسار الملف في `hooks.py`
- **"Permission denied"**: أضف `@frappe.whitelist()` للدالة
- **"Field not found"**: تأكد من اسم الحقل الصحيح
- **"CSS not applying"**: استخدم `!important` أو فحص التضارب

### للمساعدة الفورية:
- **البريد الإلكتروني**: <EMAIL>
- **وحدة تحكم المتصفح**: F12 → Console
- **سجلات النظام**: `bench --site site1.local logs`

---

هذا المرجع السريع يوفر إجابات فورية للمهام الشائعة في تطوير التطبيق.
