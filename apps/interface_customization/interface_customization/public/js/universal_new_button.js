/**
 * زر "جديد" العام - Universal New Button
 * يضيف زر "جديد" في جميع المستندات لإنشاء صفحة جديدة
 */

console.log('🔘 تحميل زر "جديد" العام...');

// قائمة المستندات المدعومة
const SUPPORTED_DOCTYPES = [
    'Sales Invoice',
    'Purchase Invoice', 
    'POS Invoice',
    'Payment Entry',
    'Journal Entry',
    'Stock Entry',
    'Delivery Note',
    'Purchase Receipt',
    'Sales Order',
    'Purchase Order',
    'Quotation',
    'Material Request',
    'Item',
    'Customer',
    'Supplier',
    'Employee'
];

/**
 * إضافة زر "جديد" للمستند - الكود الأصلي
 */
/*
function addUniversalNewButton(frm) {
    // التحقق من أن المستند مدعوم
    if (!SUPPORTED_DOCTYPES.includes(frm.doctype)) {
        return;
    }

    // إزالة الزر إذا كان موجوداً مسبقاً
    frm.remove_custom_button(__('جديد'), __('إجراءات'));

    // إضافة الزر الجديد
    frm.add_custom_button(__('جديد'), function() {
        openNewDocumentPage(frm);
    }, __('إجراءات'));

    // تغيير لون الزر ليكون مميزاً
    setTimeout(() => {
        const newButton = frm.page.btn_group.find('.btn-default:contains("جديد")');
        if (newButton.length > 0) {
            newButton.removeClass('btn-default')
                    .addClass('btn-success')
                    .css({
                        'background': '#28a745',
                        'border-color': '#28a745',
                        'color': 'white',
                        'font-weight': 'bold'
                    });
        }
    }, 100);

    console.log(`✅ تم إضافة زر "جديد" لـ ${frm.doctype}`);
}
*/

/**
 * إضافة زر "جديد" للمستند - الكود الجديد المحسن
 */
function addUniversalNewButton(frm) {
    // التحقق من أن المستند مدعوم
    if (!SUPPORTED_DOCTYPES.includes(frm.doctype)) {
        return;
    }

    // إظهار الزر في جميع النوافذ والمستندات
    console.log(`🔘 إضافة زر "جديد" لـ ${frm.doctype} في جميع النوافذ`);

    // إزالة الزر إذا كان موجوداً مسبقاً
    frm.remove_custom_button(__('جديد'), __('إجراءات'));

    // إضافة الزر الجديد
    frm.add_custom_button(__('جديد'), function() {
        openNewDocumentPage(frm);
    }, __('إجراءات'));

    // تغيير لون الزر ليكون مميزاً
    setTimeout(() => {
        const newButton = frm.page.btn_group.find('.btn-default:contains("جديد")');
        if (newButton.length > 0) {
            newButton.removeClass('btn-default')
                    .addClass('universal-new-btn')
                    .css({
                        'background': 'linear-gradient(135deg, #28a745, #20c997)',
                        'border': 'none',
                        'color': 'white',
                        'font-weight': '700',
                        'font-family': '"Cairo", sans-serif',
                        'border-radius': '6px',
                        'padding': '8px 16px',
                        'min-width': '100px',
                        'height': '36px',
                        'display': 'inline-flex',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'transition': 'all 0.3s ease',
                        'box-shadow': '0 2px 6px rgba(40, 167, 69, 0.3)'
                    });

            // إضافة أيقونة
            newButton.html('<i class="fa fa-plus"></i> جديد');

            // إضافة تأثير التمرير
            newButton.hover(
                function() {
                    $(this).css({
                        'background': 'linear-gradient(135deg, #218838, #1ea080)',
                        'transform': 'translateY(-1px)',
                        'box-shadow': '0 3px 8px rgba(40, 167, 69, 0.4)'
                    });
                },
                function() {
                    $(this).css({
                        'background': 'linear-gradient(135deg, #28a745, #20c997)',
                        'transform': 'translateY(0)',
                        'box-shadow': '0 2px 6px rgba(40, 167, 69, 0.3)'
                    });
                }
            );
        }
    }, 100);

    console.log(`✅ تم إضافة زر "جديد" المحسن للمستند ${frm.doctype}`);
}

/**
 * فتح صفحة مستند جديد
 */
function openNewDocumentPage(frm) {
    const doctype = frm.doctype;
    
    // إظهار رسالة تأكيد
    frappe.confirm(
        `هل تريد إنشاء ${getDocTypeDisplayName(doctype)} جديد؟`,
        function() {
            // إنشاء مستند جديد
            createNewDocument(doctype);
        },
        function() {
            // إلغاء
            frappe.show_alert({
                message: 'تم إلغاء العملية',
                indicator: 'orange'
            });
        }
    );
}

/**
 * إنشاء مستند جديد
 */
function createNewDocument(doctype) {
    frappe.show_alert({
        message: `جاري إنشاء ${getDocTypeDisplayName(doctype)} جديد...`,
        indicator: 'blue'
    });
    
    // إنشاء مستند جديد وفتحه في صفحة جديدة
    frappe.new_doc(doctype, {}, doc => {
        if (doc) {
            // frappe.show_alert({
            //     message: `تم إنشاء ${getDocTypeDisplayName(doctype)} جديد بنجاح`,
            //     indicator: 'green'
            // });
        }
    });
}

/**
 * الحصول على الاسم المعروض للمستند
 */
function getDocTypeDisplayName(doctype) {
    const displayNames = {
        'Sales Invoice': 'فاتورة مبيعات',
        'Purchase Invoice': 'فاتورة مشتريات',
        'POS Invoice': 'فاتورة نقاط البيع',
        'Payment Entry': 'قيد دفع',
        'Journal Entry': 'قيد يومية',
        'Stock Entry': 'قيد مخزون',
        'Delivery Note': 'إذن تسليم',
        'Purchase Receipt': 'إيصال استلام',
        'Sales Order': 'أمر مبيعات',
        'Purchase Order': 'أمر شراء',
        'Quotation': 'عرض سعر',
        'Material Request': 'طلب مواد',
        'Item': 'صنف',
        'Customer': 'عميل',
        'Supplier': 'مورد',
        'Employee': 'موظف'
    };
    
    return displayNames[doctype] || doctype;
}

/**
 * إضافة زر سريع في شريط الأدوات
 */
function addQuickNewButton() {
    // إضافة زر في شريط الأدوات العلوي
    if (cur_frm && cur_frm.page && cur_frm.page.main) {
        const quickButton = $(`
            <button class="btn btn-success btn-sm universal-new-btn" 
                    style="margin-left: 10px; font-weight: bold;">
                <i class="fa fa-plus"></i> جديد
            </button>
        `);
        
        // إضافة الزر إلى شريط الأدوات
        cur_frm.page.main.find('.form-toolbar').prepend(quickButton);
        
        // ربط الحدث
        quickButton.on('click', function() {
            if (cur_frm) {
                openNewDocumentPage(cur_frm);
            }
        });
    }
}

/**
 * إضافة اختصار لوحة المفاتيح
 */
function addKeyboardShortcut() {
    // اختصار Ctrl+N للمستند الجديد
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 'n' && cur_frm) {
            e.preventDefault();
            openNewDocumentPage(cur_frm);
        }
    });
    
    console.log('⌨️ تم إضافة اختصار Ctrl+N للمستند الجديد');
}

/**
 * إضافة قائمة سريعة للمستندات الشائعة
 */
function addQuickCreateMenu() {
    if (!cur_frm || !cur_frm.page) return;
    
    // إنشاء قائمة منسدلة
    const quickMenu = $(`
        <div class="dropdown" style="display: inline-block; margin-left: 10px;">
            <button class="btn btn-primary btn-sm dropdown-toggle" type="button" 
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fa fa-plus-circle"></i> إنشاء سريع
            </button>
            <div class="dropdown-menu">
                <a class="dropdown-item" href="#" data-doctype="Sales Invoice">
                    <i class="fa fa-file-text"></i> فاتورة مبيعات
                </a>
                <a class="dropdown-item" href="#" data-doctype="POS Invoice">
                    <i class="fa fa-shopping-cart"></i> فاتورة نقاط البيع
                </a>
                <a class="dropdown-item" href="#" data-doctype="Payment Entry">
                    <i class="fa fa-money"></i> قيد دفع
                </a>
                <a class="dropdown-item" href="#" data-doctype="Stock Entry">
                    <i class="fa fa-cubes"></i> قيد مخزون
                </a>
                <a class="dropdown-item" href="#" data-doctype="Customer">
                    <i class="fa fa-user"></i> عميل جديد
                </a>
                <a class="dropdown-item" href="#" data-doctype="Item">
                    <i class="fa fa-tag"></i> صنف جديد
                </a>
            </div>
        </div>
    `);
    
    // إضافة القائمة
    cur_frm.page.main.find('.form-toolbar').prepend(quickMenu);
    
    // ربط الأحداث
    quickMenu.find('.dropdown-item').on('click', function(e) {
        e.preventDefault();
        const doctype = $(this).data('doctype');
        if (doctype) {
            createNewDocument(doctype);
        }
    });
}

/**
 * تطبيق التحسينات على جميع النماذج
 */
frappe.ui.form.on('*', {
    refresh: function(frm) {
        // إضافة زر "جديد" العام
        addUniversalNewButton(frm);
        
        // إضافة الزر السريع
        setTimeout(() => {
            addQuickNewButton();
            addQuickCreateMenu();
        }, 500);
    },
    
    onload: function(frm) {
        // إضافة اختصار لوحة المفاتيح عند تحميل النموذج
        addKeyboardShortcut();
    }
});

/**
 * تطبيق عند تحميل الصفحة
 */
$(document).ready(function() {
    console.log('📄 تم تحميل الصفحة - تطبيق زر "جديد" العام...');
    
    // إضافة اختصار لوحة المفاتيح
    addKeyboardShortcut();
    
    // إضافة ستايل مخصص
    const customStyle = $(`
        <style>
            .universal-new-btn {
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            
            .universal-new-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            
            .dropdown-item {
                padding: 8px 16px;
                font-family: 'Cairo', sans-serif;
            }
            
            .dropdown-item:hover {
                background-color: #f8f9fa;
                color: #007bff;
            }
            
            .dropdown-item i {
                margin-left: 8px;
                width: 16px;
                text-align: center;
            }
        </style>
    `);
    
    $('head').append(customStyle);
});

// تصدير للاستخدام العام
window.UniversalNewButton = {
    add: addUniversalNewButton,
    create: createNewDocument,
    addQuickMenu: addQuickCreateMenu
};

console.log('✅ زر "جديد" العام جاهز!');
