// تخصيص واجهة الفاتورة النقدية للمستخدمين العرب
// POS Invoice Interface Customization for Arabic Users

frappe.ui.form.on('POS Invoice', {
    refresh: function(frm) {
        // التأكد من عدم إخفاء أزرار الحفظ والإرسال
        setTimeout(() => {
            // إظهار أزرار الحفظ والإرسال
            frm.page.show_menu();
            frm.page.show_actions_menu();

            // إخفاء الحقول غير الضرورية للفواتير النقدية
            hide_unnecessary_pos_fields(frm);

            // إعادة ترتيب الحقول حسب الأولوية للمبيعات النقدية
            reorganize_pos_fields(frm);

            // تخصيص التسميات للعربية
            customize_pos_labels(frm);

            // تحسين عرض جدول البنود للمبيعات النقدية
            customize_pos_items_table(frm);

            // تحسين قسم الدفع
            customize_payment_section(frm);

            // إضافة معلومات مفيدة للفواتير النقدية
            add_pos_helpful_info(frm);

            // تطبيق تصميم خاص للفواتير النقدية
            apply_pos_styling(frm);

            // إضافة زر معاينة القيود المحاسبية والمخزنية
            add_preview_entries_button(frm);
        }, 100);
    },

    onload: function(frm) {
        // تطبيق التخصيصات عند تحميل النموذج
        setup_pos_form_layout(frm);

        // تفعيل الوضع النقدي تلقائياً
        if (!frm.doc.is_pos) {
            frm.set_value('is_pos', 1);
        }
    },

    customer: function(frm) {
        // عرض معلومات العميل بوضوح للفواتير النقدية
        if (frm.doc.customer) {
            show_pos_customer_info(frm);
        }
    },

    grand_total: function(frm) {
        // تحديث معلومات الدفع عند تغيير المجموع
        update_payment_info(frm);
    }
});

// إخفاء الحقول غير الضرورية للفواتير النقدية
function hide_unnecessary_pos_fields(frm) {
    const fields_to_hide = [
        // حقول غير ضرورية للمبيعات النقدية
        'title', 'naming_series', 'tax_id', 'consolidated_invoice',
        'update_billed_amount_in_sales_order', 'update_billed_amount_in_delivery_note',
        'amended_from', 'return_against', 'set_posting_time', 'posting_time',

        // تفاصيل أوامر الشراء (غير مهمة للنقدي)
        'po_no', 'po_date',

        // العناوين المفصلة (مبسطة للنقدي)
        'shipping_address_name', 'shipping_address', 'company_address', 'company_address_display',
        'contact_mobile', 'contact_email',

        // تفاصيل العملة والأسعار (عادة ثابتة)
        'conversion_rate', 'price_list_currency', 'plc_conversion_rate',

        // قواعد التسعير (مبسطة للنقدي)
        'ignore_pricing_rule', 'pricing_rules',

        // التعبئة والتغليف
        'packed_items', 'product_bundle_help',

        // الجداول الزمنية
        'timesheets', 'total_billing_amount',

        // تفاصيل الضرائب المعقدة
        'shipping_rule', 'other_charges_calculation',

        // نقاط الولاء (يمكن تبسيطها)
        'loyalty_redemption_account', 'loyalty_redemption_cost_center',

        // الكوبونات (مبسطة)
        'coupon_code', 'apply_discount_on',

        // المقدمات (غير شائعة في النقدي)
        'allocate_advances_automatically', 'get_advances', 'advances',
        'payment_terms_template', 'payment_schedule',

        // الشطب (نادر في النقدي)
        'write_off_outstanding_amount_automatically', 'write_off_account', 'write_off_cost_center',

        // الطباعة المتقدمة
        'group_same_items', 'language', 'select_print_heading',

        // معلومات إضافية
        'inter_company_invoice_reference', 'is_discounted', 'source',
        'debit_to', 'party_account_currency', 'is_opening',

        // فريق المبيعات
        'sales_partner', 'amount_eligible_for_commission', 'commission_rate', 'total_commission',
        'sales_team',

        // الاشتراكات
        'from_date', 'to_date', 'auto_repeat', 'update_auto_repeat_reference',
        'against_income_account'
    ];

    fields_to_hide.forEach(field => {
        frm.toggle_display(field, false);
    });

    // إخفاء أقسام كاملة غير ضرورية
    frm.toggle_display('customer_po_details', false);
    frm.toggle_display('address_and_contact', false);
    frm.toggle_display('currency_and_price_list', false);
    frm.toggle_display('pricing_rule_details', false);
    frm.toggle_display('packing_list', false);
    frm.toggle_display('time_sheet_list', false);
    frm.toggle_display('sec_tax_breakup', false);
    frm.toggle_display('advances_section', false);
    frm.toggle_display('payment_schedule_section', false);
    frm.toggle_display('terms_section_break', false);
    frm.toggle_display('edit_printing_settings', false);
    frm.toggle_display('more_information', false);
    frm.toggle_display('sales_team_section_break', false);
    frm.toggle_display('subscription_section', false);
}

// إعادة ترتيب الحقول للفواتير النقدية
function reorganize_pos_fields(frm) {
    // الحقول المهمة للفواتير النقدية بالترتيب
    const important_fields = [
        'customer', 'customer_name', 'posting_date', 'pos_profile',
        'items', 'grand_total', 'payments', 'paid_amount', 'change_amount'
    ];

    // تطبيق ترتيب مخصص
    setTimeout(() => {
        reorder_pos_sections(frm);
    }, 500);
}

// تخصيص التسميات للفواتير النقدية
function customize_pos_labels(frm) {
    const arabic_pos_labels = {
        'customer': 'العميل',
        'customer_name': 'اسم العميل',
        'posting_date': 'تاريخ البيع',
        'pos_profile': 'ملف نقطة البيع',
        'company': 'الشركة',
        'cost_center': 'مركز التكلفة',
        'project': 'المشروع',
        'set_warehouse': 'المخزن',
        'update_stock': 'تحديث المخزون',
        'scan_barcode': 'مسح الباركود',
        'total_qty': 'إجمالي الكمية',
        'total': 'المجموع',
        'net_total': 'صافي المجموع',
        'taxes_and_charges': 'الضرائب والرسوم',
        'total_taxes_and_charges': 'إجمالي الضرائب',
        'additional_discount_percentage': 'نسبة الخصم الإضافي',
        'discount_amount': 'مبلغ الخصم',
        'grand_total': 'المجموع الإجمالي',
        'loyalty_points': 'نقاط الولاء',
        'loyalty_amount': 'مبلغ نقاط الولاء',
        'redeem_loyalty_points': 'استخدام نقاط الولاء',
        'loyalty_program': 'برنامج الولاء',
        'cash_bank_account': 'حساب النقدية/البنك',
        'paid_amount': 'المبلغ المدفوع',
        'change_amount': 'مبلغ الباقي',
        'account_for_change_amount': 'حساب مبلغ الباقي',
        'write_off_amount': 'مبلغ الشطب',
        'base_write_off_amount': 'مبلغ الشطب (عملة الشركة)',
        'outstanding_amount': 'المبلغ المستحق',
        'remarks': 'ملاحظات',
        'territory': 'المنطقة',
        'contact_person': 'الشخص المسؤول',
        'customer_address': 'عنوان العميل'
    };

    Object.keys(arabic_pos_labels).forEach(field => {
        if (frm.fields_dict[field]) {
            frm.fields_dict[field].df.label = arabic_pos_labels[field];
            frm.refresh_field(field);
        }
    });
}

// تخصيص جدول البنود للفواتير النقدية
function customize_pos_items_table(frm) {
    if (frm.fields_dict.items && frm.fields_dict.items.grid) {
        const grid = frm.fields_dict.items.grid;

        // إخفاء أعمدة غير ضرورية للمبيعات النقدية
        const columns_to_hide = [
            'barcode', 'has_item_scanned', 'customer_item_code', 'description',
            'item_group', 'brand', 'image', 'image_view',
            'stock_uom', 'conversion_factor', 'stock_qty',
            'price_list_rate', 'base_price_list_rate',
            'margin_type', 'margin_rate_or_amount', 'rate_with_margin', 'base_rate_with_margin',
            'discount_percentage', 'discount_amount', 'base_rate', 'base_amount',
            'item_tax_template', 'item_tax_rate', 'net_rate', 'net_amount',
            'base_net_rate', 'base_net_amount', 'pricing_rules',
            'is_free_item', 'grant_commission', 'delivered_by_supplier',
            'income_account', 'expense_account', 'enable_deferred_revenue',
            'deferred_revenue_account', 'service_start_date', 'service_end_date',
            'service_stop_date', 'delivered_qty', 'delivery_note', 'dn_detail',
            'sales_order', 'so_detail', 'pos_invoice_item',
            'serial_no', 'batch_no', 'actual_batch_qty', 'actual_qty',
            'warehouse', 'target_warehouse', 'quality_inspection',
            'allow_zero_valuation_rate', 'page_break', 'weight_per_unit',
            'total_weight', 'weight_uom', 'asset', 'is_fixed_asset',
            'use_serial_batch_fields', 'serial_and_batch_bundle'
        ];

        setTimeout(() => {
            columns_to_hide.forEach(col => {
                if (grid.docfields.find(df => df.fieldname === col)) {
                    grid.toggle_display(col, false);
                }
            });

            // إعادة ترتيب الأعمدة المهمة للفواتير النقدية
            reorder_pos_item_columns(grid);
        }, 1000);
    }
}

// إعادة ترتيب أعمدة البنود للفواتير النقدية
function reorder_pos_item_columns(grid) {
    // الأعمدة المهمة للفواتير النقدية
    const important_columns = [
        'item_code',
        'item_name',
        'qty',
        'uom',
        'rate',
        'amount',
        'cost_center'
    ];

    // تطبيق تسميات عربية للأعمدة
    const arabic_column_labels = {
        'item_code': 'رمز الصنف',
        'item_name': 'اسم الصنف',
        'qty': 'الكمية',
        'uom': 'الوحدة',
        'rate': 'السعر',
        'amount': 'المبلغ',
        'cost_center': 'مركز التكلفة'
    };

    // تطبيق التسميات العربية
    grid.docfields.forEach(df => {
        if (arabic_column_labels[df.fieldname]) {
            df.label = arabic_column_labels[df.fieldname];
        }
    });

    grid.refresh();
}

// تحسين قسم الدفع
function customize_payment_section(frm) {
    // إضافة معلومات واضحة عن الدفع
    if (frm.doc.grand_total) {
        add_payment_summary(frm);
    }

    // تحسين عرض جدول المدفوعات
    if (frm.fields_dict.payments && frm.fields_dict.payments.grid) {
        const payment_grid = frm.fields_dict.payments.grid;

        // تطبيق تسميات عربية لجدول المدفوعات
        const payment_labels = {
            'mode_of_payment': 'طريقة الدفع',
            'account': 'الحساب',
            'amount': 'المبلغ',
            'base_amount': 'المبلغ (عملة الشركة)'
        };

        payment_grid.docfields.forEach(df => {
            if (payment_labels[df.fieldname]) {
                df.label = payment_labels[df.fieldname];
            }
        });

        payment_grid.refresh();
    }
}

// إضافة ملخص الدفع
function add_payment_summary(frm) {
    const payment_summary = `
        <div class="pos-payment-summary" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border-radius: 10px; padding: 20px; margin: 15px 0;">
            <h5 style="color: white; text-align: center; margin-bottom: 15px;">ملخص الدفع</h5>
            <div class="row">
                <div class="col-md-4 text-center">
                    <strong>المجموع الإجمالي:</strong><br>
                    <span style="font-size: 18px;">${format_currency(frm.doc.grand_total || 0, frm.doc.currency)}</span>
                </div>
                <div class="col-md-4 text-center">
                    <strong>المبلغ المدفوع:</strong><br>
                    <span style="font-size: 18px;">${format_currency(frm.doc.paid_amount || 0, frm.doc.currency)}</span>
                </div>
                <div class="col-md-4 text-center">
                    <strong>مبلغ الباقي:</strong><br>
                    <span style="font-size: 18px;">${format_currency(frm.doc.change_amount || 0, frm.doc.currency)}</span>
                </div>
            </div>
        </div>
    `;

    if (!frm.payment_summary_added) {
        $(frm.fields_dict.payments.wrapper).before(payment_summary);
        frm.payment_summary_added = true;
    }
}

// تحديث معلومات الدفع
function update_payment_info(frm) {
    // إعادة إنشاء ملخص الدفع عند تغيير المجموع
    $('.pos-payment-summary').remove();
    frm.payment_summary_added = false;
    add_payment_summary(frm);
}

// إضافة معلومات مفيدة للفواتير النقدية
function add_pos_helpful_info(frm) {
    // إضافة مؤشرات الحالة
    if (!frm.doc.__islocal && frm.doc.docstatus === 1) {
        frm.dashboard.add_indicator(__('فاتورة نقدية مؤكدة'), 'green');
    } else if (frm.doc.__islocal) {
        frm.dashboard.add_indicator(__('فاتورة نقدية جديدة'), 'blue');
    } else {
        frm.dashboard.add_indicator(__('مسودة فاتورة نقدية'), 'orange');
    }

    // إضافة معلومات سريعة
    if (!frm.doc.__islocal) {
        add_pos_quick_info(frm);
    }
}

// عرض معلومات العميل للفواتير النقدية مع السهم القابل للنقر
function show_pos_customer_info(frm) {
    if (frm.doc.customer) {
        // إزالة المعلومات والأزرار السابقة أولاً
        $('.customer-info-collapsible').remove();
        $('.customer-info-toggle').remove();

        // إضافة السهم القابل للنقر للفواتير النقدية
        add_pos_customer_toggle_button(frm);

        frappe.call({
            method: 'interface_customization.custom_interface.sales_invoice_customization.get_customer_summary',
            args: {
                customer: frm.doc.customer
            },
            callback: function(r) {
                if (r.message) {
                    const customer = r.message;
                    let info_html = `
                        <div class="customer-info-collapsible" id="pos-customer-info-${frm.doc.name || 'new'}">
                            <h6>معلومات العميل</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم:</strong> ${customer.customer_name || ''}</p>
                                    <p><strong>المجموعة:</strong> ${customer.customer_group || ''}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>إجمالي المبيعات:</strong> ${format_currency(customer.total_sales || 0)}</p>
                                    <p><strong>عدد الفواتير:</strong> ${customer.invoice_count || 0}</p>
                                </div>
                            </div>
                        </div>
                    `;

                    // إضافة المعلومات الجديدة
                    $(frm.fields_dict.customer.wrapper).after(info_html);
                }
            }
        });
    } else {
        // إزالة المعلومات والأزرار عند عدم وجود عميل
        $('.customer-info-collapsible').remove();
        $('.customer-info-toggle').remove();
    }
}

// إضافة زر السهم القابل للنقر للفواتير النقدية
function add_pos_customer_toggle_button(frm) {
    // إزالة الزر السابق إن وجد
    $(frm.fields_dict.customer.wrapper).find('.customer-info-toggle').remove();

    // إضافة الزر الجديد
    const toggle_button = `
        <button class="customer-info-toggle" type="button" title="عرض/إخفاء معلومات العميل">
            <i class="fa fa-chevron-down"></i>
        </button>
    `;

    $(frm.fields_dict.customer.wrapper).append(toggle_button);

    // ربط حدث النقر
    $(frm.fields_dict.customer.wrapper).find('.customer-info-toggle').on('click', function() {
        toggle_pos_customer_info(frm, this);
    });
}

// تبديل عرض معلومات العميل للفواتير النقدية
function toggle_pos_customer_info(frm, button) {
    const info_box = $(frm.fields_dict.customer.wrapper).next('.customer-info-collapsible');
    const icon = $(button).find('i');

    if (info_box.hasClass('expanded')) {
        // إخفاء المعلومات
        info_box.removeClass('expanded');
        icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
        $(button).removeClass('rotated');
    } else {
        // عرض المعلومات
        info_box.addClass('expanded');
        icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
        $(button).addClass('rotated');
    }
}

// تطبيق تصميم خاص للفواتير النقدية
function apply_pos_styling(frm) {
    setTimeout(() => {
        add_pos_custom_css();
        improve_pos_layout(frm);
    }, 1000);
}

// إضافة CSS مخصص للفواتير النقدية
function add_pos_custom_css() {
    if (!$('#pos-invoice-custom-css').length) {
        $('head').append(`
            <style id="pos-invoice-custom-css">
                /* تصميم خاص للفواتير النقدية */
                .form-layout .form-page {
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                }

                /* قسم العميل للفواتير النقدية */
                .pos-customer-section {
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    color: white;
                    border-radius: 12px;
                    padding: 20px;
                    margin-bottom: 20px;
                }

                /* جدول البنود للفواتير النقدية */
                .pos-items-section {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    padding: 20px;
                    margin-bottom: 20px;
                }

                /* قسم الدفع */
                .pos-payment-section {
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    border-radius: 12px;
                    padding: 20px;
                    margin-bottom: 20px;
                }

                /* تحسين عرض المجموع الإجمالي */
                .form-control[data-fieldname="grand_total"] {
                    font-size: 24px;
                    font-weight: bold;
                    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
                    color: #212529;
                    text-align: center;
                    border: none;
                    border-radius: 10px;
                    padding: 15px;
                }

                /* تحسين عرض المبلغ المدفوع */
                .form-control[data-fieldname="paid_amount"] {
                    font-size: 20px;
                    font-weight: bold;
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    text-align: center;
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                }

                /* تحسين عرض مبلغ الباقي */
                .form-control[data-fieldname="change_amount"] {
                    font-size: 20px;
                    font-weight: bold;
                    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                    color: white;
                    text-align: center;
                    border: none;
                    border-radius: 8px;
                    padding: 12px;
                }

                /* تحسين أزرار الفواتير النقدية */
                .btn-primary {
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    border: none;
                    border-radius: 10px;
                    padding: 12px 24px;
                    font-weight: bold;
                    font-size: 16px;
                }

                /* تحسين جدول المدفوعات */
                .grid-wrapper .grid-header-row {
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                }

                /* تحسين عرض الباركود */
                .form-control[data-fieldname="scan_barcode"] {
                    font-size: 18px;
                    border: 2px solid #007bff;
                    border-radius: 8px;
                    padding: 12px;
                }
            </style>
        `);
    }
}

// تحسين تخطيط الفواتير النقدية
function improve_pos_layout(frm) {
    // إضافة عناوين واضحة للأقسام
    const pos_section_titles = {
        'customer_section': '🧑‍💼 بيانات العميل',
        'items_section': '🛒 بنود الفاتورة',
        'totals': '💰 المجاميع',
        'payments_section': '💳 الدفع'
    };

    Object.keys(pos_section_titles).forEach(section => {
        const section_wrapper = frm.fields_dict[section];
        if (section_wrapper && section_wrapper.wrapper) {
            const title_element = $(section_wrapper.wrapper).find('.section-head');
            if (title_element.length === 0) {
                $(section_wrapper.wrapper).prepend(`<div class="section-head" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 10px 15px; margin: -20px -20px 15px -20px; border-radius: 8px 8px 0 0; font-weight: bold; text-align: center;">${pos_section_titles[section]}</div>`);
            }
        }
    });
}

// إعداد تخطيط النموذج للفواتير النقدية
function setup_pos_form_layout(frm) {
    setTimeout(() => {
        apply_pos_styling(frm);
    }, 500);
}

// إعادة ترتيب الأقسام للفواتير النقدية
function reorder_pos_sections(frm) {
    // ترتيب مخصص للفواتير النقدية
    console.log('تم تطبيق ترتيب الأقسام للفواتير النقدية');
}

// إضافة قسم معلومات سريعة للفواتير النقدية
function add_pos_quick_info(frm) {
    const pos_quick_info = `
        <div class="pos-quick-info-section" style="background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%); color: white; border-radius: 10px; padding: 15px; margin: 10px 0;">
            <h6 style="color: white; text-align: center; margin-bottom: 10px;">معلومات سريعة - فاتورة نقدية</h6>
            <div class="row">
                <div class="col-md-3 text-center">
                    <strong>رقم الفاتورة:</strong><br>
                    ${frm.doc.name || ''}
                </div>
                <div class="col-md-3 text-center">
                    <strong>الحالة:</strong><br>
                    ${frm.doc.status || ''}
                </div>
                <div class="col-md-3 text-center">
                    <strong>المجموع:</strong><br>
                    ${format_currency(frm.doc.grand_total || 0, frm.doc.currency)}
                </div>
                <div class="col-md-3 text-center">
                    <strong>الباقي:</strong><br>
                    ${format_currency(frm.doc.change_amount || 0, frm.doc.currency)}
                </div>
            </div>
        </div>
    `;

    if (!frm.pos_quick_info_added) {
        $(frm.wrapper).find('.form-layout').prepend(pos_quick_info);
        frm.pos_quick_info_added = true;
    }
}

// تنسيق العملة
function format_currency(amount, currency) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency || 'SAR'
    }).format(amount);
}



// إضافة زر معاينة القيود المحاسبية والمخزنية
function add_preview_entries_button(frm) {
    // إضافة الزر في شريط الأدوات
    frm.add_custom_button(__('معاينة القيود'), function() {
        show_entries_preview(frm);
    }, __('المعاينة'));

    // تخصيص شكل الزر
    setTimeout(() => {
        $('.btn-custom:contains("معاينة القيود")').addClass('btn-preview-entries');
    }, 500);
}

// عرض معاينة القيود المحاسبية والمخزنية
function show_entries_preview(frm) {
    const dialog = new frappe.ui.Dialog({
        title: __('معاينة القيود المحاسبية والمخزنية'),
        size: 'extra-large',
        fields: [
            {
                fieldtype: 'HTML',
                fieldname: 'preview_content',
                options: '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> جاري تحميل المعاينة...</div>'
            }
        ],
        primary_action_label: __('إغلاق'),
        primary_action: function() {
            dialog.hide();
        }
    });

    dialog.show();

    // تحميل معاينة القيود
    load_entries_preview(frm, dialog);
}

// تحميل معاينة القيود
function load_entries_preview(frm, dialog) {
    // التحقق من وجود البيانات الأساسية
    if (!frm.doc.name || frm.doc.name.startsWith('new-')) {
        dialog.fields_dict.preview_content.$wrapper.html(
            '<div class="alert alert-info"><i class="fa fa-info-circle"></i> يرجى حفظ الفاتورة أولاً لمعاينة القيود</div>'
        );
        return;
    }

    frappe.call({
        method: 'interface_customization.custom_interface.pos_invoice_customization.get_entries_preview',
        args: {
            docname: frm.doc.name,
            doctype: frm.doc.doctype,
            is_submitted: frm.doc.docstatus === 1
        },
        callback: function(r) {
            if (r.message) {
                const preview_data = r.message;
                let preview_html = generate_preview_html(preview_data);
                dialog.fields_dict.preview_content.$wrapper.html(preview_html);
            } else {
                dialog.fields_dict.preview_content.$wrapper.html(
                    '<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> لا توجد قيود للمعاينة</div>'
                );
            }
        },
        error: function(r) {
            dialog.fields_dict.preview_content.$wrapper.html(
                '<div class="alert alert-danger"><i class="fa fa-times-circle"></i> حدث خطأ أثناء تحميل المعاينة</div>'
            );
        }
    });
}

// إنشاء HTML لمعاينة القيود
function generate_preview_html(preview_data) {
    let html = `
        <div class="entries-preview-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="preview-section accounting-entries">
                        <h4 class="preview-title">
                            <i class="fa fa-calculator"></i> القيود المحاسبية
                        </h4>
                        ${generate_accounting_entries_html(preview_data.accounting_entries)}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="preview-section stock-entries">
                        <h4 class="preview-title">
                            <i class="fa fa-cubes"></i> القيود المخزنية
                        </h4>
                        ${generate_stock_entries_html(preview_data.stock_entries)}
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="preview-summary">
                        <h5><i class="fa fa-info-circle"></i> ملخص الفاتورة</h5>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <strong>رقم الفاتورة:</strong> ${preview_data.invoice_number || ''}
                            </div>
                            <div class="summary-item">
                                <strong>التاريخ:</strong> ${preview_data.posting_date || ''}
                            </div>
                            <div class="summary-item">
                                <strong>العميل:</strong> ${preview_data.customer_name || ''}
                            </div>
                            <div class="summary-item">
                                <strong>المجموع الإجمالي:</strong> ${format_currency(preview_data.grand_total || 0)}
                            </div>
                            <div class="summary-item">
                                <strong>الحالة:</strong>
                                <span class="status-badge ${preview_data.status_class}">${preview_data.status || ''}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .entries-preview-container {
                padding: 20px;
            }

            .preview-section {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
                border: 1px solid #e9ecef;
            }

            .preview-title {
                color: #495057;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #007bff;
                font-weight: bold;
            }

            .preview-title i {
                margin-right: 10px;
                color: #007bff;
            }

            .entries-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }

            .entries-table th,
            .entries-table td {
                padding: 8px 12px;
                text-align: right;
                border: 1px solid #dee2e6;
                font-size: 13px;
            }

            .entries-table th {
                background: #e9ecef;
                font-weight: bold;
                color: #495057;
            }

            .entries-table tbody tr:nth-child(even) {
                background: #f8f9fa;
            }

            .entries-table tbody tr:hover {
                background: #e3f2fd;
            }

            .debit-amount {
                color: #28a745;
                font-weight: bold;
            }

            .credit-amount {
                color: #dc3545;
                font-weight: bold;
            }

            .stock-in {
                color: #28a745;
                font-weight: bold;
            }

            .stock-out {
                color: #dc3545;
                font-weight: bold;
            }

            .preview-summary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 8px;
            }

            .summary-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }

            .summary-item {
                background: rgba(255,255,255,0.1);
                padding: 10px;
                border-radius: 5px;
            }

            .status-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }

            .status-draft {
                background: #ffc107;
                color: #212529;
            }

            .status-submitted {
                background: #28a745;
                color: white;
            }

            .status-cancelled {
                background: #dc3545;
                color: white;
            }

            .no-entries {
                text-align: center;
                color: #6c757d;
                font-style: italic;
                padding: 20px;
            }
        </style>
    `;

    return html;
}

// إنشاء جدول القيود المحاسبية
function generate_accounting_entries_html(entries) {
    if (!entries || entries.length === 0) {
        return '<div class="no-entries">لا توجد قيود محاسبية</div>';
    }

    let html = `
        <table class="entries-table">
            <thead>
                <tr>
                    <th>الحساب</th>
                    <th>مدين</th>
                    <th>دائن</th>
                    <th>البيان</th>
                </tr>
            </thead>
            <tbody>
    `;

    let total_debit = 0;
    let total_credit = 0;

    entries.forEach(entry => {
        const debit = entry.debit || 0;
        const credit = entry.credit || 0;
        total_debit += debit;
        total_credit += credit;

        html += `
            <tr>
                <td>${entry.account || ''}</td>
                <td class="debit-amount">${debit > 0 ? format_currency(debit) : ''}</td>
                <td class="credit-amount">${credit > 0 ? format_currency(credit) : ''}</td>
                <td>${entry.remarks || entry.against || ''}</td>
            </tr>
        `;
    });

    // إضافة صف المجموع
    html += `
            <tr style="background: #e9ecef; font-weight: bold;">
                <td><strong>المجموع</strong></td>
                <td class="debit-amount"><strong>${format_currency(total_debit)}</strong></td>
                <td class="credit-amount"><strong>${format_currency(total_credit)}</strong></td>
                <td></td>
            </tr>
        </tbody>
    </table>
    `;

    return html;
}

// إنشاء جدول القيود المخزنية
function generate_stock_entries_html(entries) {
    if (!entries || entries.length === 0) {
        return '<div class="no-entries">لا توجد قيود مخزنية</div>';
    }

    let html = `
        <table class="entries-table">
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>المخزن</th>
                    <th>الكمية</th>
                    <th>المعدل</th>
                    <th>القيمة</th>
                </tr>
            </thead>
            <tbody>
    `;

    let total_value = 0;

    entries.forEach(entry => {
        const qty = entry.actual_qty || 0;
        const rate = entry.valuation_rate || 0;
        const value = Math.abs(qty * rate);
        total_value += value;

        const qty_class = qty > 0 ? 'stock-in' : 'stock-out';
        const qty_display = qty > 0 ? `+${qty}` : qty;

        html += `
            <tr>
                <td>${entry.item_code || ''}</td>
                <td>${entry.warehouse || ''}</td>
                <td class="${qty_class}">${qty_display}</td>
                <td>${format_currency(rate)}</td>
                <td>${format_currency(value)}</td>
            </tr>
        `;
    });

    // إضافة صف المجموع
    html += `
            <tr style="background: #e9ecef; font-weight: bold;">
                <td colspan="4"><strong>إجمالي القيمة</strong></td>
                <td><strong>${format_currency(total_value)}</strong></td>
            </tr>
        </tbody>
    </table>
    `;

    return html;
}
