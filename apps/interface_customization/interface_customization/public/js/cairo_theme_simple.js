/**
 * ثيم Cairo المبسط - JavaScript
 * Simple Cairo Theme - JavaScript
 */

console.log('🎨 تحميل ثيم Cairo المبسط...');

// تحسين تحميل الأيقونات لتجنب تحذيرات الـ preload
function preloadIcons() {
    // استخدام فوري للأيقونات المحملة مسبقاً
    const iconLinks = document.querySelectorAll('link[rel="preload"][as="image"]');
    iconLinks.forEach(link => {
        if (link.href.includes('icons.svg')) {
            // إنشاء عنصر مخفي لاستخدام الأيقونات فوراً
            const hiddenDiv = document.createElement('div');
            hiddenDiv.style.display = 'none';
            hiddenDiv.innerHTML = `<svg><use href="${link.href}#icon-backup"></use></svg>`;
            document.body.appendChild(hiddenDiv);

            // إزالة العنصر بعد ثانية واحدة
            setTimeout(() => {
                if (hiddenDiv.parentNode) {
                    hiddenDiv.parentNode.removeChild(hiddenDiv);
                }
            }, 1000);
        }
    });
}

// تطبيق الثيم فوراً
(function() {
    'use strict';

    // إضافة CSS مباشرة للتأكد من التطبيق
    const style = document.createElement('style');
    style.id = 'cairo-theme-emergency';
    style.innerHTML = `
        /* تطبيق طارئ للثيم */
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

        /* الألوان */
        :root {
            --blue-300: #93c5fd !important;
            --blue-400: #60a5fa !important;
            --blue-700: #1d4ed8 !important;
            --blue-50: #eff6ff !important;
        }

        /* الخط */
        body, button, h1, h2, h3, h4, h5, h6, .btn, .form-control,
        .sidebar-item-label, .control-label, .nav-link {
            font-family: "Cairo", sans-serif !important;
            font-weight: 700 !important;
        }

        /* الألوان الأساسية */
        .navbar {
            background: var(--blue-400) !important;
            height: 45px !important;
        }

        .btn-primary, button.btn-primary {
            background: var(--blue-700) !important;
            border-color: var(--blue-700) !important;
        }

        .list-row-head {
            background: var(--blue-300) !important;
        }

        .desk-sidebar-item {
            background: var(--blue-50) !important;
            border: 2px solid var(--blue-300) !important;
        }

        /* إشعار النجاح - تم إزالته */
        /* .cairo-theme-success {
            // تم إزالة CSS الخاص بإشعار النجاح
        } */
    `;

    // إضافة الستايل للصفحة
    if (document.head) {
        document.head.appendChild(style);
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            document.head.appendChild(style);
        });
    }

    // console.log('⚡ تم تطبيق الثيم الطارئ');
})();

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // console.log('📄 تم تحميل الصفحة - تطبيق التحسينات...');

    // تحسين تحميل الأيقونات أولاً
    preloadIcons();

    // تطبيق تحسينات إضافية
    applyEnhancements();
});

// دالة إشعار النجاح - تم إزالتها
// function showSuccessNotification() {
//     // تم إزالة هذه الدالة لعدم إظهار رسائل النجاح
// }

// تطبيق تحسينات إضافية
function applyEnhancements() {
    // إضافة class للجسم
    document.body.classList.add('cairo-theme-active');

    // تحسين الأزرار
    const buttons = document.querySelectorAll('.btn, button');
    buttons.forEach(function(btn) {
        btn.style.fontFamily = '"Cairo", sans-serif';
        btn.style.fontWeight = '700';
    });

    // تحسين النماذج
    const inputs = document.querySelectorAll('.form-control, input, textarea');
    inputs.forEach(function(input) {
        input.style.fontFamily = '"Cairo", sans-serif';
        input.style.borderColor = '#93c5fd';
    });

    // تحسين العناوين
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(function(heading) {
        heading.style.fontFamily = '"Cairo", sans-serif';
        heading.style.fontWeight = '700';
        heading.style.color = '#1e3a8a';
    });

    // إصلاح الروابط بدون إطارات
    fixLinksWithoutBorders();

    // console.log('🔧 تم تطبيق التحسينات الإضافية');
}

// إصلاح الروابط والعناصر بدون إطارات
function fixLinksWithoutBorders() {
    // البحث عن جميع الروابط والعناصر التفاعلية
    const selectors = [
        'a[href]:not(.btn):not(.navbar-brand)',
        '.widget-group-body a',
        '.links-widget-box a',
        '.shortcut-widget-box a',
        '.sidebar-item a',
        '.workspace-item a',
        '.module-item a',
        '.report-item a',
        '.master-item a',
        'span[data-route]',
        'div[data-route]',
        '.link-item',
        '.shortcut-widget',
        '.widget-body a',
        '.sidebar-section a',
        '.workspace-sidebar a'
    ];

    selectors.forEach(function(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(function(element) {
            // تطبيق الستايل فقط إذا لم يكن مطبق بالفعل
            if (!element.style.border || element.style.border === 'none') {
                element.style.border = '2px solid #93c5fd';
                element.style.background = '#eff6ff';
                element.style.padding = '4px 8px';
                element.style.margin = '1px';
                element.style.display = 'inline-block';
                element.style.color = '#1e40af';
                element.style.borderRadius = '5px';
                element.style.textDecoration = 'none';
                element.style.fontFamily = '"Cairo", sans-serif';
                element.style.fontWeight = '600';
                element.style.minWidth = '50%';
                element.style.textAlign = 'center';
                element.style.transition = 'all 0.2s ease';

                // إضافة تأثير التمرير
                element.addEventListener('mouseenter', function() {
                    this.style.border = '2px solid #3b82f6';
                    this.style.background = '#dbeafe';
                    this.style.color = '#1e3a8a';
                    this.style.transform = 'translateY(-1px) scale(1.02)';
                    this.style.boxShadow = '0 3px 8px rgba(29, 78, 216, 0.2)';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.border = '2px solid #93c5fd';
                    this.style.background = '#eff6ff';
                    this.style.color = '#1e40af';
                    this.style.transform = 'none';
                    this.style.boxShadow = 'none';
                });
            }
        });
    });

    // console.log('🎨 تم إصلاح الروابط بدون إطارات');
}

// مراقبة التغييرات في الصفحة
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.addedNodes.length > 0) {
            // تطبيق التحسينات على العناصر الجديدة
            setTimeout(function() {
                applyEnhancements();
                fixLinksWithoutBorders();
            }, 100);
        }
    });
});

// بدء المراقبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// تصدير للاستخدام العام
window.CairoTheme = {
    apply: applyEnhancements
};

// console.log('🎨 ثيم Cairo المبسط جاهز!');

// تطبيق فوري إذا كانت الصفحة محملة بالفعل
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyEnhancements);
} else {
    applyEnhancements();
}
