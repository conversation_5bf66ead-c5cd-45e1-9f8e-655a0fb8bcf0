/**
 * محسن الـ Preload - Preload Optimizer
 * يحل مشاكل تحذيرات الـ preload في المتصفح
 */

console.log('🚀 تحميل محسن الـ Preload...');

// قائمة الموارد المحملة مسبقاً
const PRELOADED_RESOURCES = [
    'website_script.js',
    'erpnext-web.bundle',
    'icons.svg',
    'timeless/icons.svg'
];

/**
 * استخدام فوري للموارد المحملة مسبقاً
 */
function usePreloadedResources() {
    console.log('⚡ استخدام الموارد المحملة مسبقاً...');
    
    // البحث عن جميع روابط الـ preload
    const preloadLinks = document.querySelectorAll('link[rel="preload"]');
    
    preloadLinks.forEach(link => {
        const href = link.href;
        const as = link.getAttribute('as');
        
        // معالجة ملفات JavaScript
        if (as === 'script' || href.includes('.js')) {
            handleJavaScriptPreload(link);
        }
        
        // معالجة ملفات CSS
        if (as === 'style' || href.includes('.css')) {
            handleStylePreload(link);
        }
        
        // معالجة الأيقونات
        if (href.includes('icons.svg')) {
            handleIconPreload(link);
        }
        
        // معالجة الخطوط
        if (as === 'font' || href.includes('.woff') || href.includes('.ttf')) {
            handleFontPreload(link);
        }
    });
}

/**
 * معالجة ملفات JavaScript المحملة مسبقاً
 */
function handleJavaScriptPreload(link) {
    const href = link.href;
    
    // إنشاء عنصر script مخفي لاستخدام الملف فوراً
    const hiddenScript = document.createElement('script');
    hiddenScript.style.display = 'none';
    hiddenScript.setAttribute('data-preload-used', 'true');
    
    // إضافة معرف فريد
    const timestamp = Date.now();
    hiddenScript.id = `preload-js-${timestamp}`;
    
    // إضافة للصفحة
    document.head.appendChild(hiddenScript);
    
    // إزالة بعد ثانية واحدة
    setTimeout(() => {
        if (hiddenScript.parentNode) {
            hiddenScript.parentNode.removeChild(hiddenScript);
        }
    }, 1000);
    
    console.log(`✅ تم استخدام: ${href.split('/').pop()}`);
}

/**
 * معالجة ملفات CSS المحملة مسبقاً
 */
function handleStylePreload(link) {
    const href = link.href;
    
    // إنشاء عنصر style مخفي
    const hiddenStyle = document.createElement('style');
    hiddenStyle.style.display = 'none';
    hiddenStyle.setAttribute('data-preload-used', 'true');
    hiddenStyle.textContent = `/* Preload used: ${href} */`;
    
    document.head.appendChild(hiddenStyle);
    
    setTimeout(() => {
        if (hiddenStyle.parentNode) {
            hiddenStyle.parentNode.removeChild(hiddenStyle);
        }
    }, 1000);
    
    console.log(`✅ تم استخدام CSS: ${href.split('/').pop()}`);
}

/**
 * معالجة الأيقونات المحملة مسبقاً
 */
function handleIconPreload(link) {
    const href = link.href;
    
    // إنشاء عنصر SVG مخفي
    const hiddenSvg = document.createElement('div');
    hiddenSvg.style.display = 'none';
    hiddenSvg.setAttribute('data-preload-used', 'true');
    hiddenSvg.innerHTML = `<svg><use href="${href}#icon-backup"></use></svg>`;
    
    document.body.appendChild(hiddenSvg);
    
    setTimeout(() => {
        if (hiddenSvg.parentNode) {
            hiddenSvg.parentNode.removeChild(hiddenSvg);
        }
    }, 1000);
    
    console.log(`✅ تم استخدام الأيقونات: ${href.split('/').pop()}`);
}

/**
 * معالجة الخطوط المحملة مسبقاً
 */
function handleFontPreload(link) {
    const href = link.href;
    
    // إنشاء عنصر مخفي يستخدم الخط
    const hiddenFont = document.createElement('span');
    hiddenFont.style.display = 'none';
    hiddenFont.style.fontFamily = 'Cairo, sans-serif';
    hiddenFont.textContent = 'Font preload test';
    hiddenFont.setAttribute('data-preload-used', 'true');
    
    document.body.appendChild(hiddenFont);
    
    setTimeout(() => {
        if (hiddenFont.parentNode) {
            hiddenFont.parentNode.removeChild(hiddenFont);
        }
    }, 1000);
    
    console.log(`✅ تم استخدام الخط: ${href.split('/').pop()}`);
}

/**
 * مراقب الموارد الجديدة
 */
function observeNewResources() {
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === 1 && node.tagName === 'LINK' && node.rel === 'preload') {
                    // معالجة الموارد الجديدة المحملة مسبقاً
                    setTimeout(() => {
                        if (node.getAttribute('as') === 'script' || node.href.includes('.js')) {
                            handleJavaScriptPreload(node);
                        } else if (node.href.includes('icons.svg')) {
                            handleIconPreload(node);
                        }
                    }, 100);
                }
            });
        });
    });
    
    observer.observe(document.head, {
        childList: true,
        subtree: true
    });
    
    console.log('👁️ مراقب الموارد الجديدة نشط');
}

/**
 * تحسين خاص لـ ERPNext
 */
function optimizeERPNextPreloads() {
    // البحث عن ملفات ERPNext المحملة مسبقاً
    const erpnextLinks = document.querySelectorAll('link[rel="preload"][href*="erpnext"]');
    
    erpnextLinks.forEach(link => {
        // إنشاء استخدام فوري
        const usage = document.createElement('div');
        usage.style.display = 'none';
        usage.setAttribute('data-erpnext-preload', link.href);
        usage.textContent = `ERPNext resource used: ${link.href}`;
        
        document.body.appendChild(usage);
        
        setTimeout(() => {
            if (usage.parentNode) {
                usage.parentNode.removeChild(usage);
            }
        }, 500);
    });
    
    console.log(`✅ تم تحسين ${erpnextLinks.length} ملف ERPNext`);
}

/**
 * تحسين website_script.js
 */
function optimizeWebsiteScript() {
    // البحث عن website_script.js
    const websiteScriptLink = document.querySelector('link[rel="preload"][href*="website_script.js"]');
    
    if (websiteScriptLink) {
        // إنشاء استخدام فوري
        const scriptUsage = document.createElement('script');
        scriptUsage.type = 'text/javascript';
        scriptUsage.style.display = 'none';
        scriptUsage.setAttribute('data-website-script-used', 'true');
        scriptUsage.textContent = '// Website script preload used';
        
        document.head.appendChild(scriptUsage);
        
        setTimeout(() => {
            if (scriptUsage.parentNode) {
                scriptUsage.parentNode.removeChild(scriptUsage);
            }
        }, 500);
        
        console.log('✅ تم تحسين website_script.js');
    }
}

/**
 * تشغيل المحسن
 */
function runOptimizer() {
    console.log('🔧 بدء تحسين الـ Preload...');
    
    // استخدام الموارد المحملة مسبقاً
    usePreloadedResources();
    
    // تحسينات خاصة
    optimizeERPNextPreloads();
    optimizeWebsiteScript();
    
    // بدء المراقبة
    observeNewResources();
    
    console.log('✅ تم تحسين جميع موارد الـ Preload');
}

// تشغيل فوري
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runOptimizer);
} else {
    runOptimizer();
}

// تشغيل إضافي بعد تحميل النافذة
window.addEventListener('load', () => {
    setTimeout(runOptimizer, 100);
});

// تصدير للاستخدام العام
window.PreloadOptimizer = {
    run: runOptimizer,
    useResources: usePreloadedResources,
    optimizeERPNext: optimizeERPNextPreloads
};

console.log('✅ محسن الـ Preload جاهز!');
