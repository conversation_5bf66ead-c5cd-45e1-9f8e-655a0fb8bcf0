# -*- coding: utf-8 -*-
# Copyright (c) 2024, Interface Customization and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe.model.document import Document
from frappe.utils import now_datetime, add_days, get_datetime, cstr
import os
import subprocess
import shutil
from datetime import datetime, timedelta


class DownloadBackup(Document):
    def validate(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if self.enable_auto_backup and not self.daily_backup_time:
            frappe.throw("يجب تحديد التوقيت اليومي عند تفعيل النسخ الاحتياطي التلقائي")
        
        if self.backup_count and self.backup_count < 1:
            frappe.throw("عدد النسخ المحفوظة يجب أن يكون أكبر من صفر")

    def on_update(self):
        """تنفيذ عند تحديث المستند"""
        if self.enable_auto_backup:
            self.schedule_backup()
        else:
            self.cancel_scheduled_backup()
        
        # تحديث موعد النسخة التالية
        self.update_next_backup_date()

    def schedule_backup(self):
        """جدولة النسخ الاحتياطي التلقائي"""
        try:
            # إلغاء الجدولة السابقة إن وجدت
            self.cancel_scheduled_backup()
            
            # إنشاء جدولة جديدة
            from frappe.utils.scheduler import enqueue_job
            
            # حساب الوقت التالي للنسخ الاحتياطي
            next_run = self.get_next_backup_datetime()
            
            # جدولة المهمة
            frappe.get_doc({
                "doctype": "Scheduled Job Type",
                "method": "interface_customization.custom_interface.doctype.download_backup.download_backup.create_backup",
                "frequency": "Daily",
                "cron_format": self.get_cron_format()
            }).insert(ignore_permissions=True)
            
            frappe.msgprint("تم جدولة النسخ الاحتياطي التلقائي بنجاح")
            
        except Exception as e:
            frappe.log_error(f"خطأ في جدولة النسخ الاحتياطي: {str(e)}")
            frappe.throw(f"فشل في جدولة النسخ الاحتياطي: {str(e)}")

    def cancel_scheduled_backup(self):
        """إلغاء جدولة النسخ الاحتياطي"""
        try:
            # البحث عن المهام المجدولة وحذفها
            scheduled_jobs = frappe.get_all("Scheduled Job Type", 
                filters={"method": "interface_customization.custom_interface.doctype.download_backup.download_backup.create_backup"})
            
            for job in scheduled_jobs:
                frappe.delete_doc("Scheduled Job Type", job.name, ignore_permissions=True)
                
        except Exception as e:
            frappe.log_error(f"خطأ في إلغاء جدولة النسخ الاحتياطي: {str(e)}")

    def get_cron_format(self):
        """تحويل الوقت إلى صيغة cron"""
        if not self.daily_backup_time:
            return "0 2 * * *"  # الافتراضي: 2:00 صباحاً
        
        time_parts = str(self.daily_backup_time).split(":")
        hour = int(time_parts[0])
        minute = int(time_parts[1])
        
        return f"{minute} {hour} * * *"

    def get_next_backup_datetime(self):
        """حساب موعد النسخة الاحتياطية التالية"""
        if not self.daily_backup_time:
            return None
        
        now = now_datetime()
        today = now.date()
        
        # تحويل الوقت إلى datetime
        backup_time = get_datetime(f"{today} {self.daily_backup_time}")
        
        # إذا كان الوقت قد مضى اليوم، جدول للغد
        if backup_time <= now:
            backup_time = backup_time + timedelta(days=1)
        
        return backup_time

    def update_next_backup_date(self):
        """تحديث موعد النسخة الاحتياطية التالية"""
        if self.enable_auto_backup:
            next_backup = self.get_next_backup_datetime()
            if next_backup:
                frappe.db.set_value("Download Backup", self.name, "next_backup_date", next_backup)

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوياً"""
        return create_backup()


@frappe.whitelist()
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        # الحصول على إعدادات النسخ الاحتياطي
        backup_settings = frappe.get_single("Download Backup")
        
        if not backup_settings:
            frappe.throw("لم يتم العثور على إعدادات النسخ الاحتياطي")
        
        # تحديد نوع النسخة الاحتياطية
        with_files = backup_settings.backup_format == "SQL + Files" or backup_settings.include_files
        
        # إنشاء النسخة الاحتياطية باستخدام أوامر Frappe
        site_name = frappe.local.site
        
        # تنفيذ أمر النسخ الاحتياطي
        backup_command = f"bench --site {site_name} backup"
        if with_files:
            backup_command += " --with-files"
        
        # تنفيذ الأمر
        result = subprocess.run(backup_command.split(), capture_output=True, text=True)
        
        if result.returncode == 0:
            # تحديث حالة النسخ الاحتياطي
            backup_settings.last_backup_date = now_datetime()
            backup_settings.last_backup_status = "نجح"
            
            # نسخ الملف إلى المسار المحدد إذا كان موجوداً
            if backup_settings.download_path:
                copy_backup_to_download_path(backup_settings)
            
            # تنظيف النسخ القديمة
            cleanup_old_backups(backup_settings)
            
            backup_settings.save(ignore_permissions=True)
            
            frappe.msgprint("تم إنشاء النسخة الاحتياطية بنجاح")
            return {"status": "success", "message": "تم إنشاء النسخة الاحتياطية بنجاح"}
            
        else:
            error_msg = result.stderr or "خطأ غير معروف"
            backup_settings.last_backup_status = f"فشل: {error_msg}"
            backup_settings.save(ignore_permissions=True)
            
            frappe.log_error(f"فشل في إنشاء النسخة الاحتياطية: {error_msg}")
            return {"status": "error", "message": f"فشل في إنشاء النسخة الاحتياطية: {error_msg}"}
            
    except Exception as e:
        frappe.log_error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return {"status": "error", "message": f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"}


def copy_backup_to_download_path(backup_settings):
    """نسخ النسخة الاحتياطية إلى المسار المحدد"""
    try:
        # الحصول على مسار النسخ الاحتياطية الافتراضي
        site_name = frappe.local.site
        backup_dir = f"sites/{site_name}/private/backups"
        
        # البحث عن أحدث نسخة احتياطية
        if os.path.exists(backup_dir):
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.sql.gz') or f.endswith('.tar')]
            if backup_files:
                latest_backup = max(backup_files, key=lambda x: os.path.getctime(os.path.join(backup_dir, x)))
                
                # نسخ الملف إلى المسار المحدد
                source_path = os.path.join(backup_dir, latest_backup)
                
                # إذا كان المسار المحدد مجلد
                if backup_settings.download_path and os.path.isdir(backup_settings.download_path):
                    dest_path = os.path.join(backup_settings.download_path, latest_backup)
                    shutil.copy2(source_path, dest_path)
                    
                    # تحديث حجم النسخة الاحتياطية
                    file_size = os.path.getsize(dest_path)
                    backup_settings.backup_size = format_file_size(file_size)
                    
    except Exception as e:
        frappe.log_error(f"خطأ في نسخ النسخة الاحتياطية: {str(e)}")


def cleanup_old_backups(backup_settings):
    """تنظيف النسخ الاحتياطية القديمة"""
    try:
        if not backup_settings.backup_count:
            return
        
        # تنظيف النسخ في المسار الافتراضي
        site_name = frappe.local.site
        backup_dir = f"sites/{site_name}/private/backups"
        cleanup_directory(backup_dir, backup_settings.backup_count)
        
        # تنظيف النسخ في مسار التنزيل
        if backup_settings.download_path and os.path.isdir(backup_settings.download_path):
            cleanup_directory(backup_settings.download_path, backup_settings.backup_count)
            
    except Exception as e:
        frappe.log_error(f"خطأ في تنظيف النسخ القديمة: {str(e)}")


def cleanup_directory(directory, keep_count):
    """تنظيف مجلد من النسخ القديمة"""
    try:
        if not os.path.exists(directory):
            return
        
        # الحصول على قائمة ملفات النسخ الاحتياطية
        backup_files = [f for f in os.listdir(directory) 
                       if f.endswith('.sql.gz') or f.endswith('.tar') or f.endswith('.sql')]
        
        # ترتيب الملفات حسب تاريخ الإنشاء (الأحدث أولاً)
        backup_files.sort(key=lambda x: os.path.getctime(os.path.join(directory, x)), reverse=True)
        
        # حذف الملفات الزائدة
        files_to_delete = backup_files[keep_count:]
        for file_to_delete in files_to_delete:
            file_path = os.path.join(directory, file_to_delete)
            os.remove(file_path)
            
    except Exception as e:
        frappe.log_error(f"خطأ في تنظيف المجلد {directory}: {str(e)}")


def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"


@frappe.whitelist()
def test_backup_settings():
    """اختبار إعدادات النسخ الاحتياطي"""
    try:
        backup_settings = frappe.get_single("Download Backup")

        tests = []

        # اختبار الوقت المحدد
        if backup_settings.daily_backup_time:
            tests.append({"test": "التوقيت اليومي", "status": "✓", "message": f"محدد: {backup_settings.daily_backup_time}"})
        else:
            tests.append({"test": "التوقيت اليومي", "status": "✗", "message": "غير محدد"})

        # اختبار مسار التنزيل
        if backup_settings.download_path:
            if os.path.exists(backup_settings.download_path):
                tests.append({"test": "مسار التنزيل", "status": "✓", "message": "المسار موجود"})
            else:
                tests.append({"test": "مسار التنزيل", "status": "✗", "message": "المسار غير موجود"})
        else:
            tests.append({"test": "مسار التنزيل", "status": "⚠", "message": "غير محدد - سيتم الحفظ في المسار الافتراضي"})

        # اختبار عدد النسخ
        if backup_settings.backup_count and backup_settings.backup_count > 0:
            tests.append({"test": "عدد النسخ", "status": "✓", "message": f"محدد: {backup_settings.backup_count}"})
        else:
            tests.append({"test": "عدد النسخ", "status": "✗", "message": "غير محدد أو صفر"})

        return {"status": "success", "tests": tests}

    except Exception as e:
        return {"status": "error", "message": str(e)}


def check_scheduled_backups():
    """فحص المهام المجدولة للنسخ الاحتياطي (يتم تشغيلها كل دقيقة)"""
    try:
        backup_settings = frappe.get_single("Download Backup")

        if not backup_settings.enable_auto_backup or not backup_settings.daily_backup_time:
            return

        now = now_datetime()
        current_time = now.time()
        backup_time = backup_settings.daily_backup_time

        # تحويل الوقت إلى دقائق للمقارنة
        current_minutes = current_time.hour * 60 + current_time.minute
        backup_minutes = backup_time.hour * 60 + backup_time.minute

        # إذا كان الوقت الحالي يطابق وقت النسخ الاحتياطي (بدقة دقيقة واحدة)
        if abs(current_minutes - backup_minutes) <= 1:
            # التحقق من عدم تنفيذ نسخة احتياطية اليوم
            last_backup = backup_settings.last_backup_date
            if last_backup:
                last_backup_date = get_datetime(last_backup).date()
                today = now.date()

                if last_backup_date >= today:
                    return  # تم إنشاء نسخة احتياطية اليوم بالفعل

            # تنفيذ النسخ الاحتياطي
            frappe.enqueue(
                create_backup,
                queue='default',
                timeout=1800,  # 30 دقيقة
                job_name=f"auto_backup_{now.strftime('%Y%m%d_%H%M')}"
            )

    except Exception as e:
        frappe.log_error(f"خطأ في فحص المهام المجدولة: {str(e)}")


def daily_backup_check():
    """فحص يومي لحالة النسخ الاحتياطي"""
    try:
        backup_settings = frappe.get_single("Download Backup")

        if not backup_settings.enable_auto_backup:
            return

        # التحقق من آخر نسخة احتياطية
        if backup_settings.last_backup_date:
            last_backup = get_datetime(backup_settings.last_backup_date)
            now = now_datetime()
            days_since_backup = (now - last_backup).days

            # إرسال تنبيه إذا لم يتم إنشاء نسخة احتياطية لأكثر من يومين
            if days_since_backup > 2:
                send_backup_alert(f"لم يتم إنشاء نسخة احتياطية منذ {days_since_backup} أيام")
        else:
            send_backup_alert("لم يتم إنشاء أي نسخة احتياطية بعد")

        # تنظيف النسخ القديمة
        if backup_settings.backup_count:
            cleanup_old_backups(backup_settings)

    except Exception as e:
        frappe.log_error(f"خطأ في الفحص اليومي للنسخ الاحتياطي: {str(e)}")


def send_backup_alert(message):
    """إرسال تنبيه حول حالة النسخ الاحتياطي"""
    try:
        # إرسال إشعار للمدراء
        users = frappe.get_all("User",
            filters={"role_profile_name": ["in", ["System Manager", "Administrator"]]},
            fields=["name", "email"])

        for user in users:
            frappe.get_doc({
                "doctype": "Notification Log",
                "subject": "تنبيه النسخ الاحتياطي",
                "email_content": message,
                "for_user": user.name,
                "type": "Alert"
            }).insert(ignore_permissions=True)

    except Exception as e:
        frappe.log_error(f"خطأ في إرسال تنبيه النسخ الاحتياطي: {str(e)}")


@frappe.whitelist()
def get_backup_history():
    """الحصول على تاريخ النسخ الاحتياطية"""
    try:
        site_name = frappe.local.site
        backup_dir = f"sites/{site_name}/private/backups"

        if not os.path.exists(backup_dir):
            return {"status": "error", "message": "مجلد النسخ الاحتياطية غير موجود"}

        backup_files = []
        for file in os.listdir(backup_dir):
            if file.endswith('.sql.gz') or file.endswith('.tar') or file.endswith('.sql'):
                file_path = os.path.join(backup_dir, file)
                file_stat = os.stat(file_path)

                backup_files.append({
                    "name": file,
                    "size": format_file_size(file_stat.st_size),
                    "created": datetime.fromtimestamp(file_stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                    "modified": datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })

        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backup_files.sort(key=lambda x: x['created'], reverse=True)

        return {"status": "success", "files": backup_files}

    except Exception as e:
        return {"status": "error", "message": str(e)}


@frappe.whitelist()
def delete_backup_file(filename):
    """حذف نسخة احتياطية محددة"""
    try:
        site_name = frappe.local.site
        backup_dir = f"sites/{site_name}/private/backups"
        file_path = os.path.join(backup_dir, filename)

        # التحقق من وجود الملف
        if not os.path.exists(file_path):
            return {"status": "error", "message": "الملف غير موجود"}

        # التحقق من أن الملف هو نسخة احتياطية صحيحة
        if not (filename.endswith('.sql.gz') or filename.endswith('.tar') or filename.endswith('.sql')):
            return {"status": "error", "message": "نوع الملف غير صحيح"}

        # حذف الملف
        os.remove(file_path)

        return {"status": "success", "message": f"تم حذف النسخة الاحتياطية {filename} بنجاح"}

    except Exception as e:
        frappe.log_error(f"خطأ في حذف النسخة الاحتياطية: {str(e)}")
        return {"status": "error", "message": str(e)}
