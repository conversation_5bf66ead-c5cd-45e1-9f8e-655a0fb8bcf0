// Copyright (c) 2024, Interface Customization and contributors
// For license information, please see license.txt

frappe.ui.form.on('Download Backup', {
    refresh: function(frm) {
        // إضافة أزرار مخصصة
        add_custom_buttons(frm);
        
        // تحديث حالة النسخ الاحتياطي
        update_backup_status(frm);
        
        // إعداد التحديث التلقائي للحالة
        setup_auto_refresh(frm);
    },
    
    enable_auto_backup: function(frm) {
        // إظهار/إخفاء الحقول المرتبطة
        frm.toggle_reqd('daily_backup_time', frm.doc.enable_auto_backup);
        
        if (frm.doc.enable_auto_backup) {
            frappe.msgprint({
                title: 'تنبيه',
                message: 'تأكد من تحديد التوقيت اليومي للنسخ الاحتياطي',
                indicator: 'orange'
            });
        }
    },
    
    daily_backup_time: function(frm) {
        if (frm.doc.daily_backup_time && frm.doc.enable_auto_backup) {
            // حساب موعد النسخة التالية
            calculate_next_backup(frm);
        }
    },
    
    backup_count: function(frm) {
        if (frm.doc.backup_count && frm.doc.backup_count < 1) {
            frappe.msgprint({
                title: 'خطأ',
                message: 'عدد النسخ المحفوظة يجب أن يكون أكبر من صفر',
                indicator: 'red'
            });
            frm.set_value('backup_count', 7);
        }
    }
});

function add_custom_buttons(frm) {
    // زر إنشاء نسخة احتياطية فورية
    frm.add_custom_button(__('إنشاء نسخة احتياطية الآن'), function() {
        create_immediate_backup(frm);
    }, __('النسخ الاحتياطي'));
    
    // زر اختبار الإعدادات
    frm.add_custom_button(__('اختبار الإعدادات'), function() {
        test_backup_settings(frm);
    }, __('النسخ الاحتياطي'));
    
    // زر عرض النسخ الاحتياطية
    frm.add_custom_button(__('عرض النسخ الاحتياطية'), function() {
        show_backup_files(frm);
    }, __('النسخ الاحتياطي'));
    
    // زر تنظيف النسخ القديمة
    frm.add_custom_button(__('تنظيف النسخ القديمة'), function() {
        cleanup_old_backups(frm);
    }, __('النسخ الاحتياطي'));
    
    // تغيير لون مجموعة الأزرار
    frm.page.set_inner_btn_group_as_primary(__('النسخ الاحتياطي'));
}

function create_immediate_backup(frm) {
    frappe.confirm(
        'هل تريد إنشاء نسخة احتياطية الآن؟',
        function() {
            frappe.show_alert({
                message: 'جاري إنشاء النسخة الاحتياطية...',
                indicator: 'blue'
            });
            
            frappe.call({
                method: 'interface_customization.custom_interface.doctype.download_backup.download_backup.create_backup',
                callback: function(r) {
                    if (r.message && r.message.status === 'success') {
                        frappe.show_alert({
                            message: r.message.message,
                            indicator: 'green'
                        });
                        
                        // تحديث النموذج
                        frm.reload_doc();
                    } else {
                        frappe.show_alert({
                            message: r.message ? r.message.message : 'حدث خطأ أثناء إنشاء النسخة الاحتياطية',
                            indicator: 'red'
                        });
                    }
                },
                error: function(r) {
                    frappe.show_alert({
                        message: 'فشل في إنشاء النسخة الاحتياطية',
                        indicator: 'red'
                    });
                }
            });
        }
    );
}

function test_backup_settings(frm) {
    frappe.call({
        method: 'interface_customization.custom_interface.doctype.download_backup.download_backup.test_backup_settings',
        callback: function(r) {
            if (r.message && r.message.status === 'success') {
                show_test_results(r.message.tests);
            } else {
                frappe.msgprint({
                    title: 'خطأ',
                    message: r.message ? r.message.message : 'فشل في اختبار الإعدادات',
                    indicator: 'red'
                });
            }
        }
    });
}

function show_test_results(tests) {
    let html = '<div class="test-results">';
    html += '<h4>نتائج اختبار الإعدادات:</h4>';
    html += '<table class="table table-bordered">';
    html += '<thead><tr><th>الاختبار</th><th>الحالة</th><th>الملاحظات</th></tr></thead>';
    html += '<tbody>';
    
    tests.forEach(function(test) {
        let status_class = '';
        if (test.status === '✓') status_class = 'text-success';
        else if (test.status === '✗') status_class = 'text-danger';
        else if (test.status === '⚠') status_class = 'text-warning';
        
        html += `<tr>
            <td>${test.test}</td>
            <td class="${status_class}">${test.status}</td>
            <td>${test.message}</td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    
    frappe.msgprint({
        title: 'نتائج الاختبار',
        message: html,
        wide: true
    });
}

function show_backup_files(frm) {
    // عرض النسخ الاحتياطية المتاحة
    frappe.call({
        method: 'interface_customization.custom_interface.doctype.download_backup.download_backup.get_backup_history',
        callback: function(r) {
            if (r.message && r.message.status === 'success') {
                show_backup_list(r.message.files);
            } else {
                frappe.msgprint({
                    title: 'خطأ',
                    message: r.message ? r.message.message : 'لم يتم العثور على نسخ احتياطية',
                    indicator: 'red'
                });
            }
        }
    });
}

function show_backup_list(files) {
    let html = '<div class="backup-files">';
    html += '<h4>النسخ الاحتياطية المتاحة:</h4>';

    if (files.length === 0) {
        html += '<p>لا توجد نسخ احتياطية متاحة</p>';
    } else {
        html += '<table class="table table-striped">';
        html += '<thead><tr><th>اسم الملف</th><th>تاريخ الإنشاء</th><th>الحجم</th><th>الإجراءات</th></tr></thead>';
        html += '<tbody>';

        files.forEach(function(file) {
            html += `<tr>
                <td>${file.name}</td>
                <td>${file.created}</td>
                <td>${file.size}</td>
                <td>
                    <button class="btn btn-xs btn-primary" onclick="download_backup_file('${file.name}')">
                        تنزيل
                    </button>
                    <button class="btn btn-xs btn-danger" onclick="delete_backup_file('${file.name}')">
                        حذف
                    </button>
                </td>
            </tr>`;
        });

        html += '</tbody></table>';
    }

    html += '</div>';

    frappe.msgprint({
        title: 'النسخ الاحتياطية',
        message: html,
        wide: true
    });
}

function download_backup_file(filename) {
    // تنزيل النسخة الاحتياطية
    let site_name = frappe.boot.sitename;
    let download_url = `/private/backups/${filename}`;
    window.open(download_url, '_blank');
}

function delete_backup_file(filename) {
    frappe.confirm(
        `هل تريد حذف النسخة الاحتياطية "${filename}"؟`,
        function() {
            frappe.call({
                method: 'interface_customization.custom_interface.doctype.download_backup.download_backup.delete_backup_file',
                args: {
                    filename: filename
                },
                callback: function(r) {
                    if (r.message && r.message.status === 'success') {
                        frappe.show_alert({
                            message: 'تم حذف النسخة الاحتياطية بنجاح',
                            indicator: 'green'
                        });
                        // إعادة تحميل قائمة النسخ الاحتياطية
                        show_backup_files();
                    } else {
                        frappe.show_alert({
                            message: r.message ? r.message.message : 'فشل في حذف النسخة الاحتياطية',
                            indicator: 'red'
                        });
                    }
                }
            });
        }
    );
}

function cleanup_old_backups(frm) {
    if (!frm.doc.backup_count) {
        frappe.msgprint('يجب تحديد عدد النسخ المحفوظة أولاً');
        return;
    }
    
    frappe.confirm(
        `هل تريد حذف النسخ الاحتياطية القديمة والاحتفاظ بآخر ${frm.doc.backup_count} نسخ فقط؟`,
        function() {
            frappe.call({
                method: 'interface_customization.custom_interface.doctype.download_backup.download_backup.cleanup_old_backups',
                args: {
                    backup_settings: frm.doc
                },
                callback: function(r) {
                    frappe.show_alert({
                        message: 'تم تنظيف النسخ القديمة بنجاح',
                        indicator: 'green'
                    });
                }
            });
        }
    );
}

function update_backup_status(frm) {
    // تحديث معلومات الحالة
    if (frm.doc.last_backup_date) {
        let last_backup = moment(frm.doc.last_backup_date);
        let now = moment();
        let diff = now.diff(last_backup, 'hours');
        
        if (diff > 24) {
            frm.dashboard.add_indicator(__('آخر نسخة احتياطية: منذ أكثر من 24 ساعة'), 'orange');
        } else {
            frm.dashboard.add_indicator(__('آخر نسخة احتياطية: منذ {0} ساعة', [diff]), 'green');
        }
    } else {
        frm.dashboard.add_indicator(__('لم يتم إنشاء نسخ احتياطية بعد'), 'red');
    }
    
    // إضافة مؤشر حالة التفعيل
    if (frm.doc.enable_auto_backup) {
        frm.dashboard.add_indicator(__('النسخ التلقائي: مفعل'), 'blue');
    } else {
        frm.dashboard.add_indicator(__('النسخ التلقائي: معطل'), 'grey');
    }
}

function calculate_next_backup(frm) {
    if (frm.doc.daily_backup_time) {
        let now = moment();
        let backup_time = moment(frm.doc.daily_backup_time, 'HH:mm:ss');
        
        // إذا كان الوقت قد مضى اليوم، احسب للغد
        if (backup_time.isBefore(now)) {
            backup_time.add(1, 'day');
        }
        
        frm.set_value('next_backup_date', backup_time.format('YYYY-MM-DD HH:mm:ss'));
    }
}

function setup_auto_refresh(frm) {
    // تحديث الحالة كل 30 ثانية
    if (frm.auto_refresh_interval) {
        clearInterval(frm.auto_refresh_interval);
    }
    
    frm.auto_refresh_interval = setInterval(function() {
        if (frm.doc && frm.doc.enable_auto_backup) {
            // تحديث موعد النسخة التالية
            calculate_next_backup(frm);
        }
    }, 30000);
}

// تنظيف عند إغلاق النموذج
frappe.ui.form.on('Download Backup', {
    onload: function(frm) {
        // إعداد التحديث التلقائي عند تحميل النموذج
        setup_auto_refresh(frm);
    },
    
    before_unload: function(frm) {
        // تنظيف المؤقت عند إغلاق النموذج
        if (frm.auto_refresh_interval) {
            clearInterval(frm.auto_refresh_interval);
        }
    }
});
