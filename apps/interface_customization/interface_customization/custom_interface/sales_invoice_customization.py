# -*- coding: utf-8 -*-
"""
تخصيصات فاتورة المبيعات
Sales Invoice Customizations
"""

import frappe
from frappe import _
from frappe.utils import flt, cstr, nowdate

def before_save(doc, method):
    """
    معالجة البيانات قبل حفظ فاتورة المبيعات
    Process data before saving Sales Invoice
    """
    # التحقق من البيانات الأساسية
    validate_basic_data(doc)
    
    # تحديث معلومات المخزون
    update_stock_info(doc)
    
    # تطبيق قواعد العمل المخصصة
    apply_custom_business_rules(doc)

def validate_basic_data(doc):
    """
    التحقق من البيانات الأساسية
    Validate basic data
    """
    # التحقق من وجود العميل
    if not doc.customer:
        frappe.throw(_("يجب تحديد العميل"))
    
    # التحقق من وجود بنود في الفاتورة
    if not doc.items:
        frappe.throw(_("يجب إضافة بنود للفاتورة"))
    
    # التحقق من الكميات
    for item in doc.items:
        if flt(item.qty) <= 0:
            frappe.throw(_("الكمية يجب أن تكون أكبر من صفر للصنف: {0}").format(item.item_code))
        
        if flt(item.rate) < 0:
            frappe.throw(_("السعر لا يمكن أن يكون سالباً للصنف: {0}").format(item.item_code))

def update_stock_info(doc):
    """
    تحديث معلومات المخزون للبنود
    Update stock information for items
    """
    for item in doc.items:
        if item.item_code:
            # الحصول على الكمية المتاحة في المخزون
            actual_qty = get_actual_qty(item.item_code, item.warehouse)
            item.actual_qty = actual_qty
            
            # الحصول على معدل التقييم
            valuation_rate = get_valuation_rate(item.item_code, item.warehouse)
            item.valuation_rate = valuation_rate
            
            # التحقق من توفر الكمية المطلوبة
            if doc.update_stock and flt(item.qty) > flt(actual_qty):
                frappe.msgprint(
                    _("تحذير: الكمية المطلوبة ({0}) أكبر من الكمية المتاحة ({1}) للصنف {2}")
                    .format(item.qty, actual_qty, item.item_code),
                    alert=True
                )

def apply_custom_business_rules(doc):
    """
    تطبيق قواعد العمل المخصصة
    Apply custom business rules
    """
    # تطبيق خصم تلقائي للعملاء المميزين
    apply_vip_customer_discount(doc)
    
    # تحديد مركز التكلفة التلقائي
    set_default_cost_center(doc)
    
    # تطبيق قواعد التسعير المخصصة
    apply_custom_pricing_rules(doc)

def apply_vip_customer_discount(doc):
    """
    تطبيق خصم للعملاء المميزين
    Apply discount for VIP customers
    """
    if doc.customer:
        customer_doc = frappe.get_doc("Customer", doc.customer)
        
        # التحقق من كون العميل مميز
        if hasattr(customer_doc, 'customer_group') and customer_doc.customer_group == "VIP":
            # تطبيق خصم 5% للعملاء المميزين
            if not doc.additional_discount_percentage:
                doc.additional_discount_percentage = 5
                frappe.msgprint(_("تم تطبيق خصم 5% للعميل المميز"), alert=True)

def set_default_cost_center(doc):
    """
    تحديد مركز التكلفة التلقائي
    Set default cost center
    """
    if not doc.cost_center and doc.company:
        # الحصول على مركز التكلفة الافتراضي للشركة
        default_cost_center = frappe.db.get_value("Company", doc.company, "cost_center")
        if default_cost_center:
            doc.cost_center = default_cost_center
            
            # تطبيق مركز التكلفة على جميع البنود
            for item in doc.items:
                if not item.cost_center:
                    item.cost_center = default_cost_center

def apply_custom_pricing_rules(doc):
    """
    تطبيق قواعد التسعير المخصصة
    Apply custom pricing rules
    """
    for item in doc.items:
        # تطبيق خصم كمية للطلبات الكبيرة
        apply_quantity_discount(item)
        
        # تطبيق أسعار خاصة للأصناف المحددة
        apply_special_item_pricing(item, doc.customer)

def apply_quantity_discount(item):
    """
    تطبيق خصم الكمية
    Apply quantity discount
    """
    if flt(item.qty) >= 100:
        # خصم 10% للكميات أكبر من أو تساوي 100
        discount_rate = 10
    elif flt(item.qty) >= 50:
        # خصم 5% للكميات أكبر من أو تساوي 50
        discount_rate = 5
    else:
        discount_rate = 0
    
    if discount_rate > 0 and not item.discount_percentage:
        item.discount_percentage = discount_rate

def apply_special_item_pricing(item, customer):
    """
    تطبيق أسعار خاصة للأصناف
    Apply special item pricing
    """
    # يمكن إضافة منطق مخصص للأسعار الخاصة هنا
    pass

def get_actual_qty(item_code, warehouse=None):
    """
    الحصول على الكمية الفعلية في المخزون
    Get actual quantity in stock
    """
    try:
        from erpnext.stock.utils import get_latest_stock_qty
        return get_latest_stock_qty(item_code, warehouse)
    except:
        return 0

def get_valuation_rate(item_code, warehouse=None):
    """
    الحصول على معدل التقييم
    Get valuation rate
    """
    try:
        from erpnext.stock.utils import get_valuation_rate
        return get_valuation_rate(item_code, warehouse)
    except:
        return 0

@frappe.whitelist()
def get_item_stock_info(item_code, warehouse=None):
    """
    API للحصول على معلومات المخزون للصنف
    API to get stock information for item
    """
    if not item_code:
        return {}
    
    try:
        actual_qty = get_actual_qty(item_code, warehouse)
        valuation_rate = get_valuation_rate(item_code, warehouse)
        
        # الحصول على معلومات إضافية عن الصنف
        item_doc = frappe.get_doc("Item", item_code)
        
        return {
            "actual_qty": actual_qty,
            "valuation_rate": valuation_rate,
            "stock_uom": item_doc.stock_uom,
            "item_name": item_doc.item_name,
            "item_group": item_doc.item_group,
            "has_batch_no": item_doc.has_batch_no,
            "has_serial_no": item_doc.has_serial_no
        }
    except Exception as e:
        frappe.log_error(f"Error getting stock info for {item_code}: {str(e)}")
        return {}

@frappe.whitelist()
def get_customer_summary(customer):
    """
    الحصول على ملخص العميل
    Get customer summary
    """
    if not customer:
        return {}
    
    try:
        customer_doc = frappe.get_doc("Customer", customer)
        
        # حساب إجمالي المبيعات للعميل
        total_sales = frappe.db.sql("""
            SELECT SUM(grand_total) as total
            FROM `tabSales Invoice`
            WHERE customer = %s AND docstatus = 1
        """, customer)[0][0] or 0
        
        # عدد الفواتير
        invoice_count = frappe.db.count("Sales Invoice", {
            "customer": customer,
            "docstatus": 1
        })
        
        # آخر فاتورة
        last_invoice = frappe.db.get_value("Sales Invoice", {
            "customer": customer,
            "docstatus": 1
        }, "posting_date", order_by="posting_date desc")
        
        return {
            "customer_name": customer_doc.customer_name,
            "customer_group": customer_doc.customer_group,
            "territory": customer_doc.territory,
            "total_sales": total_sales,
            "invoice_count": invoice_count,
            "last_invoice_date": last_invoice,
            "credit_limit": customer_doc.credit_limit,
            "payment_terms": customer_doc.payment_terms
        }
    except Exception as e:
        frappe.log_error(f"Error getting customer summary for {customer}: {str(e)}")
        return {}

def validate_sales_invoice_permissions(doc, method):
    """
    التحقق من صلاحيات فاتورة المبيعات
    Validate sales invoice permissions
    """
    # يمكن إضافة منطق التحقق من الصلاحيات هنا
    pass

def on_submit_sales_invoice(doc, method):
    """
    معالجة ما بعد تأكيد الفاتورة
    Process after invoice submission
    """
    # إرسال إشعارات
    send_invoice_notifications(doc)
    
    # تحديث إحصائيات العميل
    update_customer_stats(doc)

def send_invoice_notifications(doc):
    """
    إرسال إشعارات الفاتورة
    Send invoice notifications
    """
    # يمكن إضافة منطق الإشعارات هنا
    pass

def update_customer_stats(doc):
    """
    تحديث إحصائيات العميل
    Update customer statistics
    """
    # يمكن إضافة منطق تحديث الإحصائيات هنا
    pass
