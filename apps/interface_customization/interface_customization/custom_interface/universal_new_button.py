# -*- coding: utf-8 -*-
# Copyright (c) 2024, Interface Customization and contributors
# For license information, please see license.txt

from __future__ import unicode_literals
import frappe
from frappe import _
from frappe.utils import nowdate, now_datetime

# قائمة المستندات المدعومة
SUPPORTED_DOCTYPES = [
    'Sales Invoice',
    'Purchase Invoice', 
    'POS Invoice',
    'Payment Entry',
    'Journal Entry',
    'Stock Entry',
    'Delivery Note',
    'Purchase Receipt',
    'Sales Order',
    'Purchase Order',
    'Quotation',
    'Material Request',
    'Item',
    'Customer',
    'Supplier',
    'Employee'
]

@frappe.whitelist()
def create_new_document(doctype, template_data=None):
    """
    إنشاء مستند جديد مع بيانات افتراضية
    """
    try:
        # التحقق من صحة نوع المستند
        if doctype not in SUPPORTED_DOCTYPES:
            frappe.throw(_("نوع المستند غير مدعوم"))
        
        # التحقق من الصلاحيات
        if not frappe.has_permission(doctype, "create"):
            frappe.throw(_("ليس لديك صلاحية لإنشاء هذا المستند"))
        
        # إنشاء مستند جديد
        doc = frappe.new_doc(doctype)
        
        # إضافة البيانات الافتراضية
        default_data = get_default_data(doctype)
        if default_data:
            for field, value in default_data.items():
                if hasattr(doc, field):
                    setattr(doc, field, value)
        
        # إضافة بيانات القالب إذا كانت متوفرة
        if template_data:
            for field, value in template_data.items():
                if hasattr(doc, field):
                    setattr(doc, field, value)
        
        # حفظ المستند كمسودة
        doc.insert(ignore_permissions=False)
        
        return {
            "status": "success",
            "message": _("تم إنشاء المستند بنجاح"),
            "doc_name": doc.name,
            "doctype": doctype
        }
        
    except Exception as e:
        frappe.log_error(f"خطأ في إنشاء مستند جديد: {str(e)}")
        return {
            "status": "error",
            "message": _("حدث خطأ أثناء إنشاء المستند: {0}").format(str(e))
        }

def get_default_data(doctype):
    """
    الحصول على البيانات الافتراضية لكل نوع مستند
    """
    defaults = {}
    
    # البيانات العامة
    defaults.update({
        'posting_date': nowdate(),
        'posting_time': now_datetime().time(),
        'company': frappe.defaults.get_user_default("Company")
    })
    
    # بيانات خاصة بكل مستند
    if doctype in ['Sales Invoice', 'POS Invoice', 'Sales Order', 'Quotation']:
        defaults.update({
            'customer': get_default_customer(),
            'selling_price_list': frappe.db.get_single_value('Selling Settings', 'selling_price_list'),
            'currency': frappe.defaults.get_user_default("Currency") or "YER"
        })
    
    elif doctype in ['Purchase Invoice', 'Purchase Order', 'Purchase Receipt']:
        defaults.update({
            'supplier': get_default_supplier(),
            'buying_price_list': frappe.db.get_single_value('Buying Settings', 'buying_price_list'),
            'currency': frappe.defaults.get_user_default("Currency") or "YER"
        })
    
    elif doctype == 'Payment Entry':
        defaults.update({
            'payment_type': 'Receive',
            'party_type': 'Customer',
            'mode_of_payment': get_default_mode_of_payment()
        })
    
    elif doctype == 'Stock Entry':
        defaults.update({
            'stock_entry_type': 'Material Issue',
            'from_warehouse': get_default_warehouse(),
            'to_warehouse': get_default_warehouse()
        })
    
    elif doctype == 'Item':
        defaults.update({
            'item_group': 'كل مجموعات الأصناف',
            'stock_uom': 'Nos',
            'is_stock_item': 1,
            'include_item_in_manufacturing': 0
        })
    
    return defaults

def get_default_customer():
    """الحصول على العميل الافتراضي"""
    try:
        # البحث عن عميل افتراضي
        customer = frappe.db.get_value('Customer', {'is_default': 1}, 'name')
        if not customer:
            # أخذ أول عميل متاح
            customer = frappe.db.get_value('Customer', {}, 'name')
        return customer
    except:
        return None

def get_default_supplier():
    """الحصول على المورد الافتراضي"""
    try:
        # البحث عن مورد افتراضي
        supplier = frappe.db.get_value('Supplier', {'is_default': 1}, 'name')
        if not supplier:
            # أخذ أول مورد متاح
            supplier = frappe.db.get_value('Supplier', {}, 'name')
        return supplier
    except:
        return None

def get_default_warehouse():
    """الحصول على المخزن الافتراضي"""
    try:
        # البحث عن مخزن افتراضي
        warehouse = frappe.db.get_value('Warehouse', {'is_default': 1}, 'name')
        if not warehouse:
            # أخذ أول مخزن متاح
            warehouse = frappe.db.get_value('Warehouse', {}, 'name')
        return warehouse
    except:
        return None

def get_default_mode_of_payment():
    """الحصول على طريقة الدفع الافتراضية"""
    try:
        # البحث عن طريقة دفع افتراضية
        mode = frappe.db.get_value('Mode of Payment', {'is_default': 1}, 'name')
        if not mode:
            # أخذ أول طريقة دفع متاحة
            mode = frappe.db.get_value('Mode of Payment', {}, 'name')
        return mode
    except:
        return "نقد"

@frappe.whitelist()
def get_quick_create_options():
    """
    الحصول على خيارات الإنشاء السريع
    """
    options = []
    
    for doctype in SUPPORTED_DOCTYPES:
        if frappe.has_permission(doctype, "create"):
            options.append({
                'doctype': doctype,
                'label': get_doctype_label(doctype),
                'icon': get_doctype_icon(doctype)
            })
    
    return options

def get_doctype_label(doctype):
    """الحصول على التسمية العربية للمستند"""
    labels = {
        'Sales Invoice': 'فاتورة مبيعات',
        'Purchase Invoice': 'فاتورة مشتريات',
        'POS Invoice': 'فاتورة نقاط البيع',
        'Payment Entry': 'قيد دفع',
        'Journal Entry': 'قيد يومية',
        'Stock Entry': 'قيد مخزون',
        'Delivery Note': 'إذن تسليم',
        'Purchase Receipt': 'إيصال استلام',
        'Sales Order': 'أمر مبيعات',
        'Purchase Order': 'أمر شراء',
        'Quotation': 'عرض سعر',
        'Material Request': 'طلب مواد',
        'Item': 'صنف',
        'Customer': 'عميل',
        'Supplier': 'مورد',
        'Employee': 'موظف'
    }
    return labels.get(doctype, doctype)

def get_doctype_icon(doctype):
    """الحصول على أيقونة المستند"""
    icons = {
        'Sales Invoice': 'fa-file-text',
        'Purchase Invoice': 'fa-file-text-o',
        'POS Invoice': 'fa-shopping-cart',
        'Payment Entry': 'fa-money',
        'Journal Entry': 'fa-book',
        'Stock Entry': 'fa-cubes',
        'Delivery Note': 'fa-truck',
        'Purchase Receipt': 'fa-inbox',
        'Sales Order': 'fa-shopping-bag',
        'Purchase Order': 'fa-shopping-basket',
        'Quotation': 'fa-quote-left',
        'Material Request': 'fa-list-alt',
        'Item': 'fa-tag',
        'Customer': 'fa-user',
        'Supplier': 'fa-users',
        'Employee': 'fa-user-circle'
    }
    return icons.get(doctype, 'fa-file')

@frappe.whitelist()
def get_document_templates(doctype):
    """
    الحصول على قوالب المستندات المحفوظة
    """
    try:
        # البحث عن قوالب محفوظة
        templates = frappe.get_all(
            'Document Template',
            filters={'doctype': doctype},
            fields=['name', 'template_name', 'template_data']
        )
        
        return templates
        
    except:
        return []

@frappe.whitelist()
def save_document_template(doctype, template_name, template_data):
    """
    حفظ قالب مستند جديد
    """
    try:
        # إنشاء قالب جديد
        template = frappe.new_doc('Document Template')
        template.doctype = doctype
        template.template_name = template_name
        template.template_data = template_data
        template.insert()
        
        return {
            "status": "success",
            "message": _("تم حفظ القالب بنجاح")
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": _("حدث خطأ أثناء حفظ القالب: {0}").format(str(e))
        }
