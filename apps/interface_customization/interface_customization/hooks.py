app_name = "interface_customization"
app_title = "custom-interface"
app_publisher = "moneer"
app_description = "caتخصيص الواجهات"
app_email = "<EMAIL>"
app_license = "mit"

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "interface_customization",
# 		"logo": "/assets/interface_customization/logo.png",
# 		"title": "custom-interface",
# 		"route": "/interface_customization",
# 		"has_permission": "interface_customization.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
app_include_css = [
    # "/assets/interface_customization/css/sales_invoice_custom.css",
    "/assets/interface_customization/css/pos_invoice_custom.css",
    "/assets/interface_customization/css/universal_new_button.css",
    "/assets/interface_customization/css/cairo_theme_simple.css",
    # "/assets/interface_customization/css/theme-erpnext.css",
    # "/assets/interface_customization/css/custom_theme.css"
]
app_include_js = [
    "/assets/interface_customization/js/preload_optimizer.js",
    "/assets/interface_customization/js/universal_new_button.js",
    "/assets/interface_customization/js/cairo_theme_simple.js"
	# "/assets/interface_customization/js/custom_theme.js"
]

# تحسين تحميل الأيقونات
# Optimize icon loading
app_include_icons = "interface_customization/public/icons.svg"

# include js, css files in header of web template
# web_include_css = "/assets/interface_customization/css/interface_customization.css"
# web_include_js = "/assets/interface_customization/js/interface_customization.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "interface_customization/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
doctype_js = {
    "Sales Invoice" : "public/js/sales_invoice_custom.js",
    "POS Invoice" : "public/js/pos_invoice_custom.js"
}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "interface_customization/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "interface_customization.utils.jinja_methods",
# 	"filters": "interface_customization.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "interface_customization.install.before_install"
after_install = "interface_customization.install.after_install"

# Uninstallation
# ------------

before_uninstall = "interface_customization.install.before_uninstall"
# after_uninstall = "interface_customization.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "interface_customization.utils.before_app_install"
# after_app_install = "interface_customization.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "interface_customization.utils.before_app_uninstall"
# after_app_uninstall = "interface_customization.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "interface_customization.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

doc_events = {
	"Sales Invoice": {
		"before_save": "interface_customization.custom_interface.sales_invoice_customization.before_save",
		"on_submit": "interface_customization.custom_interface.sales_invoice_customization.on_submit_sales_invoice",
		"validate": "interface_customization.custom_interface.sales_invoice_customization.validate_sales_invoice_permissions"
	},
	"POS Invoice": {
		"before_save": "interface_customization.custom_interface.pos_invoice_customization.before_save_pos",
		"on_submit": "interface_customization.custom_interface.pos_invoice_customization.on_submit_pos_invoice",
		"validate": "interface_customization.custom_interface.pos_invoice_customization.validate_pos_invoice"
	}
}

# Scheduled Tasks
# ---------------

scheduler_events = {
	"cron": {
		"* * * * *": [
			"interface_customization.custom_interface.doctype.download_backup.download_backup.check_scheduled_backups"
		]
	},
	"daily": [
		"interface_customization.custom_interface.doctype.download_backup.download_backup.daily_backup_check"
	]
}

# Testing
# -------

# before_tests = "interface_customization.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "interface_customization.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "interface_customization.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["interface_customization.utils.before_request"]
# after_request = ["interface_customization.utils.after_request"]

# Job Events
# ----------
# before_job = ["interface_customization.utils.before_job"]
# after_job = ["interface_customization.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"interface_customization.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }

