{% extends "templates/web.html" %}

{% block title %}{{ _("إنشاء سريع") }}{% endblock %}

{% block head_include %}
<style>
    .quick-create-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        font-family: 'Cairo', sans-serif;
    }
    
    .page-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .page-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .doctypes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }
    
    .doctype-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }
    
    .doctype-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-color);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .doctype-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        border-color: var(--card-color);
    }
    
    .doctype-card:hover::before {
        transform: scaleX(1);
    }
    
    .doctype-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 24px;
        color: white;
        background: var(--card-color);
    }
    
    .doctype-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
    }
    
    .doctype-description {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .create-btn {
        background: var(--card-color);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        margin-top: 15px;
        transition: all 0.3s ease;
        opacity: 0;
        transform: translateY(10px);
    }
    
    .doctype-card:hover .create-btn {
        opacity: 1;
        transform: translateY(0);
    }
    
    .create-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .search-box {
        max-width: 500px;
        margin: 0 auto 30px;
        position: relative;
    }
    
    .search-input {
        width: 100%;
        padding: 15px 50px 15px 20px;
        border: 2px solid #e9ecef;
        border-radius: 25px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .search-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }
    
    @media (max-width: 768px) {
        .doctypes-grid {
            grid-template-columns: 1fr;
        }
        
        .page-header h1 {
            font-size: 2rem;
        }
        
        .doctype-card {
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block page_content %}
<div class="quick-create-container">
    <div class="page-header">
        <h1><i class="fa fa-plus-circle"></i> إنشاء سريع</h1>
        <p>اختر نوع المستند الذي تريد إنشاؤه</p>
    </div>
    
    <div class="search-box">
        <input type="text" class="search-input" placeholder="ابحث عن نوع المستند..." id="searchInput">
        <i class="fa fa-search search-icon"></i>
    </div>
    
    <div class="doctypes-grid" id="doctypesGrid">
        {% for doctype in doctypes %}
        <div class="doctype-card" 
             data-doctype="{{ doctype.name }}" 
             data-search="{{ doctype.label }} {{ doctype.description }}"
             style="--card-color: {{ doctype.color }}">
            <div class="doctype-icon">
                <i class="fa {{ doctype.icon }}"></i>
            </div>
            <div class="doctype-title">{{ doctype.label }}</div>
            <div class="doctype-description">{{ doctype.description }}</div>
            <button class="create-btn" onclick="createDocument('{{ doctype.name }}')">
                <i class="fa fa-plus"></i> إنشاء الآن
            </button>
        </div>
        {% endfor %}
    </div>
</div>

<script>
// البحث في المستندات
document.getElementById('searchInput').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const cards = document.querySelectorAll('.doctype-card');
    
    cards.forEach(card => {
        const searchData = card.getAttribute('data-search').toLowerCase();
        if (searchData.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

// إنشاء مستند جديد
function createDocument(doctype) {
    // إظهار رسالة تحميل
    frappe.show_alert({
        message: 'جاري إنشاء المستند...',
        indicator: 'blue'
    });
    
    // استدعاء API لإنشاء المستند
    frappe.call({
        method: 'interface_customization.custom_interface.universal_new_button.create_new_document',
        args: {
            doctype: doctype
        },
        callback: function(r) {
            if (r.message && r.message.status === 'success') {
                frappe.show_alert({
                    message: r.message.message,
                    indicator: 'green'
                });
                
                // الانتقال إلى المستند الجديد
                setTimeout(() => {
                    window.location.href = `/app/${doctype.toLowerCase().replace(' ', '-')}/${r.message.doc_name}`;
                }, 1000);
            } else {
                frappe.show_alert({
                    message: r.message ? r.message.message : 'حدث خطأ أثناء إنشاء المستند',
                    indicator: 'red'
                });
            }
        },
        error: function() {
            frappe.show_alert({
                message: 'حدث خطأ في الاتصال',
                indicator: 'red'
            });
        }
    });
}

// تأثيرات الحركة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.doctype-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// النقر على البطاقة لإنشاء المستند
document.querySelectorAll('.doctype-card').forEach(card => {
    card.addEventListener('click', function() {
        const doctype = this.getAttribute('data-doctype');
        createDocument(doctype);
    });
});
</script>
{% endblock %}
