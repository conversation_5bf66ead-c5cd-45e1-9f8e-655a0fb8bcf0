import frappe
from frappe import _

def get_context(context):
    """إعداد صفحة الإنشاء السريع"""
    
    context.title = _("إنشاء سريع")
    context.show_sidebar = False
    
    # الحصول على قائمة المستندات المتاحة
    context.doctypes = get_available_doctypes()
    
    # إعدادات الصفحة
    context.page_title = "إنشاء مستند جديد"
    context.meta_description = "صفحة الإنشاء السريع للمستندات"
    
    return context

def get_available_doctypes():
    """الحصول على قائمة المستندات المتاحة للإنشاء"""
    
    doctypes = [
        {
            'name': 'Sales Invoice',
            'label': 'فاتورة مبيعات',
            'icon': 'fa-file-text',
            'color': '#28a745',
            'description': 'إنشاء فاتورة مبيعات جديدة'
        },
        {
            'name': 'POS Invoice',
            'label': 'فاتورة نقاط البيع',
            'icon': 'fa-shopping-cart',
            'color': '#007bff',
            'description': 'إنشاء فاتورة نقاط بيع جديدة'
        },
        {
            'name': 'Purchase Invoice',
            'label': 'فاتورة مشتريات',
            'icon': 'fa-file-text-o',
            'color': '#dc3545',
            'description': 'إنشاء فاتورة مشتريات جديدة'
        },
        {
            'name': 'Payment Entry',
            'label': 'قيد دفع',
            'icon': 'fa-money',
            'color': '#ffc107',
            'description': 'إنشاء قيد دفع جديد'
        },
        {
            'name': 'Stock Entry',
            'label': 'قيد مخزون',
            'icon': 'fa-cubes',
            'color': '#6f42c1',
            'description': 'إنشاء قيد مخزون جديد'
        },
        {
            'name': 'Customer',
            'label': 'عميل جديد',
            'icon': 'fa-user',
            'color': '#20c997',
            'description': 'إضافة عميل جديد'
        },
        {
            'name': 'Item',
            'label': 'صنف جديد',
            'icon': 'fa-tag',
            'color': '#fd7e14',
            'description': 'إضافة صنف جديد'
        },
        {
            'name': 'Supplier',
            'label': 'مورد جديد',
            'icon': 'fa-users',
            'color': '#6c757d',
            'description': 'إضافة مورد جديد'
        }
    ]
    
    # تصفية المستندات حسب الصلاحيات
    available_doctypes = []
    for doctype in doctypes:
        if frappe.has_permission(doctype['name'], 'create'):
            available_doctypes.append(doctype)
    
    return available_doctypes
