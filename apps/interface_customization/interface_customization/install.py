# -*- coding: utf-8 -*-
"""
ملف التثبيت لتطبيق تخصيص الواجهات
Installation file for Interface Customization app
"""

import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields

def after_install():
    """
    تنفيذ بعد تثبيت التطبيق
    Execute after app installation
    """
    print("🚀 بدء تثبيت تطبيق تخصيص الواجهات...")
    
    # إنشاء الحقول المخصصة
    create_custom_fields_for_app()
    
    # تطبيق إعدادات الخصائص
    apply_property_setters()
    
    # إنشاء البيانات الافتراضية
    create_default_data()
    
    # تطبيق الصلاحيات
    setup_permissions()
    
    print("✅ تم تثبيت تطبيق تخصيص الواجهات بنجاح!")
    
    # عرض رسالة ترحيب
    show_welcome_message()

def create_custom_fields_for_app():
    """
    إنشاء الحقول المخصصة المطلوبة
    Create required custom fields
    """
    print("📝 إنشاء الحقول المخصصة...")
    
    custom_fields = {
        "Sales Invoice Item": [
            {
                "fieldname": "actual_qty",
                "label": "الكمية المتاحة",
                "fieldtype": "Float",
                "read_only": 1,
                "in_list_view": 1,
                "insert_after": "qty",
                "precision": 2,
                "description": "الكمية المتاحة في المخزون"
            },
            {
                "fieldname": "valuation_rate",
                "label": "معدل التقييم", 
                "fieldtype": "Currency",
                "read_only": 1,
                "insert_after": "actual_qty",
                "precision": 2,
                "description": "معدل تقييم الصنف في المخزون"
            }
        ],
        "Customer": [
            {
                "fieldname": "is_vip_customer",
                "label": "عميل مميز",
                "fieldtype": "Check",
                "default": 0,
                "insert_after": "customer_group",
                "description": "تحديد العميل كعميل مميز للحصول على خصومات خاصة"
            },
            {
                "fieldname": "sales_notes",
                "label": "ملاحظات المبيعات",
                "fieldtype": "Text",
                "insert_after": "is_vip_customer",
                "description": "ملاحظات خاصة بفريق المبيعات"
            }
        ],
        "Item": [
            {
                "fieldname": "sales_priority",
                "label": "أولوية المبيعات",
                "fieldtype": "Select",
                "options": "\nعالية\nمتوسطة\nمنخفضة",
                "default": "متوسطة",
                "insert_after": "item_group",
                "description": "أولوية الصنف في المبيعات"
            }
        ]
    }
    
    try:
        create_custom_fields(custom_fields)
        print("✅ تم إنشاء الحقول المخصصة بنجاح")
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحقول المخصصة: {str(e)}")

def apply_property_setters():
    """
    تطبيق إعدادات خصائص الحقول
    Apply field property setters
    """
    print("⚙️ تطبيق إعدادات الخصائص...")
    
    property_setters = [
        {
            "doctype": "Sales Invoice",
            "fieldname": "customer",
            "property": "reqd",
            "value": 1
        },
        {
            "doctype": "Sales Invoice",
            "fieldname": "posting_date",
            "property": "reqd", 
            "value": 1
        },
        {
            "doctype": "Sales Invoice Item",
            "fieldname": "cost_center",
            "property": "hidden",
            "value": 0
        },
        {
            "doctype": "Sales Invoice Item",
            "fieldname": "cost_center",
            "property": "in_list_view",
            "value": 1
        }
    ]
    
    for prop in property_setters:
        try:
            frappe.make_property_setter(prop)
        except Exception as e:
            print(f"تحذير: لم يتم تطبيق خاصية {prop['fieldname']}: {str(e)}")
    
    print("✅ تم تطبيق إعدادات الخصائص")

def create_default_data():
    """
    إنشاء البيانات الافتراضية
    Create default data
    """
    print("📊 إنشاء البيانات الافتراضية...")
    
    # إنشاء مجموعة عملاء VIP إذا لم تكن موجودة
    if not frappe.db.exists("Customer Group", "VIP"):
        vip_group = frappe.get_doc({
            "doctype": "Customer Group",
            "customer_group_name": "VIP",
            "parent_customer_group": "All Customer Groups",
            "is_group": 0
        })
        vip_group.insert(ignore_permissions=True)
        print("✅ تم إنشاء مجموعة عملاء VIP")
    
    # إنشاء قالب ضرائب مبسط
    if not frappe.db.exists("Sales Taxes and Charges Template", "ضريبة القيمة المضافة - مبسط"):
        tax_template = frappe.get_doc({
            "doctype": "Sales Taxes and Charges Template",
            "title": "ضريبة القيمة المضافة - مبسط",
            "is_default": 1,
            "taxes": [
                {
                    "charge_type": "On Net Total",
                    "account_head": frappe.db.get_value("Account", {"account_type": "Tax", "is_group": 0}, "name"),
                    "description": "ضريبة القيمة المضافة 15%",
                    "rate": 15
                }
            ]
        })
        try:
            tax_template.insert(ignore_permissions=True)
            print("✅ تم إنشاء قالب الضرائب المبسط")
        except:
            print("تحذير: لم يتم إنشاء قالب الضرائب")

def setup_permissions():
    """
    إعداد الصلاحيات
    Setup permissions
    """
    print("🔐 إعداد الصلاحيات...")
    
    # صلاحيات موظف المبيعات
    sales_person_perms = [
        {
            "doctype": "Sales Invoice",
            "role": "Sales User",
            "permlevel": 0,
            "read": 1,
            "write": 1,
            "create": 1,
            "submit": 1,
            "cancel": 0,
            "delete": 0
        }
    ]
    
    for perm in sales_person_perms:
        try:
            if not frappe.db.exists("Custom DocPerm", {
                "parent": perm["doctype"],
                "role": perm["role"],
                "permlevel": perm["permlevel"]
            }):
                frappe.get_doc({
                    "doctype": "Custom DocPerm",
                    **perm
                }).insert(ignore_permissions=True)
        except Exception as e:
            print(f"تحذير: لم يتم تطبيق صلاحية {perm['role']}: {str(e)}")
    
    print("✅ تم إعداد الصلاحيات")

def show_welcome_message():
    """
    عرض رسالة الترحيب
    Show welcome message
    """
    welcome_msg = """
    🎉 مرحباً بك في تطبيق تخصيص الواجهات!
    
    تم تثبيت التطبيق بنجاح وهو جاهز للاستخدام.
    
    المميزات الجديدة:
    ✅ واجهة فاتورة المبيعات المبسطة
    ✅ عرض معلومات المخزون التلقائي
    ✅ تسميات عربية واضحة
    ✅ تصميم محسن وسهل الاستخدام
    
    للبدء:
    1. انتقل إلى المبيعات > فاتورة المبيعات
    2. انقر على "جديد" لإنشاء فاتورة جديدة
    3. استمتع بالواجهة المبسطة!
    
    للدعم: <EMAIL>
    """
    
    print(welcome_msg)

def before_uninstall():
    """
    تنفيذ قبل إلغاء تثبيت التطبيق
    Execute before app uninstallation
    """
    print("🗑️ بدء إلغاء تثبيت تطبيق تخصيص الواجهات...")
    
    # حذف الحقول المخصصة
    remove_custom_fields()
    
    # حذف إعدادات الخصائص
    remove_property_setters()
    
    print("✅ تم إلغاء تثبيت التطبيق بنجاح")

def remove_custom_fields():
    """
    حذف الحقول المخصصة
    Remove custom fields
    """
    custom_fields = [
        "Sales Invoice Item-actual_qty",
        "Sales Invoice Item-valuation_rate", 
        "Customer-is_vip_customer",
        "Customer-sales_notes",
        "Item-sales_priority"
    ]
    
    for field in custom_fields:
        try:
            if frappe.db.exists("Custom Field", field):
                frappe.delete_doc("Custom Field", field)
        except Exception as e:
            print(f"تحذير: لم يتم حذف الحقل {field}: {str(e)}")

def remove_property_setters():
    """
    حذف إعدادات الخصائص
    Remove property setters
    """
    property_setters = frappe.get_all("Property Setter", 
        filters={"module": "Interface Customization"})
    
    for prop in property_setters:
        try:
            frappe.delete_doc("Property Setter", prop.name)
        except Exception as e:
            print(f"تحذير: لم يتم حذف إعداد الخاصية: {str(e)}")

def get_installation_status():
    """
    الحصول على حالة التثبيت
    Get installation status
    """
    status = {
        "installed": True,
        "version": "1.0.0",
        "custom_fields_created": False,
        "permissions_set": False,
        "default_data_created": False
    }
    
    # فحص الحقول المخصصة
    if frappe.db.exists("Custom Field", "Sales Invoice Item-actual_qty"):
        status["custom_fields_created"] = True
    
    # فحص البيانات الافتراضية
    if frappe.db.exists("Customer Group", "VIP"):
        status["default_data_created"] = True
    
    return status
