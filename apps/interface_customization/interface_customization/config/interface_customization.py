# -*- coding: utf-8 -*-
"""
تكوين تطبيق تخصيص الواجهات
Interface Customization App Configuration
"""

from frappe import _

def get_data():
    """
    إعدادات التطبيق في قائمة التطبيقات
    App configuration for the apps menu
    """
    return [
        {
            "label": _("تخصيص الواجهات"),
            "icon": "fa fa-cogs",
            "items": [
                {
                    "type": "doctype",
                    "name": "Sales Invoice",
                    "label": _("فاتورة المبيعات المبسطة"),
                    "description": _("واجهة مبسطة لإنشاء فواتير المبيعات"),
                    "onboard": 1,
                },
                {
                    "type": "page",
                    "name": "interface-settings",
                    "label": _("إعدادات الواجهة"),
                    "description": _("تخصيص إعدادات الواجهة"),
                },
                {
                    "type": "report",
                    "name": "Sales Invoice Summary",
                    "label": _("ملخص فواتير المبيعات"),
                    "description": _("تقرير مبسط لفواتير المبيعات"),
                    "is_query_report": True,
                }
            ]
        },
        {
            "label": _("التقارير"),
            "icon": "fa fa-chart-bar",
            "items": [
                {
                    "type": "report",
                    "name": "Customer Sales Summary",
                    "label": _("ملخص مبيعات العملاء"),
                    "description": _("تقرير مبيعات العملاء المبسط"),
                    "is_query_report": True,
                },
                {
                    "type": "report",
                    "name": "Item Stock Report",
                    "label": _("تقرير مخزون الأصناف"),
                    "description": _("تقرير مبسط لحالة المخزون"),
                    "is_query_report": True,
                }
            ]
        },
        {
            "label": _("الأدوات"),
            "icon": "fa fa-tools",
            "items": [
                {
                    "type": "page",
                    "name": "bulk-invoice-creator",
                    "label": _("منشئ الفواتير المجمعة"),
                    "description": _("إنشاء عدة فواتير دفعة واحدة"),
                },
                {
                    "type": "page",
                    "name": "customer-statement",
                    "label": _("كشف حساب العميل"),
                    "description": _("عرض كشف حساب مبسط للعميل"),
                }
            ]
        }
    ]

def get_desktop_icons():
    """
    أيقونات سطح المكتب
    Desktop icons configuration
    """
    return [
        {
            "module_name": "تخصيص الواجهات",
            "label": _("فاتورة مبيعات"),
            "link": "List/Sales Invoice",
            "type": "link",
            "icon": "fa fa-file-invoice",
            "color": "#4CAF50",
            "standard": 1,
            "_doctype": "Sales Invoice"
        },
        {
            "module_name": "تخصيص الواجهات", 
            "label": _("العملاء"),
            "link": "List/Customer",
            "type": "link",
            "icon": "fa fa-users",
            "color": "#2196F3",
            "standard": 1,
            "_doctype": "Customer"
        },
        {
            "module_name": "تخصيص الواجهات",
            "label": _("الأصناف"),
            "link": "List/Item",
            "type": "link", 
            "icon": "fa fa-cube",
            "color": "#FF9800",
            "standard": 1,
            "_doctype": "Item"
        }
    ]

def get_help_messages():
    """
    رسائل المساعدة
    Help messages for users
    """
    return [
        {
            "doctype": "Sales Invoice",
            "message": _("استخدم هذه الواجهة المبسطة لإنشاء فواتير المبيعات بسهولة. الحقول المعروضة هي الأساسية فقط."),
            "title": _("فاتورة المبيعات المبسطة")
        }
    ]

def get_user_permissions():
    """
    صلاحيات المستخدمين المخصصة
    Custom user permissions
    """
    return {
        "Sales Person": {
            "Sales Invoice": ["read", "write", "create"],
            "Customer": ["read"],
            "Item": ["read"]
        },
        "Sales Manager": {
            "Sales Invoice": ["read", "write", "create", "delete", "submit", "cancel"],
            "Customer": ["read", "write", "create"],
            "Item": ["read", "write"]
        }
    }

def get_notification_config():
    """
    إعدادات الإشعارات
    Notification configuration
    """
    return {
        "for_doctype": {
            "Sales Invoice": {
                "status": "Open",
                "filters": [
                    ["docstatus", "=", 1],
                    ["outstanding_amount", ">", 0]
                ]
            }
        }
    }

def get_dashboard_charts():
    """
    مخططات لوحة المعلومات
    Dashboard charts configuration
    """
    return [
        {
            "chart_name": _("مبيعات اليوم"),
            "chart_type": "Number Card",
            "filters_json": '{"posting_date": ["Today"]}',
            "source": "Sales Invoice",
            "function": "Sum",
            "aggregate_function_based_on": "grand_total",
            "is_public": 1,
            "owner": "Administrator"
        },
        {
            "chart_name": _("فواتير هذا الشهر"),
            "chart_type": "Line",
            "filters_json": '{"posting_date": ["This Month"]}',
            "source": "Sales Invoice", 
            "function": "Count",
            "is_public": 1,
            "owner": "Administrator"
        }
    ]

def get_workspace_sidebar_items():
    """
    عناصر الشريط الجانبي لمساحة العمل
    Workspace sidebar items
    """
    return [
        {
            "type": "Link",
            "link_type": "DocType",
            "link_to": "Sales Invoice",
            "label": _("فاتورة مبيعات جديدة"),
            "insert_after": 2
        },
        {
            "type": "Link", 
            "link_type": "Report",
            "link_to": "Sales Register",
            "label": _("سجل المبيعات"),
            "insert_after": 3
        }
    ]

def get_custom_fields():
    """
    الحقول المخصصة المطلوبة
    Required custom fields
    """
    return {
        "Sales Invoice Item": [
            {
                "fieldname": "actual_qty",
                "label": _("الكمية المتاحة"),
                "fieldtype": "Float",
                "read_only": 1,
                "in_list_view": 1,
                "insert_after": "qty"
            },
            {
                "fieldname": "valuation_rate", 
                "label": _("معدل التقييم"),
                "fieldtype": "Currency",
                "read_only": 1,
                "insert_after": "actual_qty"
            }
        ],
        "Customer": [
            {
                "fieldname": "is_vip_customer",
                "label": _("عميل مميز"),
                "fieldtype": "Check",
                "default": 0,
                "insert_after": "customer_group"
            }
        ]
    }

def get_property_setters():
    """
    إعدادات خصائص الحقول
    Field property setters
    """
    return [
        {
            "doctype": "Sales Invoice",
            "fieldname": "customer",
            "property": "reqd",
            "value": 1
        },
        {
            "doctype": "Sales Invoice",
            "fieldname": "posting_date", 
            "property": "reqd",
            "value": 1
        },
        {
            "doctype": "Sales Invoice Item",
            "fieldname": "cost_center",
            "property": "hidden",
            "value": 0
        }
    ]
