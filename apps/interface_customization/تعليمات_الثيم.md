# 📚 تعليمات الثيم المخصص - دليل شامل

## 🎯 نظرة عامة على الثيم

هذا الثيم المخصص يتكون من عدة ملفات تعمل معاً لتوفير تجربة بصرية جذابة ومتطورة لـ ERPNext.

---

## 📁 هيكل ملفات الثيم

```
apps/interface_customization/
├── interface_customization/
│   ├── hooks.py                           # ملف الربط الرئيسي
│   └── public/
│       ├── css/
│       │   ├── custom_theme.css           # ملف التنسيقات الرئيسي
│       │   ├── sales_invoice_custom.css   # تخصيص فواتير المبيعات
│       │   └── pos_invoice_custom.css     # تخصيص فواتير نقاط البيع
│       └── js/
│           ├── custom_theme.js            # ملف JavaScript للتفاعل
│           ├── sales_invoice_custom.js    # تخصيص فواتير المبيعات
│           └── pos_invoice_custom.js      # تخصيص فواتير نقاط البيع
```

---

## 🔗 ملف الربط الرئيسي (hooks.py)

### **الموقع**: `apps/interface_customization/interface_customization/hooks.py`

### **الوظيفة**:
ربط جميع ملفات CSS و JavaScript مع النظام

### **المحتوى المهم**:
```python
# ربط ملفات CSS
app_include_css = [
    "/assets/interface_customization/css/sales_invoice_custom.css",
    "/assets/interface_customization/css/pos_invoice_custom.css",
    "/assets/interface_customization/css/custom_theme.css"
]

# ربط ملفات JavaScript
app_include_js = [
    "/assets/interface_customization/js/custom_theme.js"
]
```

### **كيفية إضافة ملفات جديدة**:
1. أضف مسار الملف الجديد إلى القائمة المناسبة
2. احفظ الملف
3. شغل الأمر: `bench build --app interface_customization`

---

## 🎨 ملف التنسيقات الرئيسي (custom_theme.css)

### **الموقع**: `apps/interface_customization/interface_customization/public/css/custom_theme.css`

### **الوظيفة**:
يحتوي على جميع تنسيقات الثيم والألوان والتأثيرات

### **الأقسام الرئيسية**:

#### **1. متغيرات الألوان (السطور 7-32)**
```css
:root {
    /* الألوان الأساسية */
    --primary-sky: #4ecdc4;           /* اللون الأساسي */
    --primary-sky-dark: #26a69a;      /* اللون الداكن */
    --primary-sky-light: #80cbc4;     /* اللون الفاتح */
    --sidebar-bg: #4ecdc4;            /* لون القائمة الجانبية */

    /* ألوان الخلفية */
    --bg-main: linear-gradient(...);   /* خلفية الصفحة الرئيسية */
    --bg-card: rgba(255, 255, 255, 0.98); /* خلفية البطاقات */

    /* الظلال */
    --shadow-light: 0 2px 8px rgba(...);   /* ظل خفيف */
    --shadow-medium: 0 4px 16px rgba(...); /* ظل متوسط */
    --shadow-heavy: 0 8px 32px rgba(...);  /* ظل قوي */
}
```

#### **2. الخلفية الرئيسية (السطور 42-60)**
- تحديد خلفية الصفحة الرئيسية
- إصلاح مشاكل الخلفية البيضاء

#### **3. الشريط العلوي (السطور 70-94)**
- تنسيق شريط التنقل العلوي
- تأثيرات الشفافية والحركة

#### **4. القائمة الجانبية (السطور 96-238)**
- **الأهم**: تصميم المديولات في مربعات
- ألوان وتأثيرات القائمة الجانبية
- تحسينات التمرير

#### **5. البطاقات والكروت (السطور 250-280)**
- تنسيق البطاقات والعناصر
- تأثيرات الحركة والظلال

#### **6. الأزرار (السطور 290-350)**
- ألوان وتأثيرات الأزرار
- تأثير الموجة عند النقر

#### **7. النماذج والحقول (السطور 360-400)**
- تنسيق حقول الإدخال
- ألوان التركيز والحالات

#### **8. إصلاحات الـ workspace (السطور 917-1053)**
- **مهم جداً**: إصلاح مشاكل عرض صفحات المديولات
- تنسيق الاختصارات والبطاقات

---

## ⚡ ملف JavaScript للتفاعل (custom_theme.js)

### **الموقع**: `apps/interface_customization/interface_customization/public/js/custom_theme.js`

### **الوظيفة**:
إضافة تأثيرات تفاعلية وحركة للثيم

### **الدوال الرئيسية**:

#### **1. إعدادات الثيم (السطور 8-15)**
```javascript
const THEME_CONFIG = {
    ENABLE_ANIMATIONS: true,        // تفعيل التأثيرات
    ENABLE_SMOOTH_SCROLL: true,     // تمرير سلس
    DYNAMIC_COLORS: true,           // ألوان ديناميكية
    OPTIMIZE_PERFORMANCE: true      // تحسين الأداء
};
```

#### **2. دالة تطبيق التحسينات (السطر 30)**
```javascript
function applyThemeEnhancements() {
    enhanceNavbar();      // تحسين الشريط العلوي
    enhanceSidebar();     // تحسين القائمة الجانبية
    enhanceCards();       // تحسين البطاقات
    enhanceButtons();     // تحسين الأزرار
    enhanceForms();       // تحسين النماذج
    enhanceTables();      // تحسين الجداول
}
```

#### **3. دالة تأثير الموجة (السطر 120)**
```javascript
function addRippleEffect(button, event) {
    // إضافة تأثير الموجة للأزرار عند النقر
}
```

#### **4. دالة تغيير الألوان (السطر 280)**
```javascript
function changeThemeColor(newColor) {
    // تغيير لون الثيم ديناميكياً
}
```

---

## 🎨 كيفية تعديل الألوان

### **الطريقة الأولى: تعديل متغيرات CSS**

**الملف**: `custom_theme.css` (السطور 7-32)

```css
:root {
    /* غيّر هذه الألوان حسب تفضيلك */
    --primary-sky: #4ecdc4;        /* اللون الأساسي للثيم */
    --sidebar-bg: #4ecdc4;         /* لون القائمة الجانبية */
    --primary-sky-dark: #26a69a;   /* اللون الداكن */
    --primary-sky-light: #80cbc4;  /* اللون الفاتح */
}
```

### **الطريقة الثانية: استخدام JavaScript**

```javascript
// في console المتصفح أو في ملف JS
window.customTheme.changeColor('#ff5722'); // برتقالي
window.customTheme.changeColor('#4caf50'); // أخضر
window.customTheme.changeColor('#9c27b0'); // بنفسجي
```

### **ألوان مقترحة**:
- **أزرق**: `#2196f3`
- **أخضر**: `#4caf50`
- **برتقالي**: `#ff9800`
- **بنفسجي**: `#9c27b0`
- **وردي**: `#e91e63`

---

## 🔧 كيفية تعديل القائمة الجانبية

### **تغيير لون الخلفية**:
**الملف**: `custom_theme.css` (السطر 16)
```css
--sidebar-bg: #YOUR_COLOR; /* ضع اللون المطلوب */
```

### **تغيير حجم المربعات**:
**الملف**: `custom_theme.css` (السطور 115-123)
```css
.desk-sidebar .sidebar-item {
    margin: 0.5rem 0.75rem !important;  /* المسافة بين المربعات */
    padding: 1rem 1.25rem !important;   /* حجم المربع من الداخل */
}
```

### **تغيير عرض القائمة الجانبية**:
**الملف**: `custom_theme.css` (السطور 101-103)
```css
.desk-sidebar {
    width: 260px !important;      /* العرض المطلوب */
    min-width: 260px !important;
    max-width: 260px !important;
}
```

---

## 🎯 كيفية تعديل تأثيرات الحركة

### **تفعيل/إلغاء التأثيرات**:
**الملف**: `custom_theme.js` (السطور 8-15)
```javascript
const THEME_CONFIG = {
    ENABLE_ANIMATIONS: true,    // true = تفعيل، false = إلغاء
    ENABLE_SMOOTH_SCROLL: true, // تمرير سلس
    DYNAMIC_COLORS: true,       // ألوان ديناميكية
};
```

### **تعديل سرعة التأثيرات**:
**الملف**: `custom_theme.css` (في أي عنصر)
```css
transition: all 0.3s ease !important; /* 0.3s = السرعة */
```

---

## 📊 كيفية إضافة تحسينات جديدة

### **إضافة تنسيق CSS جديد**:
1. افتح `custom_theme.css`
2. أضف التنسيق في نهاية الملف:
```css
/* تحسين جديد */
.my-custom-element {
    background: var(--primary-sky) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
}
```

### **إضافة دالة JavaScript جديدة**:
1. افتح `custom_theme.js`
2. أضف الدالة قبل السطر الأخير:
```javascript
function myCustomFunction() {
    // الكود الخاص بك
    console.log('دالة مخصصة جديدة');
}
```

---

## 🚀 خطوات تطبيق التعديلات

### **بعد أي تعديل**:
1. احفظ الملف المعدل
2. شغل الأمر: `bench build --app interface_customization`
3. شغل الأمر: `bench --site site1.local clear-cache`
4. أعد تحميل الصفحة في المتصفح (Ctrl+F5)

### **إذا لم تظهر التغييرات**:
1. تأكد من حفظ الملف
2. تحقق من عدم وجود أخطاء في console المتصفح
3. جرب إعادة تشغيل الخادم: `bench restart`

---

## 🔍 استكشاف الأخطاء

### **المشكلة: الثيم لا يظهر**
**الحل**:
1. تحقق من `hooks.py` - تأكد من وجود مسارات الملفات
2. شغل `bench build --app interface_customization`
3. امسح الذاكرة المؤقتة

### **المشكلة: الألوان لا تتغير**
**الحل**:
1. تأكد من استخدام `!important` في CSS
2. امسح ذاكرة المتصفح المؤقتة
3. تحقق من متغيرات CSS في `:root`

### **المشكلة: التأثيرات بطيئة**
**الحل**:
1. قلل من قيم `transition` في CSS
2. فعّل `OPTIMIZE_PERFORMANCE` في JavaScript
3. قلل من التأثيرات المعقدة

---

## 📝 ملاحظات مهمة

### **ترتيب الأولوية**:
1. `custom_theme.css` - الأولوية العليا
2. ملفات CSS الأخرى
3. CSS الافتراضي لـ ERPNext

### **استخدام المتغيرات**:
- استخدم دائماً `var(--primary-sky)` بدلاً من الألوان المباشرة
- هذا يجعل التعديل أسهل ومتناسق

### **الأداء**:
- تجنب استخدام `!important` إلا عند الضرورة
- استخدم `will-change` للعناصر المتحركة
- قلل من التأثيرات المعقدة على الشاشات الصغيرة

---

## 🎉 خلاصة

هذا الثيم يوفر:
- ✅ تصميم عصري وجذاب
- ✅ ألوان قابلة للتخصيص
- ✅ تأثيرات تفاعلية
- ✅ أداء محسن
- ✅ تجاوب مع جميع الشاشات

**للدعم**: راجع هذا الملف عند الحاجة لأي تعديل أو تخصيص! 🚀

---

## 🔬 تفاصيل تقنية متقدمة

### **كيفية عمل الربط بين الملفات**:

#### **1. hooks.py → النظام**
```python
# عندما يبدأ ERPNext، يقرأ هذا الملف
app_include_css = [...]  # يحمل ملفات CSS
app_include_js = [...]   # يحمل ملفات JavaScript

# الترتيب مهم! الملف الأخير له الأولوية العليا
```

#### **2. CSS → HTML**
```css
/* المتغيرات تُعرّف مرة واحدة */
:root { --primary-sky: #4ecdc4; }

/* ثم تُستخدم في كل مكان */
.sidebar { background: var(--primary-sky); }
```

#### **3. JavaScript → CSS**
```javascript
// JavaScript يمكنه تغيير CSS
document.documentElement.style.setProperty('--primary-sky', '#ff5722');
```

### **دورة حياة التحميل**:
1. **ERPNext يبدأ** → يقرأ `hooks.py`
2. **يحمل CSS** → يطبق التنسيقات
3. **يحمل JavaScript** → يضيف التفاعل
4. **الصفحة جاهزة** → المستخدم يرى النتيجة

---

## 🎛️ إعدادات متقدمة للتخصيص

### **تخصيص الألوان حسب الوحدة**:
```css
/* ألوان مختلفة لكل وحدة */
.desk-sidebar .sidebar-item[data-module="Accounts"] {
    background: linear-gradient(135deg, #4caf50, #388e3c) !important;
}

.desk-sidebar .sidebar-item[data-module="Selling"] {
    background: linear-gradient(135deg, #2196f3, #1976d2) !important;
}

.desk-sidebar .sidebar-item[data-module="Buying"] {
    background: linear-gradient(135deg, #ff9800, #f57c00) !important;
}
```

### **تخصيص الثيم حسب الوقت**:
```javascript
// في custom_theme.js
function applyTimeBasedTheme() {
    const hour = new Date().getHours();

    if (hour >= 6 && hour < 18) {
        // نهاري - ألوان فاتحة
        changeThemeColor('#4ecdc4');
    } else {
        // ليلي - ألوان داكنة
        changeThemeColor('#263238');
    }
}

// تطبيق عند التحميل
$(document).ready(function() {
    applyTimeBasedTheme();
});
```

### **تخصيص الثيم حسب المستخدم**:
```javascript
// حفظ تفضيلات المستخدم
function saveUserThemePreference(color) {
    localStorage.setItem('user_theme_color', color);
}

function loadUserThemePreference() {
    const savedColor = localStorage.getItem('user_theme_color');
    if (savedColor) {
        changeThemeColor(savedColor);
    }
}
```

---

## 🧩 إضافة مكونات جديدة

### **إضافة زر تغيير الثيم**:
```javascript
// في custom_theme.js
function addThemeToggleButton() {
    const themeButton = $(`
        <div class="theme-toggle" style="position: fixed; top: 100px; right: 20px; z-index: 1000;">
            <button class="btn btn-primary" onclick="toggleTheme()">
                🎨 تغيير الثيم
            </button>
        </div>
    `);

    $('body').append(themeButton);
}

function toggleTheme() {
    const colors = ['#4ecdc4', '#2196f3', '#4caf50', '#ff9800', '#9c27b0'];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    changeThemeColor(randomColor);
}
```

### **إضافة مؤشر تحميل مخصص**:
```css
/* في custom_theme.css */
.custom-loader {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: var(--primary-sky);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.custom-loader::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
```

---

## 📊 مراقبة الأداء والتحسين

### **قياس أداء الثيم**:
```javascript
// في custom_theme.js
function measureThemePerformance() {
    const startTime = performance.now();

    applyThemeEnhancements();

    const endTime = performance.now();
    console.log(`⏱️ وقت تطبيق الثيم: ${endTime - startTime} ميلي ثانية`);
}
```

### **تحسين الأداء للشاشات الصغيرة**:
```css
/* تقليل التأثيرات على الشاشات الصغيرة */
@media (max-width: 768px) {
    * {
        transition: none !important;
        animation: none !important;
    }

    .desk-sidebar .sidebar-item:hover {
        transform: none !important;
    }
}
```

### **تحسين استهلاك الذاكرة**:
```javascript
// تنظيف العناصر غير المستخدمة
function cleanupUnusedElements() {
    // إزالة العناصر المخفية
    $('.hidden, [style*="display: none"]').remove();

    // تنظيف event listeners القديمة
    $('.old-element').off();
}

// تشغيل التنظيف كل 5 دقائق
setInterval(cleanupUnusedElements, 300000);
```

---

## 🔐 أمان وأفضل الممارسات

### **تجنب تضارب CSS**:
```css
/* استخدم أسماء فريدة للكلاسات */
.custom-theme-sidebar { /* بدلاً من .sidebar */ }
.custom-theme-button { /* بدلاً من .button */ }

/* استخدم !important بحذر */
.important-style {
    color: red !important; /* فقط عند الضرورة */
}
```

### **حماية JavaScript**:
```javascript
// تجنب تلوث النطاق العام
(function() {
    'use strict';

    // كل الكود هنا محمي
    function privateFunction() {
        // لا يمكن الوصول إليها من الخارج
    }

    // فقط ما تريد تصديره
    window.customTheme = {
        changeColor: changeThemeColor
    };
})();
```

### **التعامل مع الأخطاء**:
```javascript
function safeApplyTheme() {
    try {
        applyThemeEnhancements();
        console.log('✅ تم تطبيق الثيم بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تطبيق الثيم:', error);
        // تطبيق ثيم افتراضي آمن
        applyFallbackTheme();
    }
}
```

---

## 🔄 التحديث والصيانة

### **إضافة رقم إصدار**:
```javascript
// في بداية custom_theme.js
const THEME_VERSION = '2.1.0';
console.log(`🎨 إصدار الثيم: ${THEME_VERSION}`);
```

### **تسجيل التغييرات**:
```javascript
// سجل التغييرات
const CHANGELOG = {
    '2.1.0': 'إضافة تصميم المربعات للقائمة الجانبية',
    '2.0.0': 'تحسين الألوان والأداء',
    '1.0.0': 'الإصدار الأول'
};
```

### **التحقق من التوافق**:
```javascript
function checkCompatibility() {
    const erpnextVersion = frappe.boot.versions.frappe;
    const supportedVersions = ['13.0.0', '14.0.0', '15.0.0'];

    if (!supportedVersions.some(v => erpnextVersion.startsWith(v))) {
        console.warn('⚠️ قد لا يكون الثيم متوافق مع هذا الإصدار');
    }
}
```

---

## 🎓 نصائح للمطورين المتقدمين

### **استخدام CSS Grid للتخطيط**:
```css
.custom-grid-layout {
    display: grid;
    grid-template-columns: 260px 1fr;
    grid-template-rows: auto 1fr;
    grid-template-areas:
        "sidebar header"
        "sidebar main";
    height: 100vh;
}

.desk-sidebar { grid-area: sidebar; }
.layout-main { grid-area: main; }
```

### **استخدام CSS Custom Properties المتقدمة**:
```css
:root {
    /* ألوان ديناميكية */
    --hue: 180;
    --saturation: 50%;
    --lightness: 60%;

    --primary: hsl(var(--hue), var(--saturation), var(--lightness));
    --primary-dark: hsl(var(--hue), var(--saturation), calc(var(--lightness) - 20%));
    --primary-light: hsl(var(--hue), var(--saturation), calc(var(--lightness) + 20%));
}
```

### **استخدام Intersection Observer للأداء**:
```javascript
// تطبيق التأثيرات فقط عند ظهور العنصر
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');
        }
    });
});

document.querySelectorAll('.animate-on-scroll').forEach(el => {
    observer.observe(el);
});
```

---

## 📚 مراجع ومصادر إضافية

### **مراجع CSS**:
- [MDN CSS Reference](https://developer.mozilla.org/en-US/docs/Web/CSS)
- [CSS Grid Guide](https://css-tricks.com/snippets/css/complete-guide-grid/)
- [Flexbox Guide](https://css-tricks.com/snippets/css/a-guide-to-flexbox/)

### **مراجع JavaScript**:
- [MDN JavaScript Reference](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
- [jQuery Documentation](https://api.jquery.com/)

### **مراجع ERPNext**:
- [ERPNext Developer Guide](https://frappeframework.com/docs/user/en/guides)
- [Frappe Framework Docs](https://frappeframework.com/docs/user/en)

---

## 🎯 الخلاصة النهائية

هذا الدليل يغطي:
- ✅ **البنية الأساسية** للثيم
- ✅ **كيفية التعديل** والتخصيص
- ✅ **الممارسات الأفضل** للتطوير
- ✅ **حل المشاكل** الشائعة
- ✅ **التحسينات المتقدمة** للأداء

**تذكر**: احتفظ بنسخة احتياطية قبل أي تعديل كبير! 💾

**للمساعدة**: راجع هذا الدليل أو تحقق من console المتصفح للأخطاء. 🔍
