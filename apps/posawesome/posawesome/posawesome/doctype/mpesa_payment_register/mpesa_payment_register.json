{"actions": [], "allow_copy": 1, "autoname": "MPRE.-.YY.-.MM.-.######", "creation": "2021-11-10 03:47:20.144127", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["full_name", "transactiontype", "transid", "transtime", "transamount", "businessshortcode", "billrefnumber", "invoicenumber", "orgaccountbalance", "thirdpartytransid", "msisdn", "firstname", "middlename", "lastname", "column_break_14", "posting_date", "posting_time", "company", "default_currency", "customer", "mode_of_payment", "currency", "submit_payment", "payment_entry", "amended_from"], "fields": [{"fieldname": "transactiontype", "fieldtype": "Data", "label": "Transaction Type", "no_copy": 1, "read_only": 1}, {"fieldname": "transid", "fieldtype": "Data", "label": "Trans ID", "no_copy": 1, "read_only": 1}, {"fieldname": "transtime", "fieldtype": "Data", "label": "Trans Time", "no_copy": 1, "read_only": 1}, {"fieldname": "transamount", "fieldtype": "Float", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Trans Amount", "no_copy": 1, "read_only": 1}, {"fieldname": "businessshortcode", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Business Short Code", "no_copy": 1, "read_only": 1}, {"fieldname": "billrefnumber", "fieldtype": "Data", "label": "<PERSON>", "no_copy": 1, "read_only": 1}, {"fieldname": "invoicenumber", "fieldtype": "Data", "label": "Invoice Number", "no_copy": 1, "read_only": 1}, {"fieldname": "orgaccountbalance", "fieldtype": "Data", "label": "Org Account Ba<PERSON>", "no_copy": 1, "read_only": 1}, {"fieldname": "thirdpartytransid", "fieldtype": "Data", "label": "Third Party Trans ID", "no_copy": 1, "read_only": 1}, {"fieldname": "msisdn", "fieldtype": "Data", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "MSISDN", "no_copy": 1, "options": "Phone", "read_only": 1}, {"fieldname": "firstname", "fieldtype": "Data", "in_preview": 1, "in_standard_filter": 1, "label": "First Name", "no_copy": 1, "read_only": 1}, {"fieldname": "middlename", "fieldtype": "Data", "label": "Middle Name", "no_copy": 1, "read_only": 1}, {"fieldname": "lastname", "fieldtype": "Data", "in_preview": 1, "in_standard_filter": 1, "label": "Last Name", "no_copy": 1, "read_only": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "customer", "fieldtype": "Link", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Customer", "options": "Customer"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "in_list_view": 1, "in_preview": 1, "in_standard_filter": 1, "label": "Posting Date", "no_copy": 1, "read_only": 1}, {"fieldname": "payment_entry", "fieldtype": "Link", "label": "Payment Entry", "options": "Payment Entry", "read_only": 1}, {"fetch_from": "company.default_currency", "fieldname": "default_currency", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Mpesa Payment Register", "print_hide": 1, "read_only": 1}, {"fieldname": "mode_of_payment", "fieldtype": "Link", "label": "Mode of Payment", "options": "Mode of Payment"}, {"default": "KES", "fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"fieldname": "full_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Full Name", "no_copy": 1, "read_only": 1}, {"default": "0", "fieldname": "submit_payment", "fieldtype": "Check", "label": "Submit Payment "}, {"default": "Now", "fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "no_copy": 1, "read_only": 1}], "in_create": 1, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2023-06-07 22:24:09.518620", "modified_by": "Administrator", "module": "POSAwesome", "name": "Mpesa Payment Register", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "submit": 1, "write": 1}], "show_preview_popup": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "customer", "track_changes": 1}