{"name": "posawesome", "scripts": {"dev": "vite", "prebuild": "npm install"}, "dependencies": {"@vuepic/vue-datepicker": "11.0.2", "@vueuse/core": "^13.4.0", "dexie": "^4.0.11", "lodash": "^4.17.21", "mitt": "^3.0.1", "socket.io-client": "^4.8.1", "vue": "^3.3.4", "vue-qrcode-reader": "^5.7.2", "vue-virtual-scroller": "^2.0.0-beta.8", "vuetify": "^3.7.5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"@eslint/js": "^9.16.0", "@vitejs/plugin-vue": "^5.2.3", "esbuild": "^0.25.5", "eslint": "^9.16.0", "eslint-plugin-vue": "^9.32.0", "eslint-plugin-vuetify": "^2.5.1", "globals": "^15.13.0", "vite": "^6.2.4", "vue": "^3.3.4"}}