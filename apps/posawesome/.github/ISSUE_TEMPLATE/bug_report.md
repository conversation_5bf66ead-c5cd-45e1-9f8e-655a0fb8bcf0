---
name: Bug report
about: Create a report to help us improve
title: "[BUG]: "
labels: ''
assignees: ''

---

<!--  Thanks for your time to make POS Awesome better with your feedback 👍

**IMPORTANT** Before reporting a bug:

A properly detailed bug report can save a LOT of time and help fix issues as soon as possible.
- ->

### Versions

- Frappe: <!-- ex: V12.13.0 -->
- Erpnext: <!-- ex: V13.14.0 -->
- POS Awesome: <!-- ex: V01.0.2 -->

### Stacktrace / full error message

```javascript
(paste here)
```

### Steps to reproduce

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See the error

### What is Expected?


### What is actually happening?


### Additional context
Add any other context about the problem here.


### Screenshots
If applicable, could you add screenshots to help explain your problem?
