{"name": "@types/uglify-js", "version": "3.13.1", "description": "TypeScript definitions for UglifyJS", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/uglify-js", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/alan-agius4", "githubUsername": "alan-agius4"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uglify-js"}, "scripts": {}, "dependencies": {"source-map": "^0.6.1"}, "typesPublisherContentHash": "bf88e1f7f9d4879a56f46a116ac78628ac22d5b1e66bf80ee92ebb562c11613d", "typeScriptVersion": "3.6"}