{"name": "@types/body-parser", "version": "1.19.2", "description": "TypeScript definitions for body-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/santialbo", "githubUsername": "santial<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/vilic", "githubUsername": "vilic"}, {"name": "<PERSON>", "url": "https://github.com/dreampulse", "githubUsername": "dreampulse"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/blendsdk", "githubUsername": "blendsdk"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tlaziuk", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jwalton", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/body-parser"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/node": "*"}, "typesPublisherContentHash": "ad069aa8b9e8a95f66df025de11975c773540e4071000abdb7db565579b013ee", "typeScriptVersion": "3.7"}