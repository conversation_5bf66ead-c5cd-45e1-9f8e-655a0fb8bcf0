{"name": "@types/webpack-sources", "version": "3.2.0", "description": "TypeScript definitions for webpack-sources", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-sources", "license": "MIT", "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud", "githubUsername": "e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/chrise<PERSON>tein", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-sources"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.7.3"}, "typesPublisherContentHash": "7e7ca0cf90f1bb589fc4b5ebc534a134ca1068088fc7e58500fc94d5634fbaf1", "typeScriptVersion": "3.6"}