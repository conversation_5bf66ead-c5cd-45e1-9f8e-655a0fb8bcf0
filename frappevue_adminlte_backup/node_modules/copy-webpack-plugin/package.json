{"name": "copy-webpack-plugin", "version": "5.1.2", "description": "Copy files && directories with webpack", "license": "MIT", "repository": "webpack-contrib/copy-webpack-plugin", "author": "<PERSON>", "homepage": "https://github.com/webpack-contrib/copy-webpack-plugin", "bugs": "https://github.com/webpack-contrib/copy-webpack-plugin/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "npm run build", "release": "standard-version", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dependencies": {"cacache": "^12.0.3", "find-cache-dir": "^2.1.0", "glob-parent": "^3.1.0", "globby": "^7.1.1", "is-glob": "^4.0.1", "loader-utils": "^1.2.3", "minimatch": "^3.0.4", "normalize-path": "^3.0.0", "p-limit": "^2.2.1", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "webpack-log": "^2.0.0"}, "devDependencies": {"@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.6", "@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/defaults": "^6.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^24.9.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.1", "del": "^4.1.1", "del-cli": "^1.1.0", "enhanced-resolve": "^4.1.1", "eslint": "^6.7.2", "eslint-config-prettier": "^6.7.0", "eslint-plugin-import": "^2.19.1", "husky": "^3.1.0", "is-gzip": "^2.0.0", "jest": "^24.9.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "memfs": "^3.0.1", "mkdirp": "^0.5.1", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "webpack": "^4.41.2"}, "keywords": ["webpack", "plugin", "transfer", "move", "copy"]}