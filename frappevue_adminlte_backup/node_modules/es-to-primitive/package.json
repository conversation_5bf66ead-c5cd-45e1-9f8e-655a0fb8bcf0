{"name": "es-to-primitive", "version": "1.2.1", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "description": "ECMAScript “ToPrimitive” algorithm. Provides ES5 and ES2015 versions.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "node --es-staging test", "coverage": "covert test/*.js", "coverage-quiet": "covert test/*.js --quiet", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-to-primitive.git"}, "keywords": ["primitive", "abstract", "ecmascript", "es5", "es6", "es2015", "toPrimitive", "coerce", "type", "object", "string", "number", "boolean", "symbol", "null", "undefined"], "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.0", "covert": "^1.1.1", "eslint": "^6.6.0", "foreach": "^2.0.5", "function.prototype.name": "^1.1.1", "has-symbols": "^1.0.0", "object-inspect": "^1.6.0", "object-is": "^1.0.1", "replace": "^1.1.1", "semver": "^6.3.0", "tape": "^4.11.0"}, "testling": {"files": "test", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}}