{"name": "eslint-scope", "description": "ECMAScript scope analyzer for ESLint", "homepage": "http://github.com/eslint/eslint-scope", "main": "lib/index.js", "version": "4.0.3", "engines": {"node": ">=4.0.0"}, "repository": "eslint/eslint-scope", "bugs": {"url": "https://github.com/eslint/eslint-scope/issues"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "node Makefile.js test", "lint": "node Makefile.js lint", "generate-release": "eslint-generate-release", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "publish-release": "eslint-publish-release"}, "files": ["LICENSE", "README.md", "lib"], "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "eslint": "^3.15.0", "eslint-config-eslint": "^4.0.0", "eslint-release": "^1.0.0", "espree": "^3.1.1", "istanbul": "^0.4.5", "mocha": "^3.2.0", "npm-license": "^0.3.3", "shelljs": "^0.7.6", "typescript": "~2.0.10", "typescript-eslint-parser": "^1.0.0"}}