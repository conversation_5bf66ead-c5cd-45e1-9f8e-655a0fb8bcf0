{"name": "create-hash", "version": "1.2.0", "description": "create hashes for browserify", "browser": "browser.js", "main": "index.js", "scripts": {"standard": "standard", "test": "npm run-script standard && npm run-script unit", "unit": "node test.js | tspec"}, "repository": {"type": "git", "url": "**************:crypto-browserify/createHash.git"}, "keywords": ["crypto"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/crypto-browserify/createHash/issues"}, "homepage": "https://github.com/crypto-browserify/createHash", "devDependencies": {"hash-test-vectors": "^1.3.2", "safe-buffer": "^5.0.1", "standard": "^10.0.2", "tap-spec": "^2.1.2", "tape": "^4.6.3"}, "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}