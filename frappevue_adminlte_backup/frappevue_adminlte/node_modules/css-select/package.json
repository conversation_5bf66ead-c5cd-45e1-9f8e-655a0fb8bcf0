{"name": "css-select", "version": "4.1.3", "description": "a CSS selector compiler/engine", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/fb55"}, "keywords": ["css", "selector", "sizzle"], "repository": {"type": "git", "url": "git://github.com/fb55/css-select.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "devDependencies": {"@types/boolbase": "^1.0.0", "@types/jest": "^26.0.14", "@types/node": "^15.0.2", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^27.0.3", "prettier": "^2.1.2", "ts-jest": "^27.0.1", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4, "proseWrap": "always"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": ["<rootDir>/test/*.ts"]}}