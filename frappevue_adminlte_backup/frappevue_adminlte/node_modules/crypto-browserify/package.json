{"author": "<PERSON> <<EMAIL>> (dominictarr.com)", "name": "crypto-browserify", "description": "implementation of crypto for the browser", "version": "3.12.0", "homepage": "https://github.com/crypto-browserify/crypto-browserify", "repository": {"type": "git", "url": "git://github.com/crypto-browserify/crypto-browserify.git"}, "scripts": {"standard": "standard", "test": "npm run standard && npm run unit", "unit": "node test/", "browser": "zuul --browser-version $BROWSER_VERSION --browser-name $BROWSER_NAME -- test/index.js"}, "engines": {"node": "*"}, "dependencies": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}, "devDependencies": {"hash-test-vectors": "~1.3.2", "pseudorandombytes": "^2.0.0", "safe-buffer": "^5.1.1", "standard": "^5.0.2", "tape": "~2.3.2", "zuul": "^3.6.0"}, "optionalDependencies": {}, "browser": {"crypto": false}, "license": "MIT"}