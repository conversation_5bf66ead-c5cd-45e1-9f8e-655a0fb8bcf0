{"author": "<PERSON> <<EMAIL>> (http://feedic.com)", "name": "css-what", "description": "a CSS selector parser", "version": "5.1.0", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"url": "https://github.com/fb55/css-what"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib/**/*"], "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.2.4", "prettier": "^2.4.1", "ts-jest": "^27.0.5", "typescript": "^4.4.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest"}, "prettier": {"tabWidth": 4}}