module.exports = {
  addDays: require('./add_days/index.js'),
  addHours: require('./add_hours/index.js'),
  addISOYears: require('./add_iso_years/index.js'),
  addMilliseconds: require('./add_milliseconds/index.js'),
  addMinutes: require('./add_minutes/index.js'),
  addMonths: require('./add_months/index.js'),
  addQuarters: require('./add_quarters/index.js'),
  addSeconds: require('./add_seconds/index.js'),
  addWeeks: require('./add_weeks/index.js'),
  addYears: require('./add_years/index.js'),
  areRangesOverlapping: require('./are_ranges_overlapping/index.js'),
  closestIndexTo: require('./closest_index_to/index.js'),
  closestTo: require('./closest_to/index.js'),
  compareAsc: require('./compare_asc/index.js'),
  compareDesc: require('./compare_desc/index.js'),
  differenceInCalendarDays: require('./difference_in_calendar_days/index.js'),
  differenceInCalendarISOWeeks: require('./difference_in_calendar_iso_weeks/index.js'),
  differenceInCalendarISOYears: require('./difference_in_calendar_iso_years/index.js'),
  differenceInCalendarMonths: require('./difference_in_calendar_months/index.js'),
  differenceInCalendarQuarters: require('./difference_in_calendar_quarters/index.js'),
  differenceInCalendarWeeks: require('./difference_in_calendar_weeks/index.js'),
  differenceInCalendarYears: require('./difference_in_calendar_years/index.js'),
  differenceInDays: require('./difference_in_days/index.js'),
  differenceInHours: require('./difference_in_hours/index.js'),
  differenceInISOYears: require('./difference_in_iso_years/index.js'),
  differenceInMilliseconds: require('./difference_in_milliseconds/index.js'),
  differenceInMinutes: require('./difference_in_minutes/index.js'),
  differenceInMonths: require('./difference_in_months/index.js'),
  differenceInQuarters: require('./difference_in_quarters/index.js'),
  differenceInSeconds: require('./difference_in_seconds/index.js'),
  differenceInWeeks: require('./difference_in_weeks/index.js'),
  differenceInYears: require('./difference_in_years/index.js'),
  distanceInWords: require('./distance_in_words/index.js'),
  distanceInWordsStrict: require('./distance_in_words_strict/index.js'),
  distanceInWordsToNow: require('./distance_in_words_to_now/index.js'),
  eachDay: require('./each_day/index.js'),
  endOfDay: require('./end_of_day/index.js'),
  endOfHour: require('./end_of_hour/index.js'),
  endOfISOWeek: require('./end_of_iso_week/index.js'),
  endOfISOYear: require('./end_of_iso_year/index.js'),
  endOfMinute: require('./end_of_minute/index.js'),
  endOfMonth: require('./end_of_month/index.js'),
  endOfQuarter: require('./end_of_quarter/index.js'),
  endOfSecond: require('./end_of_second/index.js'),
  endOfToday: require('./end_of_today/index.js'),
  endOfTomorrow: require('./end_of_tomorrow/index.js'),
  endOfWeek: require('./end_of_week/index.js'),
  endOfYear: require('./end_of_year/index.js'),
  endOfYesterday: require('./end_of_yesterday/index.js'),
  format: require('./format/index.js'),
  getDate: require('./get_date/index.js'),
  getDay: require('./get_day/index.js'),
  getDayOfYear: require('./get_day_of_year/index.js'),
  getDaysInMonth: require('./get_days_in_month/index.js'),
  getDaysInYear: require('./get_days_in_year/index.js'),
  getHours: require('./get_hours/index.js'),
  getISODay: require('./get_iso_day/index.js'),
  getISOWeek: require('./get_iso_week/index.js'),
  getISOWeeksInYear: require('./get_iso_weeks_in_year/index.js'),
  getISOYear: require('./get_iso_year/index.js'),
  getMilliseconds: require('./get_milliseconds/index.js'),
  getMinutes: require('./get_minutes/index.js'),
  getMonth: require('./get_month/index.js'),
  getOverlappingDaysInRanges: require('./get_overlapping_days_in_ranges/index.js'),
  getQuarter: require('./get_quarter/index.js'),
  getSeconds: require('./get_seconds/index.js'),
  getTime: require('./get_time/index.js'),
  getYear: require('./get_year/index.js'),
  isAfter: require('./is_after/index.js'),
  isBefore: require('./is_before/index.js'),
  isDate: require('./is_date/index.js'),
  isEqual: require('./is_equal/index.js'),
  isFirstDayOfMonth: require('./is_first_day_of_month/index.js'),
  isFriday: require('./is_friday/index.js'),
  isFuture: require('./is_future/index.js'),
  isLastDayOfMonth: require('./is_last_day_of_month/index.js'),
  isLeapYear: require('./is_leap_year/index.js'),
  isMonday: require('./is_monday/index.js'),
  isPast: require('./is_past/index.js'),
  isSameDay: require('./is_same_day/index.js'),
  isSameHour: require('./is_same_hour/index.js'),
  isSameISOWeek: require('./is_same_iso_week/index.js'),
  isSameISOYear: require('./is_same_iso_year/index.js'),
  isSameMinute: require('./is_same_minute/index.js'),
  isSameMonth: require('./is_same_month/index.js'),
  isSameQuarter: require('./is_same_quarter/index.js'),
  isSameSecond: require('./is_same_second/index.js'),
  isSameWeek: require('./is_same_week/index.js'),
  isSameYear: require('./is_same_year/index.js'),
  isSaturday: require('./is_saturday/index.js'),
  isSunday: require('./is_sunday/index.js'),
  isThisHour: require('./is_this_hour/index.js'),
  isThisISOWeek: require('./is_this_iso_week/index.js'),
  isThisISOYear: require('./is_this_iso_year/index.js'),
  isThisMinute: require('./is_this_minute/index.js'),
  isThisMonth: require('./is_this_month/index.js'),
  isThisQuarter: require('./is_this_quarter/index.js'),
  isThisSecond: require('./is_this_second/index.js'),
  isThisWeek: require('./is_this_week/index.js'),
  isThisYear: require('./is_this_year/index.js'),
  isThursday: require('./is_thursday/index.js'),
  isToday: require('./is_today/index.js'),
  isTomorrow: require('./is_tomorrow/index.js'),
  isTuesday: require('./is_tuesday/index.js'),
  isValid: require('./is_valid/index.js'),
  isWednesday: require('./is_wednesday/index.js'),
  isWeekend: require('./is_weekend/index.js'),
  isWithinRange: require('./is_within_range/index.js'),
  isYesterday: require('./is_yesterday/index.js'),
  lastDayOfISOWeek: require('./last_day_of_iso_week/index.js'),
  lastDayOfISOYear: require('./last_day_of_iso_year/index.js'),
  lastDayOfMonth: require('./last_day_of_month/index.js'),
  lastDayOfQuarter: require('./last_day_of_quarter/index.js'),
  lastDayOfWeek: require('./last_day_of_week/index.js'),
  lastDayOfYear: require('./last_day_of_year/index.js'),
  max: require('./max/index.js'),
  min: require('./min/index.js'),
  parse: require('./parse/index.js'),
  setDate: require('./set_date/index.js'),
  setDay: require('./set_day/index.js'),
  setDayOfYear: require('./set_day_of_year/index.js'),
  setHours: require('./set_hours/index.js'),
  setISODay: require('./set_iso_day/index.js'),
  setISOWeek: require('./set_iso_week/index.js'),
  setISOYear: require('./set_iso_year/index.js'),
  setMilliseconds: require('./set_milliseconds/index.js'),
  setMinutes: require('./set_minutes/index.js'),
  setMonth: require('./set_month/index.js'),
  setQuarter: require('./set_quarter/index.js'),
  setSeconds: require('./set_seconds/index.js'),
  setYear: require('./set_year/index.js'),
  startOfDay: require('./start_of_day/index.js'),
  startOfHour: require('./start_of_hour/index.js'),
  startOfISOWeek: require('./start_of_iso_week/index.js'),
  startOfISOYear: require('./start_of_iso_year/index.js'),
  startOfMinute: require('./start_of_minute/index.js'),
  startOfMonth: require('./start_of_month/index.js'),
  startOfQuarter: require('./start_of_quarter/index.js'),
  startOfSecond: require('./start_of_second/index.js'),
  startOfToday: require('./start_of_today/index.js'),
  startOfTomorrow: require('./start_of_tomorrow/index.js'),
  startOfWeek: require('./start_of_week/index.js'),
  startOfYear: require('./start_of_year/index.js'),
  startOfYesterday: require('./start_of_yesterday/index.js'),
  subDays: require('./sub_days/index.js'),
  subHours: require('./sub_hours/index.js'),
  subISOYears: require('./sub_iso_years/index.js'),
  subMilliseconds: require('./sub_milliseconds/index.js'),
  subMinutes: require('./sub_minutes/index.js'),
  subMonths: require('./sub_months/index.js'),
  subQuarters: require('./sub_quarters/index.js'),
  subSeconds: require('./sub_seconds/index.js'),
  subWeeks: require('./sub_weeks/index.js'),
  subYears: require('./sub_years/index.js')
}
