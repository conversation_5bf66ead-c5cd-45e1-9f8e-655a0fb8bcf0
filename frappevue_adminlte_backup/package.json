{"name": "frappe_vue", "version": "0.1.1", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@leam-tech/renovation-core": "^1.2.4", "@vue/devtools": "^5.3.4", "core-js": "^3.6.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "serve": "^13.0.2", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vue-the-storages": "^1.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}